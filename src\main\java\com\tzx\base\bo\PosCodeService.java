package com.tzx.base.bo;

import net.sf.json.JSONObject;

import com.tzx.framework.common.constant.Code;

public interface PosCodeService
{
	String	NAME	= "com.tzx.base.bo.imp.PosCodeServiceImpl";
	
	String getCode(String tenantId, Code code, JSONObject pram) throws Exception;
	
	String getCodeValue(String tenantId,int store_id,int length,String prefix,String type)throws Exception;
	
	/**判断是否小于最大值,否则重置
	 * @param tenantId
	 * @param code
	 * @param param
	 * @param maxValue
	 * @return 是否重置
	 * @throws Exception
	 */
	boolean resetCode(String tenantId, Code code, JSONObject param,int maxValue) throws Exception;
}
