package com.tzx.clientorder.wechatprogram.bo.impl;

import java.sql.Timestamp;
import java.util.*;

import javax.annotation.Resource;

import com.tzx.clientorder.wechatprogram.common.constant.QimaiPaymentWayEnum;
import com.tzx.clientorder.wlifeprogram.common.util.WlifePromptUtil;
import com.tzx.clientorder.wlifeprogram.dao.BasicDao;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.tzx.base.entity.PaymentWay;
import com.tzx.base.entity.PosBill;
import com.tzx.base.entity.PosBillMember;
import com.tzx.base.entity.PosBillPayment;
import com.tzx.base.entity.PosBillPaymentCoupons;
import com.tzx.clientorder.wechatprogram.bo.AfterPaymentService;
import com.tzx.clientorder.wechatprogram.bo.PromptService;
import com.tzx.clientorder.wechatprogram.bo.dto.afterpay.OrderInfo;
import com.tzx.clientorder.wechatprogram.bo.dto.afterpay.PayOrderInfo;
import com.tzx.clientorder.wechatprogram.bo.dto.afterpay.PaymentInfo;
import com.tzx.clientorder.wechatprogram.common.constant.PromptConstant;
import com.tzx.clientorder.wechatprogram.common.constant.PromptMsgConstant;
import com.tzx.clientorder.wechatprogram.common.entity.pos.PosOrder;
import com.tzx.clientorder.wechatprogram.common.entity.pos.PosOrderAssist;
import com.tzx.clientorder.wechatprogram.common.entity.pos.PosOrderItem;
import com.tzx.clientorder.wechatprogram.common.entity.pos.PosOrderItemMethod;
import com.tzx.clientorder.wechatprogram.common.entity.program.ConsumeCouponsEntity;
import com.tzx.clientorder.wechatprogram.common.entity.program.MemberCouponsEntity;
import com.tzx.clientorder.wechatprogram.common.entity.program.MemberEntity;
import com.tzx.clientorder.wechatprogram.common.entity.program.OrderCookEntity;
import com.tzx.clientorder.wechatprogram.common.entity.program.OrderDishEntity;
import com.tzx.clientorder.wechatprogram.common.entity.program.OrderInfoEntity;
import com.tzx.clientorder.wechatprogram.common.entity.program.OrderSetmealEntity;
import com.tzx.clientorder.wechatprogram.common.entity.program.OrderSetmealItemEntity;
import com.tzx.clientorder.wechatprogram.common.entity.program.PayInfoEntity;
import com.tzx.clientorder.wechatprogram.common.entity.program.PayMemberEntity;
import com.tzx.clientorder.wechatprogram.common.util.PromptUtil;
import com.tzx.clientorder.wechatprogram.dao.AfterPaymentDao;
import com.tzx.framework.common.constant.Oper;
import com.tzx.framework.common.constant.SysParameterCode;
import com.tzx.framework.common.constant.Type;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.OtherSystemException;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.CommonUtil;
import com.tzx.framework.common.util.DateUtil;
import com.tzx.framework.common.util.DoubleHelper;
import com.tzx.framework.common.util.JsonUtils;
import com.tzx.framework.common.util.Md5Utils;
import com.tzx.member.acewill.bo.AcewillCustomerService;
import com.tzx.member.common.entity.CrmCardTradingListEntity;
import com.tzx.member.crm.bo.CustomerCardConsumeService;
import com.tzx.pos.base.Constant;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.util.ParamUtil;
import com.tzx.pos.bo.PosDiscountService;
import com.tzx.pos.bo.PosDishService;
import com.tzx.pos.bo.PosOpenTableService;
import com.tzx.pos.bo.PosPrintNewService;
import com.tzx.pos.bo.PosPrintService;
import com.tzx.pos.bo.payment.PosPaymentService;
import com.tzx.pos.po.springjdbc.dao.PosBillMemberDao;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import net.sf.json.JsonConfig;

@Service(AfterPaymentService.NAME)
public class AfterPaymentServiceImpl implements AfterPaymentService
{

	private static final Logger			LOG				= LoggerFactory.getLogger(AfterPaymentServiceImpl.class);

	public static final String			LOCK_POS_NUM	= "PROMPT";

	@Resource(name = PromptService.QIMAI_NAME)
	private PromptService				programService;

	@Resource(name = PosOpenTableService.NAME)
	private PosOpenTableService			openTableService;

	@Resource(name = PosDishService.NAME)
	private PosDishService				posDishService;

	@Resource(name = PosDiscountService.NAME)
	private PosDiscountService			posDiscountService;

	@Resource(name = PosPaymentService.NAME)
	private PosPaymentService			posPaymentService;

	@Resource(name = PosPrintNewService.NAME)
	private PosPrintNewService			posPrintNewService;

	@Resource(name = PosPrintService.NAME)
	private PosPrintService				posPrintService;

	@Resource(name = AcewillCustomerService.NAME)
	private AcewillCustomerService		acewillCustomerService;

	@Resource(name = CustomerCardConsumeService.NAME)
	private CustomerCardConsumeService	customerService;

	@Resource(name = AfterPaymentDao.NAME)
	private AfterPaymentDao				afterPaymentDao;

	@Resource(name = PosBillMemberDao.NAME)
	private PosBillMemberDao			memberDao;

	@Resource(name = BasicDao.NAME)
	private BasicDao		basicDao;


	private Boolean findPriceInUnitInfo(JSONObject item,int unitId, List<JSONObject> unit_infos, StringBuilder errBuild, StringBuilder errUpdateBuild, StringBuilder errPriceBuild) throws Exception
	{
		Boolean hasFound = false;
		Iterator<JSONObject> unitIt = unit_infos.iterator();
		JSONObject foundItem = null;
		while(unitIt.hasNext()){
			JSONObject unit = unitIt.next();
			if(unitId == unit.optInt("duid")){
				hasFound = true;
				foundItem = unit;
				break;
			}
		}
		if(!hasFound){
			errBuild.append("菜品").append(item.optString("dishName")).append("pos系统不存在;");
			if(errUpdateBuild.length() == 0) {
				errUpdateBuild.append("【").append(item.optString("dishName")).append("】");
			}
			else{
				errUpdateBuild.append("").append("【").append(item.optString("dishName")).append("】");
			}
		}
		else{
			hasFound = false;
			if(item.optDouble("dishPrice")!= foundItem.optDouble("price")){
				errBuild.append("菜品").append(item.optString("dishName")).append("小程序价格").append(item.optDouble("dishPrice"))
				.append(",pos系统小程序渠道价格").append(foundItem.optDouble("price")).append(",两边不一致;");
				if(errPriceBuild.length() == 0) {
					errPriceBuild.append("【").append(item.optString("dishName")).append("】");
				}
				else{
					errPriceBuild.append("").append("【").append(item.optString("dishName")).append("】");
				}
			}else{
				hasFound = true;
			}
		}
		return  hasFound;
	}
	/**
	 * 校验价格是否一致
	 * @param microLifeOrderInfo
	 * @throws Exception
	 */
	private void checkPrice(String tenancyId,Integer storeId,OrderInfoEntity microLifeOrderInfo) throws  Exception
	{
		JSONObject organJson = basicDao.getShopInfo(tenancyId, Integer.valueOf(storeId));
		String priceSystem = organJson.optString("price_system");

		Set<Integer> itemIdSet = new HashSet<Integer>();

		List<JSONObject> dishList = new ArrayList<>();
		// 套餐明细
		List<OrderSetmealEntity> setmealList = microLifeOrderInfo.getSetmeal();
		if (setmealList != null)
		{
			for (OrderSetmealEntity setmeal : setmealList)
			{
				JSONObject dishMain = new JSONObject();
				dishMain.put("dishName", setmeal.getDish_name()+"_"+setmeal.getUnit_name());
				dishMain.put("dishId", setmeal.getDishid());
				dishMain.put("unitId", setmeal.getUnitid());
				dishMain.put("dishPrice",setmeal.getPrice());
//				// 合并套餐主菜,辅菜
//				List<OrderSetmealItemEntity> setmealItemList = new ArrayList<OrderSetmealItemEntity>();
//				if (null != setmeal.getMaindish())
//				{
//					setmealItemList.addAll(setmeal.getMaindish());
//				}
//				if (null != setmeal.getMandatory())
//				{
//					setmealItemList.addAll(setmeal.getMandatory());
//				}
//				// 组织套餐明细数据
//				List<PosOrderItem> setmealOrderItemList = new ArrayList<PosOrderItem>();
//				for (OrderSetmealItemEntity item : setmealItemList)
//				{
//					JSONObject dishDetail = new JSONObject();
//					itemIdSet.add(item.getDishid());
//					dishDetail.put("dishName", item.getDish_name()+"_"+item.getUnit_name());
//					dishDetail.put("dishId", item.getDishid());
//					dishDetail.put("unitId", item.getUnitid());
//					dishList.add(dishDetail);
//				}
				itemIdSet.add(setmeal.getDishid());
				dishList.add(dishMain);
			}
		}

		// 单品
		List<OrderDishEntity> normalitemList = microLifeOrderInfo.getNormalitems();
		if (normalitemList != null)
		{
			for (OrderDishEntity item : normalitemList)
			{
				JSONObject dishMain = new JSONObject();
				dishMain.put("dishName", item.getDish_name()+"_"+item.getUnit_name());
				dishMain.put("dishId", item.getDishid());
				dishMain.put("unitId", item.getUnitid());
				dishMain.put("dishPrice",item.getPrice());
				itemIdSet.add(item.getDishid());
				dishList.add(dishMain);
			}
		}

		int[] item_ids = WlifePromptUtil.transferIntegerArray(itemIdSet);
		//根据item_id查询unit信息
		List<JSONObject> unit_infos = basicDao.getUnitInfos(tenancyId, item_ids, priceSystem);
		String errorMsg = "";
		StringBuilder errBuild = new StringBuilder();
		StringBuilder errUpdateBuild = new StringBuilder();
		StringBuilder errPriceBuild = new StringBuilder();
		Boolean noErr = true;
		//循环遍历校验价格是否一致
		for(JSONObject item : dishList){
		   if(!findPriceInUnitInfo(item,item.optInt("unitId"), unit_infos, errBuild, errUpdateBuild, errPriceBuild)){
			   noErr = false;
		   }
		}

		if(!noErr) {
			errorMsg = errBuild.toString();
			if("".equals(errorMsg)){
				errorMsg = "落单失败:价格校验不一致";
			}
			else{
				errorMsg = "落单失败:" + errorMsg;
			}
			LOG.error(errorMsg);
			errorMsg = "";
			if(errUpdateBuild.length() > 0){
				errorMsg = "当前所选菜品" + errUpdateBuild.toString() + "更新异常,";
			}
			if(errPriceBuild.length() > 0){
				errorMsg = errorMsg +  "当前所选菜品" + errPriceBuild.toString() + "价格异常,";
			}
			if("".equals(errorMsg)){
				errorMsg = "落单失败:价格校验不一致,";
			}
			errorMsg = errorMsg + "请联系门店工作人员处理。";
			throw new OtherSystemException(2007, errorMsg, null);
		}
	}
	@Override
	public JSONObject saveOrUpdateOrder(String tenancyId,Integer storeId,JSONObject param, JSONObject printJson, String channel) throws Exception
	{
		JSONObject responseJson = new JSONObject();
		OrderInfo microLifeOrder = (OrderInfo) JSONObject.toBean(param, getOrderJsonConfig());
		if (null == microLifeOrder || null == microLifeOrder.getOrder_info())
		{
			throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
		}

		// 获得订单信息
		OrderInfoEntity microLifeOrderInfo = microLifeOrder.getOrder_info();

		// 查询桌台状态
		JSONObject tableStatus = afterPaymentDao.getTableStatus(tenancyId, storeId, microLifeOrderInfo.getTableno());
		String errorMsg = null;
		if (null == tableStatus || tableStatus.isEmpty())
		{
			errorMsg = "落单失败:当前桌台不存在";
			LOG.error(errorMsg);
			throw SystemException.getInstance(errorMsg, PosErrorCode.NOT_EXISTS_TABLE_CODE);
		}
		
		boolean isLock = false;
		if(null!=microLifeOrderInfo.getMember())
		{
			isLock = isLockByOther(microLifeOrderInfo.getMember().getMobile(), tableStatus);
		}
		
		if (isLock)
		{
			errorMsg = "桌台已锁单";
			LOG.error(errorMsg);
			String name = "";
			String lockOptNum = tableStatus.getString("lock_opt_num");
			String lockPosNum = tableStatus.optString("lock_pos_num");
			if (LOCK_POS_NUM.equals(lockPosNum))
			{
				name = tableStatus.optString("opt_name");
			}
			else
			{
				name = afterPaymentDao.getEmpNameById(lockOptNum, tenancyId, storeId);
			}

			throw SystemException.getInstance(PosErrorCode.TABLE_ALREADY_LOCK_ERROR).set("{0}", name).set("{1}", lockPosNum);
		}

		//增加价格校验
		checkPrice(tenancyId, storeId, microLifeOrderInfo);

		List<PosOrderItem> defaultDishList = null;
		PosOrderAssist orderAssist;
		// 未锁台
		String state = tableStatus.getString("state");
		if (SysDictionary.TABLE_STATE_FREE.equals(state))
		{
			JSONObject posBill = afterPaymentDao.getBillInfoByOrderNum(tenancyId, storeId, microLifeOrder.getOrdernum());
			if (posBill != null)
			{
				throw new OtherSystemException(PosErrorCode.OPER_ERROR.getNumber(), "单号重复" + microLifeOrder.getOrdernum(), null);
			}
			//获取报表日期,班次
			orderAssist = createOrderAssist(tenancyId,storeId);
			
			// 未占用，开台
			JSONObject paramJson = this.getOpenTableParam(tenancyId, storeId, microLifeOrderInfo.getTableno(), microLifeOrderInfo.getOrdernum(),microLifeOrderInfo.getPeople(), orderAssist);
			
			String billNum = openTableService.openTableByTableCode(tenancyId, storeId, DateUtil.parseDate(orderAssist.getReport_date()), orderAssist.getShift_id(), paramJson);
			
			// 获取默认菜品
			defaultDishList = this.getDefaultDishList(tenancyId, storeId, microLifeOrderInfo.getTableno(), microLifeOrderInfo.getPeople());
			
			orderAssist.setBill_num(billNum);
			// 根据openid判断,
			List<JSONObject> holdObjs = afterPaymentDao.getBillLockOpenId(tenancyId, storeId, microLifeOrder.getTableno());
			if (null != holdObjs && 0 < holdObjs.size()) {
				afterPaymentDao.updateOpenId(orderAssist.getBill_num(), microLifeOrder.getOpenid());
			}
			else {
				afterPaymentDao.insertOpenId(tenancyId, storeId, orderAssist.getBill_num(), microLifeOrder.getOpenid());
			}
		}
		else if (SysDictionary.TABLE_STATE_BUSY.equals(state))
		{
			// 已占用，订单号
			JSONObject posBill = afterPaymentDao.getBillInfoByTableCode(tenancyId, storeId, microLifeOrderInfo.getTableno());
			
			//获取报表日期,班次
			orderAssist = createOrderAssist(tenancyId, storeId);
			orderAssist.setBill_num(posBill.optString("bill_num"));
			// 根据openid判断,
			List<JSONObject> holdObjs = afterPaymentDao.getBillLockOpenId(tenancyId, storeId, microLifeOrder.getTableno());
			if (null != holdObjs && 0 < holdObjs.size()) {
				afterPaymentDao.updateOpenId(orderAssist.getBill_num(), microLifeOrder.getOpenid());
			}
			else {
				afterPaymentDao.insertOpenId(tenancyId, storeId, orderAssist.getBill_num(), microLifeOrder.getOpenid());
			}
		}
		else
		{
			LOG.error("storeId={},tablecode={} status error", storeId, microLifeOrderInfo.getTableno());
			throw SystemException.getInstance(PosErrorCode.TABLE_STATE_EXCEPTION);
		}

		// 拼装下单所需参数
		Data orderParam = createOrderDishData(tenancyId,storeId,microLifeOrderInfo, orderAssist,defaultDishList, channel);
		// 下单
		Data newestOrderDish = posDishService.newestOrderDish(orderParam, printJson);

		// 更新OpenId
		if (newestOrderDish.getCode() == 0)
		{
//			// 会员信息
//			String sysParameter = CacheTableUtil.getSysParameter("CustomerType");
//			if (SysDictionary.CUSTOMER_TYPE_ACEWILL.equals(sysParameter) && null != microLifeOrderInfo.getMember())
//			{
//				this.savePosBillMember(tenancyId,storeId,microLifeOrderInfo.getMember(), orderAssist);
//				// 处理折扣
//				// 查询账单pos_bill，判断账单有没折扣
////				JSONObject member = order.optJSONObject("member");
//				JSONObject billObject = afterPaymentDao.getBillDiscount(tenancyId, storeId, microLifeOrderInfo.getTableno());
//				if (null == billObject)
//				{
//					LOG.error("进行折扣的门店为{}，桌号为:{}的账单不存在", storeId, microLifeOrderInfo.getTableno());
//				}
//				
//					setMemberDiscount(member, tenancyId, storeId, microLifeOrderInfo.getTableno(), billObject);
//			}
			
			// 更新posbill
//			if (SysDictionary.TABLE_STATE_FREE.equals(state))
			{
				if (null != orderAssist.getBill_num())
				{
					afterPaymentDao.updateOrderSource(tenancyId, orderAssist.getBill_num(), microLifeOrderInfo.getOrdernum(), channel);
				}
			}
			
			JSONObject billObject = afterPaymentDao.getBillInfo(tenancyId, storeId, microLifeOrderInfo.getTableno());
			
			JSONObject orderData = this.getOrderInfo(tenancyId, storeId, microLifeOrderInfo.getTableno(), billObject);
			LOG.info(" getOrderDetail data:" + orderData.toString());

			JSONObject dataJson = new JSONObject();
			dataJson.put("order_info", orderData);
			
			responseJson.put(PromptConstant.SUCCESS, 1);
			responseJson.put(PromptConstant.MSG, "SUCCESS");
			responseJson.put(PromptConstant.DATA, dataJson);
		}
		else
		{
			if (Constant.CODE_PARAM_FAILURE == newestOrderDish.getCode())
			{
				throw new OtherSystemException(PosErrorCode.GREAT_THAN_SOLD_OUT_COUNT.getNumber(), newestOrderDish.getMsg(), null);
			}
			else
			{
				throw new OtherSystemException(newestOrderDish.getCode(), newestOrderDish.getMsg(), null);
			}

		}
		return responseJson;
	}

	private JsonConfig getOrderJsonConfig()
	{
		Map<String, Class<?>> classMap = new HashMap<>();
		classMap.put("order_info", OrderInfoEntity.class);
		classMap.put("member", MemberEntity.class);
		classMap.put("setmeal", OrderSetmealEntity.class);
		classMap.put("normalitems", OrderDishEntity.class);
		classMap.put("maindish", OrderSetmealItemEntity.class);
		classMap.put("mandatory", OrderSetmealItemEntity.class);
		classMap.put("cooks", OrderCookEntity.class);

		return PromptUtil.getJsonConfig(OrderInfo.class, classMap);
	}
	
	private boolean isLockByOther(String mobile, JSONObject tableStatus)
	{
		boolean flag = false;
		// 查询桌台状态
		try
		{
			String state = tableStatus.optString("state");
			String lockOptNum = tableStatus.optString("lock_opt_num");
			if (SysDictionary.TABLE_STATE_BUSY.equals(state) && CommonUtil.hasText(lockOptNum))
			{
				if (CommonUtil.hasText(mobile) && !mobile.equals(lockOptNum))
				{
					flag = true;
				}
			}
		}
		catch (Exception e)
		{
			//e.printStackTrace();
		}
		return flag;
	}
	
	private JSONObject getOpenTableParam(String tenancyId,Integer storeId,String tableCode,String orderNum,Integer guest, PosOrderAssist orderAssist) throws Exception
	{
		JSONObject object = new JSONObject();
		object.put("mode", 0);
		object.put("report_date", orderAssist.getReport_date());
		object.put("shift_id", orderAssist.getShift_id());
		object.put("pos_num", orderAssist.getPos_num());
		object.put("opt_num", orderAssist.getOpt_num());
		object.put("waiter_num", orderAssist.getOpt_num());
		object.put("table_code", tableCode);
		object.put("preorderno", orderNum);
		object.put("guest", guest);
		object.put("sale_mode", SysDictionary.SALE_MODE_TS01);
		object.put("chanel", SysDictionary.CHANEL_WX02);
		object.put("item_menu_id", 0);
		object.put("copy_bill_num", "");
		object.put("remark", "");
		object.put("shop_real_amount", 0);
		object.put("platform_charge_amount", 0);
		object.put("settlement_type", "");
		object.put("discount_mode_id", 0);
		object.put("discountk_amount", 0);
		object.put("discount_rate", 0);
		
		return object;
	}
	
	private List<PosOrderItem> getDefaultDishList(String tenantId, Integer storeId,String tableCode, Integer guest) throws Exception
	{
		List<PosOrderItem> defaultDishList = null;
		
		List<JSONObject> itemList = openTableService.getDefaultDishList(tenantId, storeId, String.valueOf(guest),tableCode, null);
		if (null != itemList && 0 < itemList.size())
		{
			defaultDishList = new ArrayList<PosOrderItem>();
			for (JSONObject itemJson : itemList)
			{
				defaultDishList.add((PosOrderItem) JsonUtils.jsonToObject(itemJson, PosOrderItem.class));
			}
		}

		return defaultDishList;
	}

	/**
	 * 组织下单参数
	 * 
	 * @param microLifeOrder
	 * @param orderAssist
	 * @return
	 * @throws Exception
	 */
	private Data createOrderDishData(String tenantId, Integer storeId,OrderInfoEntity microLifeOrderInfo, PosOrderAssist orderAssist,List<PosOrderItem> defaultDishList, String channel) throws Exception
	{
		PosOrder order = createOrder(tenantId, storeId, microLifeOrderInfo, orderAssist, defaultDishList);
		order.setOrder_source(channel);
		JSONObject orderJsonObject = JSONObject.fromObject(order, getAcewillOrderJsonConfig());

		List<JSONObject> orderList = new ArrayList<>();
		orderList.add(orderJsonObject);

		Data data = new Data();
		data.setTenancy_id(tenantId);
		data.setStore_id(storeId);
		data.setType(Type.ORDERING);
		data.setOper(Oper.add);
		data.setData(orderList);
		return data;
	}

	private JsonConfig getAcewillOrderJsonConfig()
	{
		Map<String, Class<?>> classMap = new HashMap<>();
		classMap.put("item", PosOrderItem.class);
		classMap.put("method", PosOrderItemMethod.class);
		
		return PromptUtil.getJsonConfig(PosOrder.class, classMap);
	}

	private PosOrder createOrder(String tenantId, Integer storeId,OrderInfoEntity microLifeOrderInfo, PosOrderAssist orderAssist,List<PosOrderItem> defaultDishList) throws Exception
	{
		PosOrder acewillOrder = new PosOrder();
		// 账单号
		acewillOrder.setBill_num(orderAssist.getBill_num());
		// 桌号
		acewillOrder.setTable_code(microLifeOrderInfo.getTableno());
		// 是否厨打
		acewillOrder.setIsprint("Y");
		// 0:下单 1:
		acewillOrder.setMode(0);
		// 报表日期
		acewillOrder.setReport_date(orderAssist.getReport_date());
		// 销售模式
		// acewillOrder.setSale_mode(sale_mode);
		// 班次id
		acewillOrder.setShift_id(orderAssist.getShift_id());
		// 收款机编号
		acewillOrder.setPos_num(orderAssist.getPos_num());
		// 操作员编号
		acewillOrder.setOpt_num(orderAssist.getOpt_num());
		// 服务员号
		acewillOrder.setWaiter_num(null);
		// 整单备注
		acewillOrder.setBill_taste(PromptUtil.formatOrderMemo(microLifeOrderInfo.getOrdermemo()));
		
		// 菜品明细
		List<PosOrderItem> orderItems = new ArrayList<PosOrderItem>();
		acewillOrder.setItem(orderItems);
		// 点餐序号
		Integer item_serial = 0;
		if (null != defaultDishList && 0 < defaultDishList.size())
		{
			//添加默认菜品
			orderItems.addAll(defaultDishList);
			item_serial = defaultDishList.size();
		}
		else
		{
			item_serial = afterPaymentDao.getLastItemSerial(tenantId, orderAssist.getBill_num());
		}

		// 获取套餐档案
		Map<String, Map<String, JSONObject>> itemComboMap = this.getComboDetailMap(tenantId, microLifeOrderInfo.getSetmeal());
		
		// 套餐主项默认规格id不需要设置
		getSetmealUnitMap(tenantId, microLifeOrderInfo.getSetmeal());

		// 查询规格
		Map<String, String> unitNameMap = this.getUnitNameMap(tenantId,storeId,microLifeOrderInfo);

		// 套餐明细
		List<OrderSetmealEntity> setmealList = microLifeOrderInfo.getSetmeal();
		if (setmealList != null)
		{
			for (OrderSetmealEntity setmeal : setmealList)
			{
				// 点餐序号
				item_serial++;

				// 组织套餐主项数据
				String duName = unitNameMap.get(String.valueOf(setmeal.getUnitid()));
				setmeal.setUnit_name(duName);

				orderItems.add(this.createOrderItem(setmeal, item_serial));

				// 合并套餐主菜,辅菜
				List<OrderSetmealItemEntity> setmealItemList = new ArrayList<OrderSetmealItemEntity>();
				if (null != setmeal.getMaindish())
				{
					setmealItemList.addAll(setmeal.getMaindish());
				}
				if (null != setmeal.getMandatory())
				{
					setmealItemList.addAll(setmeal.getMandatory());
				}
				
				// 获取套餐明细信息
				Map<String, JSONObject> itemComboDetailMap = itemComboMap.get(String.valueOf(setmeal.getDishid()));
				
				// 组织套餐明细数据
				List<PosOrderItem> setmealOrderItemList = new ArrayList<PosOrderItem>();
				for (OrderSetmealItemEntity item : setmealItemList)
				{
					item.setNumber(setmeal.getNumber() * item.getNumber());
					item.setMallListName();// 设置套餐明细菜品名称
					String duItemName = unitNameMap.get(String.valueOf(item.getUnitid()));
					item.setUnit_name(duItemName);
					// 获取套餐明细信息
					JSONObject itemComboDetail = this.getComboDetailByDetailsId(itemComboDetailMap, item);

					PosOrderItem setmealOrderItem = this.createOrderItem(setmeal, item, item_serial, itemComboDetail);
					setmealOrderItemList.add(setmealOrderItem);
				}
				
				// 套餐明细排序
				orderItems.addAll(this.sortOrderSetmealItem(setmealOrderItemList));
			}
		}

		// 单品
		List<OrderDishEntity> normalitemList = microLifeOrderInfo.getNormalitems();
		if (normalitemList != null)
		{
			for (OrderDishEntity item : normalitemList)
			{
				item_serial++;
				String duName = unitNameMap.get(String.valueOf(item.getUnitid()));
				item.setUnit_name(duName);

				PosOrderItem setmealOrderItem = this.createOrderItem(item, item_serial);
				orderItems.add(setmealOrderItem);
			}
		}

		return acewillOrder;
	}

	/**
	 * 查询套餐主项默认规格
	 * 
	 * @param microLifeOrder
	 * @return
	 * @throws Exception
	 */
	private Map<String, String> getSetmealUnitMap(String tenentId,List<OrderSetmealEntity> list) throws Exception
	{
		List<String> itemIdList = new ArrayList<>();
		if (list != null)
		{
			for (OrderSetmealEntity setmeal : list)
			{
				itemIdList.add(String.valueOf(setmeal.getDishid()));
			}
		}
		// 查询默认规格
		Map<String, String> setmealUnitMap = new HashMap<String, String>();
		List<JSONObject> findItemUnit = afterPaymentDao.findItemUnit(tenentId, itemIdList);
		if (findItemUnit != null)
		{
			for (JSONObject jsonObject : findItemUnit)
			{
				setmealUnitMap.put(jsonObject.getString("item_id"), jsonObject.getString("id"));
			}
		}

		// 设置套餐主项菜品规格
		if (list != null)
		{
			for (OrderSetmealEntity setmeal : list)
			{
				String setmealUnitId = setmealUnitMap.get(String.valueOf(setmeal.getDishid()));
				if (!StringUtils.isEmpty(setmealUnitId))
				{
					setmeal.setUnitid(Integer.valueOf(setmealUnitId));
				}
			}
		}
		return setmealUnitMap;
	}

	/**
	 * 查询规格
	 * 
	 * @param microLifeOrder
	 * @return
	 * @throws Exception
	 */
	private Map<String, String> getUnitNameMap(String tenancyId,Integer storeId,OrderInfoEntity microLifeOrderInfo) throws Exception
	{
		List<String> duids = new ArrayList<>();
		List<OrderSetmealEntity> setmealList = microLifeOrderInfo.getSetmeal();
		if (setmealList != null)
		{
			for (OrderSetmealEntity setmeal : setmealList)
			{
				if (null != setmeal.getMaindish())
				{
					for (OrderSetmealItemEntity setmealItem : setmeal.getMaindish())
					{
						duids.add(String.valueOf(setmealItem.getUnitid()));
					}
				}
				
				if (null != setmeal.getMandatory())
				{
					for (OrderSetmealItemEntity setmealItem : setmeal.getMandatory())
					{
						duids.add(String.valueOf(setmealItem.getUnitid()));
					}
				}
				
				duids.add(String.valueOf(setmeal.getUnitid()));
			}
		}
		
		// 单品中的规格id
		List<OrderDishEntity> normalList = microLifeOrderInfo.getNormalitems();
		if (normalList != null)
		{
			for (OrderDishEntity normal : normalList)
			{
				duids.add(String.valueOf(normal.getUnitid()));
			}
		}

		List<JSONObject> list = afterPaymentDao.getUnitNameList(tenancyId, storeId, duids);
		Map<String, String> unitNameMap = new HashMap<>();
		if (null != list)
		{
			for (JSONObject jsonObject : list)
			{
				unitNameMap.put(jsonObject.getString("id"), jsonObject.getString("unit_name"));
			}
		}
		return unitNameMap;
	}

	/**
	 * 查询套餐明细
	 * 
	 * @param tenantId
	 * @param list
	 * @return
	 * @throws Exception
	 */
	private Map<String, Map<String, JSONObject>> getComboDetailMap(String tenantId, List<OrderSetmealEntity> list) throws Exception
	{
		List<Integer> itemIdList = new ArrayList<>();
		if (list != null)
		{
			for (OrderSetmealEntity setmeal : list)
			{
				itemIdList.add(setmeal.getDishid());
			}
		}

		// 查询套餐明细
		List<JSONObject> omboDetailsList = afterPaymentDao.getItemComboDetails(tenantId, itemIdList);

		Map<String, Map<String, JSONObject>> comboMap = new HashMap<String, Map<String, JSONObject>>();
		if (null != omboDetailsList)
		{
			for (JSONObject comboDetail : omboDetailsList)
			{
				String iItemId = comboDetail.optString("iitem_id");// 套餐主项ID
				String isItemGroup = comboDetail.optString("is_itemgroup");
				String detailsId = comboDetail.optString("details_id");
				String itemUnitId = comboDetail.optString("item_unit_id");

				Map<String, JSONObject> comboDetailMap = null;
				if (comboMap.containsKey(iItemId))
				{
					comboDetailMap = comboMap.get(iItemId);
				}
				else
				{
					comboDetailMap = new HashMap<String, JSONObject>();
				}

				if ("Y".equals(isItemGroup))
				{
					comboDetailMap.put(detailsId, comboDetail);
				}
				else
				{
					comboDetailMap.put(detailsId + "_" + itemUnitId, comboDetail);
				}

				comboMap.put(iItemId, comboDetailMap);
			}
		}
		return comboMap;
	}

	private JSONObject getComboDetailByDetailsId(Map<String, JSONObject> itemComboDetailMap, OrderSetmealItemEntity item) throws Exception
	{
		JSONObject itemComboDetail = null;
		if (null != itemComboDetailMap)
		{
			if (CommonUtil.hv(item.getRpdid()))
			{
				itemComboDetail = itemComboDetailMap.get(String.valueOf(item.getRpdid()));
			}
			if (null == itemComboDetail)
			{
				if (itemComboDetailMap.containsKey(item.getDishid() + "_" + item.getUnitid()))
				{
					itemComboDetail = itemComboDetailMap.get(item.getDishid() + "_" + item.getUnitid());
				}
				else
				{
					itemComboDetail = itemComboDetailMap.get(String.valueOf(item.getDishid()));
				}
			}
		}
		return itemComboDetail;
	}

	/**
	 * 单品
	 * 
	 * @param item
	 * @param item_serial
	 * @param itemComboDetail
	 * @return
	 */
	private PosOrderItem createOrderItem(OrderDishEntity item, Integer item_serial)
	{
		PosOrderItem orderItem = new PosOrderItem();
		// 辅助Assist
		orderItem.setAssist_item_id("0");
		orderItem.setAssist_num("0");

		orderItem.setItem_id(String.valueOf(item.getDishid()));
		orderItem.setItem_name(item.getDish_name());
		orderItem.setItem_num(item.getDishno());
		orderItem.setItem_price(item.getPrice());
		orderItem.setItem_count(item.getNumber());
		orderItem.setItem_property(SysDictionary.ITEM_PROPERTY_SINGLE);
		orderItem.setItem_remark(null);
		// orderItem.setSale_mode(sale_mode);
		// 规格id
		orderItem.setUnit_id(String.valueOf(item.getUnitid()));
		// 规格名称
		orderItem.setItem_unit_name(item.getUnit_name());
		// 口味备注
		orderItem.setItem_taste(PromptUtil.formatOrderMemo(item.getMemo()));
		// 做法
		orderItem.setMethod(createItemMethod(item.getCooks()));
		// 点菜序号
		orderItem.setItem_serial(item_serial);
		// 座位号
		orderItem.setSeat_num(null);
		// 套餐id
		orderItem.setSetmeal_id(null);
		// 套菜点菜号
		orderItem.setSetmeal_rwid(null);
		// 等叫标记
		// orderItem.setWaitcall_tag(waitcall_tag);
		//小程序点菜人
		orderItem.setOpenid(item.getOpenid());
		return orderItem;
	}

	/**
	 * 套餐主项
	 * 
	 * @param setmeal
	 * @param item_serial
	 * @param itemComboDetail
	 * @return
	 */
	private PosOrderItem createOrderItem(OrderSetmealEntity setmeal, Integer item_serial)
	{
		PosOrderItem orderItem = new PosOrderItem();
		// 辅助Assist
		orderItem.setAssist_item_id("0");
		orderItem.setAssist_num("0");
		
		orderItem.setItem_id(String.valueOf(setmeal.getDishid()));
		orderItem.setItem_name(setmeal.getDish_name());
		orderItem.setItem_num(setmeal.getDishno());
		orderItem.setItem_price(setmeal.getPrice());
		orderItem.setItem_count(setmeal.getNumber());
		orderItem.setItem_property(SysDictionary.ITEM_PROPERTY_SETMEAL);
		orderItem.setItem_remark(null);
		// orderItem.setSale_mode(sale_mode);
		// 规格id
		orderItem.setUnit_id(String.valueOf(setmeal.getUnitid()));
		// 规格名称
		orderItem.setItem_unit_name(setmeal.getUnit_name());
		// 口味备注
		orderItem.setItem_taste(PromptUtil.formatOrderMemo(setmeal.getMemo()));
		// 做法
		orderItem.setMethod(createItemMethod(setmeal.getCooks()));
		// 点菜序号
		orderItem.setItem_serial(item_serial);
		// 座位号
		orderItem.setSeat_num(null);
		// 套餐id
		orderItem.setSetmeal_id(String.valueOf(setmeal.getDishid()));
		// 套菜点菜号
		orderItem.setSetmeal_rwid(item_serial);
		// 等叫标记
		// orderItem.setWaitcall_tag(waitcall_tag);
		//小程序点菜人
		orderItem.setOpenid(setmeal.getOpenid());
		return orderItem;
	}

	/**
	 * 套餐明细
	 * 
	 * @param setmeal
	 * @param item
	 * @param item_serial
	 * @param itemComboDetail
	 * @return
	 */
	private PosOrderItem createOrderItem(OrderSetmealEntity setmeal, OrderSetmealItemEntity item, Integer item_serial, JSONObject itemComboDetail)
	{
		PosOrderItem orderItem = new PosOrderItem();
		// 辅助Assist
		if (itemComboDetail != null)
		{
			orderItem.setOrder_number(itemComboDetail.optInt("combo_order"));
			orderItem.setAssist_item_id(itemComboDetail.getString("id"));
			orderItem.setAssist_num(String.valueOf(DoubleHelper.div(setmeal.getNumber(), item.getNumber(), 0)));
		}
		else
		{
			orderItem.setAssist_item_id("0");
			orderItem.setAssist_num("0");
		}

		orderItem.setItem_id(String.valueOf(item.getDishid()));
		orderItem.setItem_name(item.getDish_name());
		orderItem.setItem_num(item.getDishno());
		// orderItem.setItem_price(item.getAprice());
		orderItem.setItem_count(item.getNumber());
		orderItem.setItem_property(SysDictionary.ITEM_PROPERTY_MEALLIST);
		orderItem.setItem_remark(null);
		// orderItem.setSale_mode(sale_mode);
		// 规格id
		orderItem.setUnit_id(String.valueOf(item.getUnitid()));
		// 规格名称
		orderItem.setItem_unit_name(item.getUnit_name());
		// 口味备注
		orderItem.setItem_taste(PromptUtil.formatOrderMemo(item.getMemo()));
		// 做法
		orderItem.setMethod(createItemMethod(item.getCooks()));
		// 点菜序号
		orderItem.setItem_serial(item_serial);
		// 座位号
		orderItem.setSeat_num(null);
		// 套餐id
		orderItem.setSetmeal_id(String.valueOf(setmeal.getDishid()));
		// 套菜点菜号
		orderItem.setSetmeal_rwid(item_serial);
		// 等叫标记
		// orderItem.setWaitcall_tag(waitcall_tag);
		//小程序点菜人
		orderItem.setOpenid(setmeal.getOpenid());
		return orderItem;
	}

	/**
	 * 套餐明细排序
	 * 
	 * @param setmealItemList
	 * @return
	 * @throws Exception
	 */
	private List<PosOrderItem> sortOrderSetmealItem(List<PosOrderItem> setmealItemList) throws Exception
	{
		if (null == setmealItemList)
		{
			return null;
		}
		Collections.sort(setmealItemList);
		return setmealItemList;
	}
	
	private List<PosOrderItemMethod> createItemMethod(List<OrderCookEntity> cooks)
	{
		List<PosOrderItemMethod> methods = new ArrayList<>();
		if (null != cooks)
		{
			for (OrderCookEntity cook : cooks)
			{
				PosOrderItemMethod method = new PosOrderItemMethod();
				if (CommonUtil.isNullOrEmpty(cook.getCookid()))
				{
					continue;
				}
				method.setMethod_id(String.valueOf(cook.getCookid()));
				method.setMethod_name(cook.getCook_name());
				methods.add(method);
			}
		}
		return methods;
	}

	private PosOrderAssist createOrderAssist(String tenantId,Integer storeId) throws Exception
	{
		PosOrderAssist orderAssist = new PosOrderAssist();
		Date reportDate = afterPaymentDao.getReportDate(tenantId, storeId);
		orderAssist.setReport_date(DateUtil.format(reportDate));
		List<JSONObject> optStateInfoList = posDishService.getOptStateInfo(tenantId, storeId, orderAssist.getReport_date());
		if (optStateInfoList == null || optStateInfoList.size() == 0)
		{
			throw new OtherSystemException(Constant.CODE_NULL_DATASET, Constant.CHECK_POS_LOGIN_FAILURE, null);
		}
		JSONObject optStateInfo = optStateInfoList.get(0);
		orderAssist.setPos_num(optStateInfo.getString("pos_num"));
		orderAssist.setOpt_num(optStateInfo.getString("opt_num"));
		int shiftId = afterPaymentDao.getShiftId(tenantId, storeId, reportDate, orderAssist.getOpt_num(), orderAssist.getPos_num());
		orderAssist.setShift_id(shiftId);
		return orderAssist;
	}

	@Override
	public JSONObject payUploadBill(String tenancyId,Integer storeId,JSONObject param, String channel) throws Exception
	{
		JSONObject responseJson = new JSONObject();
		String tableCode = param.optString("tableno");
		if (StringUtils.isEmpty(tableCode))
		{
			throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
		}
		String outOrderId = param.optString("ordernum");

		// 查询账单pos_bill
		JSONObject billObject = afterPaymentDao.getBillInfo(tenancyId, storeId, tableCode);
		if (null == billObject)
		{
			LOG.error("门店为{}，桌号为:{}的账单不存在", storeId, tableCode);
			throw SystemException.getInstance(PosErrorCode.NOT_EXISTS_BILL);
		}
		String billNum = billObject.optString("bill_num");
		String orderNum = billObject.optString("order_num");
		if (null != billNum && !CommonUtil.hasText(orderNum))
		{
			afterPaymentDao.updateOrderSource(tenancyId, outOrderId, billNum,channel);
		}

		JSONObject orderData = this.getOrderInfo(tenancyId, storeId, tableCode, billObject);
		LOG.info(" getOrderDetail data:" + orderData.toString());

		// 查询最新的openid,查询的是锁单表的openid
		String openid = null;
		List<JSONObject> holdBillList = afterPaymentDao.getBillLockOpenId(tenancyId, storeId, tableCode);
		if (null != holdBillList && holdBillList.size() > 0)
		{
			openid = holdBillList.get(0).optString("open_id");
		}

		JSONObject dataJson = new JSONObject();
		dataJson.put("tableno", tableCode);
		dataJson.put("ordernum", outOrderId);
		dataJson.put("billnum", billNum);
		dataJson.put("order_info", orderData);
		dataJson.put("openid", openid);
		dataJson.put("is_locked", billObject.optString("is_locked"));

		responseJson.put(PromptConstant.SUCCESS, 1);
		responseJson.put(PromptConstant.MSG, "SUCCESS");
		responseJson.put(PromptConstant.DATA, dataJson);
		
		return responseJson;
	}
	
	@Override
	public JSONObject getOrderInfo(String tenancyId, int storeId, String tableCode, JSONObject billObject) throws Exception
	{
		// 订单信息
		JSONObject orderObject = new JSONObject();

		if (null == billObject || billObject.isEmpty())
		{
			return orderObject;
		}
		String billNum = billObject.optString("bill_num");

		orderObject.put("tableno", tableCode);
		orderObject.put("billnum", billNum);
		orderObject.put("ordernum", billObject.optString("order_num"));

		orderObject.put("ordermemo", PromptUtil.formatOrderMemo(billObject.optString("bill_taste")));
		orderObject.put("total_amount", billObject.optDouble("bill_amount"));
		orderObject.put("cost_amount", billObject.optDouble("payment_amount"));
		orderObject.put("people", billObject.optString("guest"));

		// 折扣优惠信息
		int discountModeId = billObject.optInt("discount_mode_id");
		if (discountModeId == SysDictionary.DISCOUNT_MODE_6)
		{
			orderObject.put("member_price", billObject.optDouble("discountk_amount"));
		}
		else
		{
			orderObject.put("member_price", 0);
		}
		orderObject.put("discount_money", billObject.optDouble("discount_amount"));

		//查询优惠
		List<JSONObject> discountInfo = afterPaymentDao.getBillDiscountList(tenancyId, storeId, billNum);
		orderObject.put("discount_info", discountInfo);

		// 查询服务费
		List<JSONObject> serviceCharge = afterPaymentDao.getServiceCharge(tenancyId, storeId, billNum);
		orderObject.put("mealfee_info", serviceCharge);
		orderObject.put("mealfee", billObject.optDouble("service_amount"));

		// 会员信息
		JSONObject member = afterPaymentDao.getBillMemberInfo(tenancyId, storeId, billNum);
		if (CommonUtil.isNullOrEmpty(member))
		{
			member = new JSONObject();
			member.put("openid", "");
			member.put("cno", "");
			member.put("name", "");
			member.put("mobile", "");
			member.put("credit", 0);
			member.put("balance", 0);

		}
		orderObject.put("member", member);

		// 设置订单中的菜品信息
		setDish(tenancyId, storeId, orderObject);
		return orderObject;
	}

	private void setDish(String tenancyId, int storeId, JSONObject orderObject) throws Exception
	{
		// 桌台的未结账的账单号
		String billNum = orderObject.optString("billnum");

		List<JSONObject> setmealList = new ArrayList<JSONObject>();
		List<JSONObject> normalList = new ArrayList<JSONObject>();

		List<JSONObject> methodList = afterPaymentDao.getMethodList(tenancyId, storeId, billNum);
		Map<String, List<JSONObject>> cooksMap = new HashMap<String, List<JSONObject>>();
		if (null != methodList)
		{
			List<JSONObject> cooksList = null;
			JSONObject cookJson = null;
			for (JSONObject methodJson : methodList)
			{
				String key = methodJson.optString("rwid") + "_" + methodJson.optString("item_id");
				if (cooksMap.containsKey(key))
				{
					cooksList = cooksMap.get(key);
				}
				else
				{
					cooksList = new ArrayList<JSONObject>();
				}
				cookJson = new JSONObject();
				cookJson.put("cookid", methodJson.optInt("zfkw_id"));
				cookJson.put("cook_name", methodJson.optString("zfkw_name"));
				cookJson.put("aprice", ParamUtil.getDoubleValueByObject(methodJson, "amount"));
				cooksList.add(cookJson);
				cooksMap.put(key, cooksList);
			}
		}

		List<JSONObject> itemList = afterPaymentDao.getItemList(tenancyId, storeId, billNum);
		if (null != itemList && 0 < itemList.size())
		{
			JSONObject dishJson = null;
			for (JSONObject itemJson : itemList)
			{
				if (SysDictionary.ITEM_PROPERTY_MEALLIST.equals(itemJson.optString("item_property")))
				{
					continue;
				}
				String itemId = ParamUtil.getStringValueByObject(itemJson, "item_id");
				Double itemPrie = ParamUtil.getDoubleValueByObject(itemJson, "item_price");
				Double memberPrie = ParamUtil.getDoubleValueByObject(itemJson, "third_price");
				Double itemCount = ParamUtil.getDoubleValueByObject(itemJson, "item_count");
				Double real_amount = ParamUtil.getDoubleValueByObject(itemJson, "real_amount");
				dishJson = new JSONObject();
				dishJson.put("dishid", Integer.valueOf(itemId));
				dishJson.put("dishno", itemJson.optString("item_num"));
				dishJson.put("dish_name", itemJson.optString("item_name"));
				dishJson.put("kindid", itemJson.optInt("item_class"));
				dishJson.put("unitid", itemJson.optInt("item_unit_id"));
				dishJson.put("unit_name", itemJson.optString("item_unit_name"));
				dishJson.put("number", itemCount);
				dishJson.put("price", real_amount);
				dishJson.put("member_price", itemPrie);
				dishJson.put("is_member_price", "0");
				if(SysDictionary.ITEM_REMARK_TC01.equals(itemJson.optString("item_remark"))){
					dishJson.put("is_return", "1");
				}
				else{
					dishJson.put("is_return", "0");
				}
				if ((SysDictionary.DISCOUNT_MODE_6 == itemJson.optInt("discount_mode_id") || SysDictionary.DISCOUNT_MODE_8 == itemJson.optInt("discount_mode_id")) && 0 < memberPrie.doubleValue() && itemPrie.doubleValue() > memberPrie.doubleValue())
				{
					dishJson.put("member_price", memberPrie);
					dishJson.put("is_member_price", "1");
				}
				dishJson.put("orgprice", itemPrie);
				dishJson.put("is_bargain_price", "0");
				String cookKey = itemJson.optString("rwid") + "_" + itemId;
				if (cooksMap.containsKey(cookKey))
				{
					dishJson.put("cooks", cooksMap.get(cookKey));
				}
				else
				{
					dishJson.put("cooks", new JSONArray());
				}
				dishJson.put("cook_price", ParamUtil.getDoubleValueByObject(itemJson, "method_money"));
				dishJson.put("memo", PromptUtil.formatOrderMemo(itemJson.optString("item_taste")));
				dishJson.put("is_gift", itemJson.optString("is_gift"));
				dishJson.put("openid", itemJson.optString("openid"));
				dishJson.put("discount_rate", itemJson.optString("single_discount_rate"));
				if (SysDictionary.ITEM_PROPERTY_SINGLE.equals(itemJson.optString("item_property")))
				{
					normalList.add(dishJson);
				}
				else if (SysDictionary.ITEM_PROPERTY_SETMEAL.equals(itemJson.optString("item_property")))
				{
					List<JSONObject> maindishList = new ArrayList<JSONObject>();
					List<JSONObject> mandatoryList = new ArrayList<JSONObject>();
					JSONObject detailDishJson = null;
					for (JSONObject detailJson : itemList)
					{
						if (SysDictionary.ITEM_PROPERTY_MEALLIST.equals(detailJson.optString("item_property")) && CommonUtil.eq(itemId, detailJson.optString("setmeal_id")) && CommonUtil.eq(itemJson.optString("item_serial"), detailJson.optString("setmeal_rwid"))
								&& CommonUtil.eq(itemJson.optString("item_remark"), detailJson.optString("item_remark")))
						{
							Double detailItemCount = DoubleHelper.div(ParamUtil.getDoubleValueByObject(detailJson, "item_count"), itemCount, DoubleHelper.SCALE_4);
							Double detailCookPrice = DoubleHelper.div(ParamUtil.getDoubleValueByObject(detailJson, "method_money"), itemCount, DoubleHelper.SCALE_4);

							detailDishJson = new JSONObject();
							detailDishJson.put("dishid", detailJson.optInt("item_id"));
							detailDishJson.put("dishno", detailJson.optString("item_num"));
							detailDishJson.put("dish_name", detailJson.optString("item_name"));
							detailDishJson.put("kindid", detailJson.optInt("item_class"));
							detailDishJson.put("unitid", detailJson.optInt("item_unit_id"));
							detailDishJson.put("unit_name", detailJson.optString("item_unit_name"));
							detailDishJson.put("number", detailItemCount);

							String cookDetailKey = detailJson.optString("rwid") + "_" + detailJson.optInt("item_id");
							if (cooksMap.containsKey(cookDetailKey))
							{
								detailDishJson.put("cooks", cooksMap.get(cookDetailKey));
							}
							else
							{
								detailDishJson.put("cooks", new JSONArray());
							}
							detailDishJson.put("cook_price", detailCookPrice);
							detailDishJson.put("memo", PromptUtil.formatOrderMemo(detailJson.optString("item_taste")));

							if ("Y".equals(detailJson.optString("is_itemgroup")))
							{
								Double detailAPrice = DoubleHelper.div(ParamUtil.getDoubleValueByObject(detailJson, "assist_money"), itemCount, DoubleHelper.SCALE_4);
								detailDishJson.put("aprice", detailAPrice);
								detailDishJson.put("rpdid", detailJson.optInt("item_group_id"));
								mandatoryList.add(detailDishJson);
							}
							else
							{
								maindishList.add(detailDishJson);
							}
						}
					}
					dishJson.put("maindish", maindishList);
					dishJson.put("mandatory", mandatoryList);
					dishJson.put("aprice", ParamUtil.getDoubleValueByObject(itemJson, "assist_money"));
					setmealList.add(dishJson);
				}
			}
		}
		orderObject.put("setmeal", setmealList);
		orderObject.put("normalitems", normalList);
	}
	/** 拆分优惠券虚收
	 * @param payment
	 * @param paymentWayJson
	 * @return
	 * @throws Exception
	 */
	private List<PosBillPayment> getBillPaymentListForCoupons(PosBillPayment payment, PaymentWay paymentWay) throws Exception
	{
		List<com.tzx.base.entity.PosBillPayment> paymentList = new ArrayList<com.tzx.base.entity.PosBillPayment>();
		if (payment.getCurrency_amount() <= payment.getDue())
		{
			paymentList.add(payment);
		}
		else if (payment.getCurrency_amount() == payment.getTenancy_assume())
		{
			if (null != paymentWay)
			{
				payment.setJzid(paymentWay.getId());
				payment.setType(paymentWay.getPayment_class());
				payment.setName(paymentWay.getPayment_name1());
				payment.setName_english(paymentWay.getPayment_name2());
				paymentList.add(payment);
			}
			else
			{
				throw SystemException.getInstance(PosErrorCode.NOT_EXIST_JZID);
			}
		}
		else
		{
			if (null != paymentWay)
			{
				Double phantomMoreCoupon = payment.getMore_coupon().doubleValue();
				Double moreCoupon = 0d;
				if (phantomMoreCoupon > payment.getTenancy_assume())
				{
					phantomMoreCoupon = payment.getTenancy_assume().doubleValue();
					moreCoupon = DoubleHelper.psub(payment.getMore_coupon(), phantomMoreCoupon);
				}
				// 优惠券虚收
				com.tzx.base.entity.PosBillPayment phantomPayment = payment.deepCopy();
				phantomPayment.setJzid(paymentWay.getId());
				phantomPayment.setType(paymentWay.getPayment_class());
				phantomPayment.setName(paymentWay.getPayment_name1());
				phantomPayment.setName_english(paymentWay.getPayment_name2());
				phantomPayment.setAmount(payment.getTenancy_assume());
				phantomPayment.setCurrency_amount(payment.getTenancy_assume());
				phantomPayment.setMore_coupon(phantomMoreCoupon);
				phantomPayment.setCoupon_buy_price(0d);
				phantomPayment.setDue(0d);
				phantomPayment.setId(null);
				paymentList.add(phantomPayment);

				// 优惠券实收
				payment.setAmount(payment.getDue());
				payment.setCurrency_amount(payment.getDue());
				payment.setMore_coupon(moreCoupon);
				payment.setTenancy_assume(0d);
				paymentList.add(payment);
			}
			else
			{
				throw SystemException.getInstance(PosErrorCode.NOT_EXIST_JZID);
			}
		}
		return paymentList;
	}
	@Override
	public JSONObject paymentClose(String tenancyId,Integer storeId,JSONObject param, String channel) throws Exception
	{
		JSONObject responseJson = new JSONObject();
		PaymentInfo microLifePayOrder = (PaymentInfo) JSONObject.toBean(param, getPayJsonConfig());

		// 订单信息
		PayOrderInfo payOrderData = microLifePayOrder.getOrder_info();
		
		// 会员信息
		PayMemberEntity member = microLifePayOrder.getMember_info();
		
		// 付款明细
		List<PayInfoEntity> payInfoList = microLifePayOrder.getPay_info();
		
		if (null == payInfoList || 0==payInfoList.size())
		{
			throw SystemException.getInstance("pay_info is null", PosErrorCode.PARAM_ERROR);
		}
		
		JSONObject posBillJson = null;
		String tagInfo = "A_" + payOrderData.getBillnum();
		if (CommonUtil.hv(payOrderData.getBillnum()))
		{
			posBillJson = afterPaymentDao.getBillInfoByBillNum(tenancyId, storeId, payOrderData.getBillnum());
		}
		else if (CommonUtil.hv(microLifePayOrder.getOrdernum()))
		{
			posBillJson = afterPaymentDao.getBillInfoByOrderNum(tenancyId, storeId, microLifePayOrder.getOrdernum());
			tagInfo = "B_" + microLifePayOrder.getOrdernum();
		}
		else
		{
			throw SystemException.getInstance("oid is null", PosErrorCode.PARAM_ERROR);
		}
		
		if (posBillJson == null)
		{
			// 账单不存在
			LOG.error("账单号为:{}的账单不存在", tagInfo);
			throw SystemException.getInstance(PosErrorCode.NOT_EXISTS_BILL);
		}
		PosBill posBill = JsonUtils.jsonToBean(posBillJson, PosBill.class);

		// 判断账单状态
		if (!SysDictionary.BILL_PROPERTY_OPEN.equals(posBill.getBill_property()))
		{
			LOG.error("账单号为:{}的账单已关闭", tagInfo);
			throw SystemException.getInstance("账单已关闭", PosErrorCode.BILL_CLOSED);
		}
		if (!SysDictionary.PAYMENT_STATE_NOTPAY.equals(posBill.getPayment_state()))
		{
			LOG.error("账单号为:{}的账单处于支付中", tagInfo);
			throw SystemException.getInstance("账单支付中", PosErrorCode.BILL_PAYMENT_STATE_PAYING_ERROR);
		}
		
		// 查询签到班次,获取操作机台
		List<JSONObject> optStateInfoList = posPaymentService.getOptStateInfo(tenancyId, storeId, DateUtil.formatDate(posBill.getReport_date()));
		if (optStateInfoList.size() > 0)
		{
			JSONObject optStateInfo = optStateInfoList.get(0);
			posBill.setPos_num(optStateInfo.getString("pos_num"));
			posBill.setCashier_num(optStateInfo.getString("opt_num"));
		}
		
		// 获取付款方式
		Map<String, PaymentWay> paymentWayMap = getPaymentWayMap(tenancyId, storeId, payInfoList, channel);
		
		// 判断账单金额
		Double paymentAmount = posBill.getPayment_amount();
		payOrderData.setCost_amount(paymentAmount);

		List<PosBillPayment> posBillPaymentList = new ArrayList<PosBillPayment>();
		List<PosBillMember> posBillMemberList = new ArrayList<PosBillMember>();
		List<PosBillPaymentCoupons> posBillPaymentCouponsList = new ArrayList<PosBillPaymentCoupons>();
		
		Double sumPaymentAmount = 0d;
		for (PayInfoEntity payInfo : payInfoList)
		{
			if ((PromptConstant.PAY_SOURCE_WEIXIN.equals(payInfo.getSource()) || PromptConstant.PAY_SOURCE_ALIPAY.equals(payInfo.getSource())) && 0 == payInfo.getAmount())
			{
				//
				continue;
			}

			// 付款金额合计
			sumPaymentAmount = DoubleHelper.padd(sumPaymentAmount, payInfo.getAmount());

			// 付款明细
			PaymentWay paymentWay = getPaymentWay(paymentWayMap, payInfo.getSource(), channel);
			PosBillPayment createPosBillPayment = createPosBillPayment(tenancyId, storeId, member, posBill, paymentWay, payInfo, channel);

			String enableCouponsCalculateBill = afterPaymentDao.getSysParameter(tenancyId, storeId, SysParameterCode.ENABLE_COUPONS_CALCULATE_BILL);
			PaymentWay paymentCouponPhantomWay = getPaymentWay(paymentWayMap, PromptConstant.PAY_SOURCE_COUPON_PHANTOM_YZ, SysDictionary.CHANEL_QIMAI);
			// 优惠券明细
			if (PromptConstant.PAY_SOURCE_COUPON_LIST.contains(payInfo.getSource()))
			{
				String paymentUid=Md5Utils.cmd5(JSONObject.fromObject(createPosBillPayment).toString());
				createPosBillPayment.setPayment_uid(paymentUid);

				//优惠券虚收实收 记录
				if ("2".equals(enableCouponsCalculateBill))
				{
					posBillPaymentList.addAll(this.getBillPaymentListForCoupons(createPosBillPayment, paymentCouponPhantomWay));
				}
				else{
					posBillPaymentList.add(createPosBillPayment);
				}

				posBillPaymentCouponsList.addAll(createPosBillPaymentCoupons(tenancyId, storeId, posBill, payInfo.getCoupons_info(), paymentUid));
			}
			else{
				posBillPaymentList.add(createPosBillPayment);
			}

			// 如果有会员信息
			if (member != null && PromptConstant.PAY_SOURCE_MEMBER_LIST.contains(payInfo.getSource()))
			{
				if (PromptConstant.PAY_SOURCE_BALANCE_LIST.contains(payInfo.getSource()) && 0d < payInfo.getAmount())
				{
					PosBillMember createPosBillMember = createPosBillMember(tenancyId, storeId, posBill.getBill_num(), posBill.getReport_date(), member, payInfo, SysDictionary.BILL_MEMBERCARD_CZXF03);
					posBillMemberList.add(createPosBillMember);

					List<CrmCardTradingListEntity> crmCardTradingListEntityList = new ArrayList<>();
					CrmCardTradingListEntity crmCardTradingList = createCrmCardTradingList(tenancyId, storeId, member, posBill, payInfo);
					crmCardTradingListEntityList.add(crmCardTradingList);
					afterPaymentDao.saveCrmCardTradingList(crmCardTradingListEntityList);
				}
				else if (PromptConstant.PAY_SOURCE_CREDIT_LIST.contains(payInfo.getSource()) && 0d < payInfo.getCredit())
				{
					PosBillMember createPosBillMember = createPosBillMember(tenancyId, storeId, posBill.getBill_num(), posBill.getReport_date(), member, payInfo, SysDictionary.BILL_MEMBERCARD_JFDX05);
					posBillMemberList.add(createPosBillMember);
				}
				else if (PromptConstant.PAY_SOURCE_COUPON_LIST.contains(payInfo.getSource()))
				{
					PosBillMember createPosBillMember = createPosBillMember(tenancyId, storeId, posBill.getBill_num(), posBill.getReport_date(), member, payInfo, SysDictionary.BILL_MEMBERCARD_YHJ04);
					posBillMemberList.add(createPosBillMember);
				}
			}
		}

		// 会员奖励积分以及优惠券
		if (null != member && (0d < member.getReceive_credit() || CommonUtil.hv(member.getReceive_coupons())))
		{
			PosBillMember createPosBillMember = createPosBillMember(tenancyId, storeId, posBill.getBill_num(), posBill.getReport_date(), member, null, SysDictionary.BILL_MEMBERCARD_JFZS06);
			createPosBillMember.setAmount(posBill.getPayment_amount());
			createPosBillMember.setCredit(member.getReceive_credit());
			posBillMemberList.add(createPosBillMember);
		}
				
		if (null != posBillPaymentList && 0 < posBillPaymentList.size())
		{
			afterPaymentDao.savePosBillPayment(posBillPaymentList);
		}

		if (null != posBillPaymentCouponsList && 0 < posBillPaymentCouponsList.size())
		{
			afterPaymentDao.savePosBillPaymentCouponsList(posBillPaymentCouponsList);
		}

		if (null != posBillMemberList && 0 < posBillMemberList.size())
		{
			memberDao.deletePosBillMember(tenancyId, storeId, posBill.getBill_num(), new String[] {SysDictionary.BILL_MEMBERCARD_JFZS06});
			memberDao.batchInsertPosBillMember(tenancyId, storeId, posBillMemberList);
		}
		
		afterPaymentDao.updatePosBillSource(tenancyId, posBill.getBill_num(), channel);
		
		if (DoubleHelper.sub(paymentAmount, sumPaymentAmount, DoubleHelper.SCALE_4) <= 0)
		{
			JSONObject printJson = new JSONObject();
			JSONObject resultJson = new JSONObject();
			String isInvoice = StringUtils.isEmpty(payOrderData.getInvoice_title()) ? "0" : "1";
			posPaymentService.closedAcewillPosBill(tenancyId, storeId, posBill.getBill_num(), DateUtil.formatDate(posBill.getReport_date()), posBill.getShift_id(), posBill.getPos_num(), posBill.getWaiter_num(), "Y", isInvoice, resultJson, printJson, "1", DateUtil.currentTimestamp());
			LOG.info("pay.success:单号：" + posBill.getBill_num() + "=======================================================门店清台成功");
			
			// 打印
			try
			{
				posPrintService.printPosBillForPayment(printJson, posPrintNewService);
				LOG.info("pay.success:单号：" + posBill.getBill_num() + "=======================================================门店清台打印完成");
			}
			catch (Exception e)
			{
				LOG.error("print failed... bill_number:" + posBill.getBill_num());
			}
			
			JSONObject resultData = new JSONObject();
			resultData.put("meal_number", StringUtils.substring(posBill.getBill_num(), (posBill.getBill_num().length()-4)));

			responseJson.put(PromptConstant.SUCCESS, 1);
			responseJson.put(PromptConstant.MSG, "SUCCESS");
			responseJson.put(PromptConstant.DATA, resultData);
		}
		else
		{
			throw SystemException.getInstance(PosErrorCode.BILL_DIFFERENCE_MORE_ZERO_ERROR);
		}
		return responseJson;
	}

	private JsonConfig getPayJsonConfig()
	{
		Map<String, Class<?>> classMap = new HashMap<>();
		classMap.put("pay_info", PayInfoEntity.class);
		classMap.put("member_info", PayMemberEntity.class);
		classMap.put("receive_coupons", ConsumeCouponsEntity.class);
		classMap.put("coupons_info",ConsumeCouponsEntity.class);
		classMap.put("order_info", PayOrderInfo.class);
		
		return PromptUtil.getJsonConfig(PaymentInfo.class, classMap);
	}
	
	private Map<String, PaymentWay> getPaymentWayMap(String tenantId, Integer storeId, List<PayInfoEntity> payInfoList, String orderChannel) throws Exception
	{
		List<String> paymentClassList = new ArrayList<>();
		for (PayInfoEntity payInfo : payInfoList)
		{
			String paymentClass = PromptUtil.getPaymentClassBySource(payInfo.getSource(), orderChannel);
			if (!StringUtils.isEmpty(paymentClass))
			{
				paymentClassList.add(paymentClass);
			}
		}
		if (SysDictionary.CHANEL_QIMAI.equals(orderChannel)) {
			paymentClassList.add(QimaiPaymentWayEnum.getPaymentClassBySource(PromptConstant.PAY_SOURCE_COUPON_PHANTOM_YZ));
		}

		List<PaymentWay> paymentWayList = afterPaymentDao.findPaymentWay(tenantId, storeId, paymentClassList);
		Map<String, PaymentWay> paymentWayMap = new HashMap<>();
		if (paymentWayList != null)
		{
			for (PaymentWay bean : paymentWayList)
			{
				paymentWayMap.put(bean.getPayment_class(), bean);
			}
		}
		return paymentWayMap;
	}

	private PaymentWay getPaymentWay(Map<String, PaymentWay> paymentWayMap, String paySource, String orderChannel) throws Exception
	{
		String paymentClass = PromptUtil.getPaymentClassBySource(paySource, orderChannel);
		if (paymentClass == null)
		{
			LOG.error("未知的支付方式：{}", paySource);
			return null;
		}
		PaymentWay paymentWay = paymentWayMap.get(paymentClass);
		if (paymentWay == null)
		{
			LOG.error("支付方式{}未启用", paySource);
			return null;
		}
		return paymentWay;
	}

	private PosBillPayment createPosBillPayment(String tenancyId, Integer storeId, PayMemberEntity memberInfo, PosBill posBill, PaymentWay paymentWay, PayInfoEntity payInfo,String orderSource) throws Exception
	{
		PosBillPayment posBillPayment = new PosBillPayment();
		posBillPayment.setTenancy_id(tenancyId);
		posBillPayment.setStore_id(storeId);
		posBillPayment.setBill_num(posBill.getBill_num());
		posBillPayment.setBatch_num(posBill.getBatch_num()); // 批次编号
		posBillPayment.setTable_code(posBill.getTable_code());
		posBillPayment.setReport_date(posBill.getReport_date());
		posBillPayment.setShift_id(posBill.getShift_id());
		posBillPayment.setPos_num(posBill.getPos_num());
		posBillPayment.setCashier_num(posBill.getCashier_num());// 收款员号
		if (null != paymentWay)
		{
			posBillPayment.setType(paymentWay.getPayment_class());
			posBillPayment.setJzid(paymentWay.getId());
			posBillPayment.setName(paymentWay.getPayment_name1());
			posBillPayment.setName_english(paymentWay.getPayment_name2());
		}else
		{
//			String paymentClass = PromptUtil.getPaymentClassBySource(payInfo.getSource(), orderSource);
//			posBillPayment.setType(paymentClass);
			LOG.warn(String.format("付款来源%s对应付款方式不存在", payInfo.getSource()));
			throw SystemException.getInstance(PosErrorCode.NOT_EXIST_JZID);
		}
		
		posBillPayment.setAmount(payInfo.getAmount());
		posBillPayment.setCurrency_amount(payInfo.getAmount());
		// 付款流水号
		posBillPayment.setBill_code(payInfo.getSerilno());
		posBillPayment.setCount(1);
		// 付款号码
		if (memberInfo != null && (PromptConstant.PAY_SOURCE_MEMBER_LIST.contains(payInfo.getSource())))
		{
			posBillPayment.setNumber(memberInfo.getCno());
			posBillPayment.setPhone(memberInfo.getMobile());
		}
		
		if (PromptConstant.PAY_SOURCE_COUPON_LIST.contains(payInfo.getSource()))
		{
			// 优惠券类型
			posBillPayment.setCoupon_type(SysDictionary.COUPON_TYPE_CODE);
		}
		posBillPayment.setLast_updatetime(DateUtil.currentTimestamp());
		posBillPayment.setIs_ysk("N");
		posBillPayment.setRate(1d);
		posBillPayment.setPayment_state(SysDictionary.PAYMENT_STATE_PAY_COMPLETE);

		Double couponBuyPrice = payInfo.getBuyer_pay_amount();
		Double due = payInfo.getReceipt_amount();
		Double tenancyAssume = DoubleHelper.psub(payInfo.getAmount(), due);
		Double thirdAssume = DoubleHelper.psub(due, couponBuyPrice);
		Double thirdFee = 0d;

		// 用户实付
		posBillPayment.setCoupon_buy_price(couponBuyPrice);
		// 券账单净收
		posBillPayment.setDue(due);
		// 商家优惠承担
		posBillPayment.setTenancy_assume(tenancyAssume);
		// 第三方优惠承担
		posBillPayment.setThird_assume(thirdAssume);
		// 第三方票券服务费
		posBillPayment.setThird_fee(thirdFee);
		return posBillPayment;
	}

	private PosBillMember createPosBillMember(String tenancyId, Integer storeId, String billNum, Date reportDate, PayMemberEntity wlife, PayInfoEntity payInfo, String operatType) throws Exception
	{
		PosBillMember posBillMember = new PosBillMember();
		posBillMember.setTenancy_id(tenancyId);
		posBillMember.setStore_id(storeId);
		posBillMember.setBill_num(billNum);
		posBillMember.setReport_date(reportDate);
		// 类型
		posBillMember.setType(operatType);
		if (null != payInfo)
		{
			posBillMember.setAmount(payInfo.getAmount());
			if (PromptConstant.PAY_SOURCE_CREDIT_LIST.contains(payInfo.getSource()))
			{
				posBillMember.setCredit(payInfo.getCredit());
			}
			posBillMember.setBill_code(payInfo.getSerilno());
		}
		posBillMember.setCard_code(wlife.getCno());
		posBillMember.setMobil(wlife.getMobile());
		posBillMember.setCustomer_code(null);
		posBillMember.setCustomer_name(wlife.getName());
		// 交易前积分
		posBillMember.setConsume_before_credit(wlife.getBeforeCredit());
		// 交易后积分
		posBillMember.setConsume_after_credit(wlife.getCredit());
		// 交易前主账户余额
		posBillMember.setConsume_before_main_balance(wlife.getBeforeBalance());
		// 交易前赠送账户余额
		posBillMember.setConsume_before_reward_balance(0d);
		// 交易后主账户余额
		posBillMember.setConsume_after_main_balance(wlife.getBalance());
		// 交易后赠送账户余额
		posBillMember.setConsume_after_reward_balance(0d);
		//会员类型
		posBillMember.setCustomer_type(wlife.getCustomerType());
		posBillMember.setLast_updatetime(DateUtil.currentTimestamp());

		return posBillMember;
	}

	private List<PosBillPaymentCoupons> createPosBillPaymentCoupons(String tenancyId, Integer storeId, PosBill posBill, List<ConsumeCouponsEntity> consumeCouponsList,String paymentUid)
	{
		Timestamp currentTime = DateUtil.currentTimestamp();
		List<PosBillPaymentCoupons> couponsList = new ArrayList<PosBillPaymentCoupons>();
		if (null != consumeCouponsList && 0 < consumeCouponsList.size())
		{
			for (ConsumeCouponsEntity couponInfo : consumeCouponsList)
			{
				Double incomeAmount =  couponInfo.getIncome_amount();
				if(null==incomeAmount){
					incomeAmount = 0d;
				}
				Double couponAmount =  couponInfo.getCoupon_amount();
				if(null==couponAmount){
					couponAmount = 0d;
				}
				Integer couponCount = couponInfo.getCoupon_count();
				if(null==couponCount){
					couponCount = 1;
				}
				Double couponBuyPrice = DoubleHelper.pmui(incomeAmount, couponCount.doubleValue());
				Double due = DoubleHelper.pmui(incomeAmount, couponCount.doubleValue());
				Double tenancyAssume = DoubleHelper.psub(couponAmount, due);
				Double thirdAssume = 0d;
				Double thirdFee = 0d;

				PosBillPaymentCoupons paymentCoupons = new PosBillPaymentCoupons(tenancyId, storeId, posBill.getBill_num(), posBill.getReport_date(), paymentUid, couponInfo.getCoupon_code(), couponInfo.getCoupon_value(), couponInfo.getCoupon_name(), currentTime, null, null,
						couponInfo.getCoupon_id(), couponAmount, couponCount.doubleValue(), posBill.getSource(), PromptUtil.getCouponPro(couponInfo.getCoupon_pro()), SysDictionary.COUPON_TYPE_CODE);

				// 优惠劵号
				paymentCoupons.setCoupons_code("");
				// 菜品单价
				paymentCoupons.setPrice(null);
				// 抵扣菜品
				paymentCoupons.setItem_id(null);
				// 菜品数量
				paymentCoupons.setItem_num(null);
				paymentCoupons.setCoupon_buy_price(couponBuyPrice);
				paymentCoupons.setDue(due);
				paymentCoupons.setTenancy_assume(tenancyAssume);
				paymentCoupons.setThird_assume(thirdAssume);
				paymentCoupons.setThird_fee(thirdFee);

				couponsList.add(paymentCoupons);
			}
		}
		return couponsList;
	}

	private CrmCardTradingListEntity createCrmCardTradingList(String tenancyId, Integer storeId, PayMemberEntity wlife, PosBill posBill, PayInfoEntity payInfo)
	{
		CrmCardTradingListEntity crmCardTradingList = new CrmCardTradingListEntity();
		Timestamp timestamp = new Timestamp(System.currentTimeMillis());
		// AcewillPayWLife wlife = microLifePayOrder.getWlife();
		// 商户id
		crmCardTradingList.setTenancy_id(tenancyId);
		// Id
		crmCardTradingList.setId(null);
		// 卡id
		crmCardTradingList.setCard_id(null);
		// 卡号 
		crmCardTradingList.setCard_code(wlife.getCno());
		// 交易单号
		crmCardTradingList.setBill_code(payInfo.getSerilno());
		// 交易渠道
		crmCardTradingList.setChanel(posBill.getSource());
		// 交易门店ID
		crmCardTradingList.setStore_id(storeId);
		// 交易日期
		crmCardTradingList.setBusiness_date(posBill.getReport_date());
		// 主账户交易金额
		crmCardTradingList.setMain_trading(payInfo.getAmount());
		// 赠送账户交易金额
		crmCardTradingList.setReward_trading(0d);
		// 交易类型
		crmCardTradingList.setOperat_type(SysDictionary.OPERAT_TYPE_XF);

		// 原主账户金额
		crmCardTradingList.setMain_original(DoubleHelper.add(wlife.getBalance(), payInfo.getAmount(), DoubleHelper.SCALE_4));
		// 原赠送账户金额
		crmCardTradingList.setReward_original(0d);
		// 押金金额
		crmCardTradingList.setDeposit(0d);
		// 操作员
		crmCardTradingList.setOperator(null);
		// 操作时间
		crmCardTradingList.setOperate_time(timestamp);
		// 账单金额
		crmCardTradingList.setBill_money(posBill.getPayment_amount());
		// 第三方账单号
		crmCardTradingList.setThird_bill_code(posBill.getBill_num());
		// 原账单号
		crmCardTradingList.setBill_code_original(null);
		// 活动ID
		crmCardTradingList.setActivity_id(null);
		// 会员ID
		crmCardTradingList.setCustomer_id(null);
		// 已撤销金额
		crmCardTradingList.setRevoked_trading(0d);
		//
		crmCardTradingList.setBatch_num(posBill.getBatch_num());
		// 最后修改时间
		crmCardTradingList.setLast_updatetime(timestamp);
		// 门店修改时间
		crmCardTradingList.setStore_updatetime(timestamp);
		// 卡类ID
		crmCardTradingList.setCard_class_id(null);
		// 会员name
		crmCardTradingList.setName(wlife.getName());
		// 会员电话
		crmCardTradingList.setMobil(wlife.getMobile());
		// 操作员ID
		crmCardTradingList.setOperator_id(null);
		// 班次ID
		crmCardTradingList.setShift_id(posBill.getShift_id());
		// 卡余额
		crmCardTradingList.setTotal_balance(wlife.getBalance());
		// 卡赠送账户余额
		crmCardTradingList.setReward_balance(0d);
		// 卡主账户余额
		crmCardTradingList.setMain_balance(wlife.getBalance());
		// 付款方式
		crmCardTradingList.setPay_type(null);

		// 销售人员ID
		crmCardTradingList.setSalesman(null);
		// 人员提成金额
		crmCardTradingList.setCommission_saler_money(0d);
		// 机构提成金额
		crmCardTradingList.setCommission_store_money(0d);
		// 可开票金额
		crmCardTradingList.setInvoice_balance(0d);

		crmCardTradingList.setIs_invoice("0");
		crmCardTradingList.setPayment_state("1");
		crmCardTradingList.setRecharge_state("1");
		crmCardTradingList.setRequest_status("02");
		crmCardTradingList.setRequest_code(null);
		crmCardTradingList.setRequest_msg(null);

		return crmCardTradingList;
	}

	@Override
	public JSONObject lockOrder(String tenancyId, Integer storeId, JSONObject param) throws Exception
	{
		JSONObject responseJson = new JSONObject();
		
		String tableCode = param.optString("tableno");
		String openId = param.optString("openid");
		String name = param.optString("name");
		String mobile = param.optString("mobile");

		JSONObject tableStatus = afterPaymentDao.getTableStatus(tenancyId, storeId, tableCode);
		if (null == tableStatus || tableStatus.isEmpty())
		{
			throw SystemException.getInstance(PosErrorCode.NOT_EXISTS_TABLE_CODE);
		}

		String state = tableStatus.optString("state");
		String lockOptNum = tableStatus.optString("lock_opt_num");
		String lockPosNum = tableStatus.optString("lock_pos_num");

		if (false == SysDictionary.TABLE_STATE_BUSY.equals(state))
		{
			// 桌位不是占用状态,桌位未开台
			throw SystemException.getInstance(PosErrorCode.TABLE_STATE_EXCEPTION);
		}

		if (CommonUtil.hv(lockPosNum) && false == LOCK_POS_NUM.equals(lockPosNum))
		{
			// 桌位已锁台,但不是微生活锁台
			throw SystemException.getInstance(PosErrorCode.TABLE_ALREADY_LOCK_ERROR).set("{0}", lockOptNum).set("{1}", lockPosNum);
		}
		else
		{
			if(CommonUtil.isNullOrEmpty(mobile))
			{
				mobile = openId;
			}
			if(CommonUtil.isNullOrEmpty(name))
			{
				name = openId;
			}
			afterPaymentDao.lockTableStatus(storeId, tableCode, LOCK_POS_NUM, mobile, name);
		}

		responseJson.put(PromptConstant.SUCCESS, 1);
		responseJson.put(PromptConstant.MSG, "SUCCESS");
		
		return responseJson;
	}

	@Override
	public JSONObject unlockOrder(String tenancyId, Integer storeId, JSONObject param) throws Exception
	{
		JSONObject responseJson = new JSONObject();
		String tableCode = param.optString("tableno");
		String outOrderId = param.optString("ordernum");
		if (CommonUtil.isNullOrEmpty(outOrderId))
		{
			throw SystemException.getInstance(PosErrorCode.ORDER_NOT_EXISTS_ERROR);
		}

		if (SysDictionary.ORDER_MODE_AFTERPAY.equals(afterPaymentDao.getSysParameter(tenancyId, storeId, SysParameterCode.WLIFE_ORDERMODE_KEY)))
		{
			// 根据微生活订单号查询账单
			JSONObject billJson = afterPaymentDao.getBillInfoByOrderNum(tenancyId, storeId, outOrderId);
			if (null == billJson || billJson.isEmpty())
			{
				throw SystemException.getInstance(PosErrorCode.NOT_EXISTS_BILL);
			}
			
			JSONObject tableStatus = afterPaymentDao.getTableStatus(tenancyId, storeId, tableCode);
			if (null == tableStatus || tableStatus.isEmpty())
			{
				throw SystemException.getInstance(PosErrorCode.NOT_EXISTS_TABLE_CODE);
			}

			String state = tableStatus.optString("state");
			String lockOptNum = tableStatus.optString("lock_opt_num");
			String lockPosNum = tableStatus.optString("lock_pos_num");

			if (false == SysDictionary.TABLE_STATE_BUSY.equals(state))
			{
				// 桌位不是占用状态,桌位未开台
				throw SystemException.getInstance(PosErrorCode.TABLE_STATE_EXCEPTION);
			}

			if (CommonUtil.hv(lockPosNum) && false == LOCK_POS_NUM.equals(lockPosNum))
			{
				// 桌位已锁台,但不是微生活锁台
				throw SystemException.getInstance(PosErrorCode.TABLE_ALREADY_LOCK_ERROR).set("{0}", lockOptNum).set("{1}", lockPosNum);
			}
			else
			{
				afterPaymentDao.lockTableStatus(storeId, tableCode, null, null, null);
			}
		}
		responseJson.put(PromptConstant.SUCCESS, 1);
		responseJson.put(PromptConstant.MSG, "SUCCESS");
		return responseJson;
	}

	@Override
	public List<JSONObject> getBilPayment(String tenancyId, int storeId, String billNum) throws Exception {
		return afterPaymentDao.getBilPayment(tenancyId, storeId, billNum);
	}

	@Override
	public JSONObject getTableOrder(String tenancyId, Integer storeId, JSONObject jsobj,String channel) throws Exception
	{
		JSONObject responseJson = new  JSONObject();
		String tableCode = jsobj.optString("tableno");
		String orderNum = jsobj.optString("ordernum");

		LOG.info("小程序查询桌台状态参数有：桌台是" + tableCode + ",传入的平台订单号是:" + orderNum);
		if (StringUtils.isEmpty(tableCode))
		{
			throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
		}
		
		JSONObject data = new JSONObject();
		
		JSONObject tableStateJson = afterPaymentDao.getTableStateForBill(tenancyId, storeId, tableCode);
		if(null!=tableStateJson && !tableStateJson.isEmpty())
		{
			String billNum = tableStateJson.optString("bill_num");
			String billOrderNum = tableStateJson.optString("order_num");
			
			if (CommonUtil.hv(orderNum))
			{
				if (CommonUtil.isNullOrEmpty(billOrderNum) || !orderNum.equals(billOrderNum))
				{
					//请求平台单号与桌位账单订单号不一致
					JSONObject billJson = afterPaymentDao.getBillInfoByOrderNum(tenancyId, storeId, orderNum);
					if (null != billJson && !billJson.isEmpty())
					{
						// 桌号一致，并且桌位已关台，调用结账通知
						if (SysDictionary.BILL_PROPERTY_CLOSED.equals(billJson.optString("bill_property")))
						{
							programService.completeOrder(tenancyId, storeId, billJson.optString("bill_num"));
						}
						// 桌号不一致，并且平台订单号查询的订单还未关台，调用转台通知
						else if (!tableCode.equals(billJson.optString("fictitious_table")))
						{
//							programService.changeTable(tenancyId, storeId, billJson.optString("bill_num"), billJson.optString("fictitious_table"));
						}
					}//&& CommonUtil.isNullOrEmpty(billOrderNum)
					else if (CommonUtil.hv(billNum))
					{
						afterPaymentDao.updateOrderSource(tenancyId, billNum, orderNum,channel);
						billOrderNum = orderNum;
					}
				}
			}
			
			String tablestatus = "0";
			if (SysDictionary.TABLE_STATE_BUSY.equals(tableStateJson.optString("state")) && CommonUtil.hv(billNum))
			{
				tablestatus = "1";
			}
			
			data.put("tableno", tableCode);
			data.put("status", tablestatus);
			data.put("ordernum", billOrderNum);
			data.put("billnum", billNum);
		}else 
		{
			throw SystemException.getInstance(PosErrorCode.NOT_EXISTS_TABLE_CODE);
		}

		responseJson.put(PromptConstant.SUCCESS, 1);
		responseJson.put(PromptConstant.MSG, PromptMsgConstant.QUERY_ORDER_SUCCESS);
		responseJson.put(PromptConstant.DATA, data);

		LOG.info("微生活小程序查询桌台状态返回结果" + responseJson);
		return responseJson;
	}
}
