package com.tzx.clientorder.mtwechat.po.springjdbc.dao.imp;

import java.util.Date;
import java.util.List;

import net.sf.json.JSONObject;

import org.springframework.stereotype.Repository;

import com.tzx.clientorder.mtwechat.po.springjdbc.dao.MtSoldOutDao;
import com.tzx.framework.common.util.DateUtil;
import com.tzx.pos.base.dao.imp.BaseDaoImp;

/**
 * Created by qingui on 2018-06-01.
 */
@Repository(MtSoldOutDao.NAME)
public class MtSoldOutDaoImp extends BaseDaoImp implements MtSoldOutDao{

    @Override
    public List<JSONObject> getItemUnits(String tenancyId, int storeId, Date reportDate) throws Exception {
        StringBuffer sql = new StringBuffer();
        String date = DateUtil.formatDate(reportDate);
        sql.append(" select hiu.item_id as did,hiu.id as duid,himc.class as pkid,hii.item_code as dishsno from pos_soldout ps ")
                .append(" left join hq_item_unit hiu on ps.item_id = hiu.item_id and hiu.valid_state = '1'")
                .append(" left join hq_item_info hii on ps.item_id = hii.id and hii.valid_state = '1'")
                .append(" join hq_item_menu_details himd on hii.id=himd.item_id and himd.valid_state='1'")
                .append(" join hq_item_menu_class himc on himc.details_id = himd.id and himc.chanel='WX02' ")
                .append(" where ps.setdate = '"+ date +"' and ps.num = 0 and ps.store_id = " + storeId);
        return this.query4Json(tenancyId, sql.toString());
    }

    @Override
    public List<JSONObject> getItems(String tenancyId, int storeId, String itemIds, Date reportDate) throws Exception {
        StringBuffer sql = new StringBuffer();
        String date = DateUtil.formatDate(reportDate);
        sql.append(" select hiu.item_id as did,hiu.id as duid,himc.class as pkid,hii.item_code as dishsno from pos_soldout ps ")
                .append(" left join hq_item_unit hiu on ps.item_id = hiu.item_id and hiu.valid_state = '1'")
                .append(" left join hq_item_info hii on ps.item_id = hii.id and hii.valid_state = '1'")
                .append(" join hq_item_menu_details himd on hii.id=himd.item_id and himd.valid_state='1'")
                .append(" join hq_item_menu_class himc on himc.details_id = himd.id and himc.chanel='WX02' ")
                .append(" where ps.setdate = '"+ date +"' and ps.num = 0 and ps.store_id = " + storeId)
                .append(" and ps.item_id in ("+ itemIds +")");
        return this.query4Json(tenancyId, sql.toString());
    }

    @Override
    public List<JSONObject> getUnits(String tenancyId, String itemIds) throws Exception {
        StringBuffer sql = new StringBuffer();
        sql.append(" select hiu.item_id as did,hiu.id as duid,himc.class as pkid,hii.item_code as dishsno from hq_item_unit hiu " +
                " join hq_item_info hii on hiu.item_id = hii.id  " +
                " join hq_item_menu_details himd on hii.id=himd.item_id and himd.valid_state='1'" +
                " join hq_item_menu_class himc on himc.details_id = himd.id and himc.chanel='WX02'" +
                " where hiu.item_id in ("+ itemIds +") ");
        return this.query4Json(tenancyId, sql.toString());
    }
}
