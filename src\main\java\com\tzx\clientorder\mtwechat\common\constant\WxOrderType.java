package com.tzx.clientorder.mtwechat.common.constant;

/**
 * 美团对接微信点餐，传参类型
 * Created by qing<PERSON> on 2018-05-30.
 */
public class WxOrderType {

    /**
     * 心跳检测接口
     */
    public static final String	HEART_BEAT = "heartbeat";

    /**
     * 拉取基础资料
     */
    public static final String	BASIC_INFO = "basicInfo";

    /**
     * 下单
     */
    public static final String	EXPRESS_CHECKOUT = "expressCheckout";

    /** 沽清 */
    //public static final String	SOLD_OUT_URL			= "/developer/rendor/dish/soldOut";
    public static final String	SOLD_OUT_URL			= "http://127.0.0.1:8088/dish/soldOut";
    /** 取消沽清 */
    //public static final String	SOLD_OUT_CALCEL_URL		= "/developer/rendor/dish/cancelSoldOut";
    public static final String	SOLD_OUT_CALCEL_URL		= "http://127.0.0.1:8088/dish/cancelSoldOut";

//	public static final String	USER_WLIFE_ORDER_TYPE_KEY				= "USER_WLIFE_ORDER_TYPE";

//	// 美味不用等小程序点餐
//	public static final String	USER_WLIFE_ORDER_TYPE_MEIWEI_PROGRAM	= "MEIWEI_PROGRAM";
    
    /** 门店版本 */
	public static final String	WLIFE_SHOP_VERSION		= "2.11.2";
}
