package com.tzx.base.entity;

import java.lang.reflect.Field;
import java.sql.Timestamp;
import java.util.Date;

import com.tzx.pos.base.util.ParamUtil;

import net.sf.json.JSONObject;

/**
 * 账单付款明细表映射实体
 * 
 * <AUTHOR>
 * <AUTHOR>
 */
public class PosBillPaymentCoupons
{
	private String		tenancy_id;
	private Integer		store_id;
	private Integer		id;
	private String		bill_num;
	private Date		report_date;
	private String		payment_id;
	private String		coupons_code;
	private Double		deal_value			= 0d;
	private String		deal_name;
	private Timestamp	last_updatetime;
	private String		remark;
	private String		upload_tag			= "0";
	private String		is_cancel			= "0";
	private String		class_id;
	private String		type_id;
	private Double		discount_money		= 0d;
	private Double		discount_num		= 1d;
	private String		chanel;
	private Double		price;
	private Integer		item_id;
	private Integer		item_num;
	private String		coupons_pro;
	private Integer		coupon_type;
	private Double		coupon_buy_price	= 0d;
	private Double		due					= 0d;
	private Double		tenancy_assume		= 0d;
	private Double		third_assume		= 0d;
	private Double		third_fee			= 0d;
	private Integer		request_state;
	private Integer		rwid;
	private Integer		item_unit_id;
	private Double		more_coupon			= 0d;
	private Double		real_assume			= 0d;

	private String rwids;

	public PosBillPaymentCoupons()
	{
		super();
	}

	public PosBillPaymentCoupons(String tenancy_id, Integer store_id, String bill_num, Date report_date, String payment_id, String coupons_code, Double deal_value, String deal_name, Timestamp last_updatetime, String remark, String class_id, String type_id, Double discount_money,
			Double discount_num, String chanel, String coupons_pro, Integer coupon_type)
	{
		super();
		this.tenancy_id = tenancy_id;
		this.store_id = store_id;
		this.bill_num = bill_num;
		this.report_date = report_date;
		this.payment_id = payment_id;
		this.coupons_code = coupons_code;
		this.deal_value = deal_value;
		this.deal_name = deal_name;
		this.last_updatetime = last_updatetime;
		this.remark = remark;
		this.class_id = class_id;
		this.type_id = type_id;
		this.discount_money = discount_money;
		this.discount_num = discount_num;
		this.chanel = chanel;
		this.coupons_pro = coupons_pro;
		this.coupon_type = coupon_type;
	}

	public String getTenancy_id()
	{
		return tenancy_id;
	}

	public void setTenancy_id(String tenancy_id)
	{
		this.tenancy_id = tenancy_id;
	}

	public Integer getStore_id()
	{
		return store_id;
	}

	public void setStore_id(Integer store_id)
	{
		this.store_id = store_id;
	}

	public Integer getId()
	{
		return id;
	}

	public void setId(Integer id)
	{
		this.id = id;
	}

	public String getBill_num()
	{
		return bill_num;
	}

	public void setBill_num(String bill_num)
	{
		this.bill_num = bill_num;
	}

	public Date getReport_date()
	{
		return report_date;
	}

	public void setReport_date(Date report_date)
	{
		this.report_date = report_date;
	}

	public String getPayment_id()
	{
		return payment_id;
	}

	public void setPayment_id(String payment_id)
	{
		this.payment_id = payment_id;
	}

	public String getCoupons_code()
	{
		return coupons_code;
	}

	public void setCoupons_code(String coupons_code)
	{
		this.coupons_code = coupons_code;
	}

	public Double getDeal_value()
	{
		return deal_value;
	}

	public void setDeal_value(Double deal_value)
	{
		this.deal_value = deal_value;
	}

	public String getDeal_name()
	{
		return deal_name;
	}

	public void setDeal_name(String deal_name)
	{
		this.deal_name = deal_name;
	}

	public Timestamp getLast_updatetime()
	{
		return last_updatetime;
	}

	public void setLast_updatetime(Timestamp last_updatetime)
	{
		this.last_updatetime = last_updatetime;
	}

	public String getRemark()
	{
		return remark;
	}

	public void setRemark(String remark)
	{
		this.remark = remark;
	}

	public String getUpload_tag()
	{
		return upload_tag;
	}

	public void setUpload_tag(String upload_tag)
	{
		this.upload_tag = upload_tag;
	}

	public String getIs_cancel()
	{
		return is_cancel;
	}

	public void setIs_cancel(String is_cancel)
	{
		this.is_cancel = is_cancel;
	}


    public String getClass_id() {
        return class_id;
    }

    public void setClass_id(String class_id) {
        this.class_id = class_id;
    }

    public String getType_id() {
        return type_id;
    }

    public void setType_id(String type_id) {
        this.type_id = type_id;
    }

    public Double getDiscount_money()
	{
		return discount_money;
	}

	public void setDiscount_money(Double discount_money)
	{
		this.discount_money = discount_money;
	}

	public Double getDiscount_num()
	{
		return discount_num;
	}

	public void setDiscount_num(Double discount_num)
	{
		this.discount_num = discount_num;
	}

	public String getChanel()
	{
		return chanel;
	}

	public void setChanel(String chanel)
	{
		this.chanel = chanel;
	}

	public Double getPrice()
	{
		return price;
	}

	public void setPrice(Double price)
	{
		this.price = price;
	}

	public Integer getItem_id()
	{
		return item_id;
	}

	public void setItem_id(Integer item_id)
	{
		this.item_id = item_id;
	}

	public Integer getItem_num()
	{
		return item_num;
	}

	public void setItem_num(Integer item_num)
	{
		this.item_num = item_num;
	}

	public String getCoupons_pro()
	{
		return coupons_pro;
	}

	public void setCoupons_pro(String coupons_pro)
	{
		this.coupons_pro = coupons_pro;
	}

	public Integer getCoupon_type()
	{
		return coupon_type;
	}

	public void setCoupon_type(Integer coupon_type)
	{
		this.coupon_type = coupon_type;
	}

	public Double getCoupon_buy_price()
	{
		return coupon_buy_price;
	}

	public void setCoupon_buy_price(Double coupon_buy_price)
	{
		this.coupon_buy_price = coupon_buy_price;
	}

	public Double getDue()
	{
		return due;
	}

	public void setDue(Double due)
	{
		this.due = due;
	}

	public Double getTenancy_assume()
	{
		return tenancy_assume;
	}

	public void setTenancy_assume(Double tenancy_assume)
	{
		this.tenancy_assume = tenancy_assume;
	}

	public Double getThird_assume()
	{
		return third_assume;
	}

	public void setThird_assume(Double third_assume)
	{
		this.third_assume = third_assume;
	}

	public Double getThird_fee()
	{
		return third_fee;
	}

	public void setThird_fee(Double third_fee)
	{
		this.third_fee = third_fee;
	}

	public Integer getRequest_state()
	{
		return request_state;
	}

	public void setRequest_state(Integer request_state)
	{
		this.request_state = request_state;
	}

	public Integer getRwid()
	{
		return rwid;
	}

	public void setRwid(Integer rwid)
	{
		this.rwid = rwid;
	}

	public Integer getItem_unit_id()
	{
		return item_unit_id;
	}

	public void setItem_unit_id(Integer item_unit_id)
	{
		this.item_unit_id = item_unit_id;
	}

	public Double getMore_coupon()
	{
		return more_coupon;
	}

	public void setMore_coupon(Double more_coupon)
	{
		this.more_coupon = more_coupon;
	}

	public Double getReal_assume()
	{
		return real_assume;
	}

	public void setReal_assume(Double real_assume)
	{
		this.real_assume = real_assume;
	}

	public String getRwids() {
		return rwids;
	}

	public void setRwids(String rwids) {
		this.rwids = rwids;
	}

	public static PosBillPaymentCoupons getBean(JSONObject paramJson) throws Exception
	{
		PosBillPaymentCoupons bean = new PosBillPaymentCoupons();

		Class<? extends PosBillPaymentCoupons> beanClass = bean.getClass();

		Field[] fields = beanClass.getDeclaredFields();
		for (Field f : fields)
		{
			String type = f.getType().toString();

			if (type.endsWith("String"))
			{
				f.set(bean, ParamUtil.getStringValueByObject(paramJson, f.getName()));
			}
			else if (type.endsWith("int") || type.endsWith("Integer"))
			{
				f.set(bean, ParamUtil.getIntegerValueByObject(paramJson, f.getName()));
			}
			else if (type.endsWith("double") || type.endsWith("Double"))
			{
				f.set(bean, ParamUtil.getDoubleValueByObject(paramJson, f.getName()));
			}
			else if (type.endsWith("Date"))
			{
				f.set(bean, ParamUtil.getDateValueByObject(paramJson, f.getName()));
			}
			else if (type.endsWith("Timestamp"))
			{
				f.set(bean, ParamUtil.getTimestampValueByObject(paramJson, f.getName()));
			}
			else
			{
				f.set(bean, paramJson.opt(f.getName()));
			}
		}
		return bean;
	}
}
