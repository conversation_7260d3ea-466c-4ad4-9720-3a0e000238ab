package com.tzx.clientorder.wechatprogram.dao.impl;

import java.util.ArrayList;
import java.util.List;

import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import com.tzx.base.entity.PaymentWay;
import com.tzx.base.entity.PosBill;
import com.tzx.base.entity.PosBillDiscount;
import com.tzx.base.entity.PosBillPayment;
import com.tzx.base.entity.PosBillPaymentCoupons;
import com.tzx.base.entity.PosBillService;
import com.tzx.clientorder.wechatprogram.dao.PrePaymentDao;
import com.tzx.member.common.entity.CrmCardTradingListEntity;

import net.sf.json.JSONObject;

@Repository(PrePaymentDao.NAME)
public class PrePaymentDaoImp extends PromptGeneralDaoImp implements PrePaymentDao
{
	@SuppressWarnings("unchecked")
	@Override
	public List<PaymentWay> findPaymentWay(String tenantId, Integer storeId, List<String> paymentClassList) throws Exception
	{
		String paymentClasses = StringUtils.collectionToDelimitedString(paymentClassList, ",", "'", "'");
		if (storeId != null && !StringUtils.isEmpty(paymentClasses))
		{
			StringBuilder sql = new StringBuilder();
			sql.append("select pw.* from payment_way pw LEFT JOIN payment_way_of_ogran pwo on pw.id = pwo.payment_id where pwo.organ_id=").append(storeId).append(" and pw.payment_class in (").append(paymentClasses).append(")");
			return (List<PaymentWay>) this.query(tenantId, sql.toString(), PaymentWay.class);
		}
		return null;
	}

	@Override
	public List<JSONObject> findItemUnit(String tenentId, List<String> itemIdList) throws Exception
	{
		String itemIds = StringUtils.collectionToDelimitedString(itemIdList, ",");
		if (!StringUtils.isEmpty(itemIds))
		{
			StringBuilder sql = new StringBuilder();
			sql.append("select * from hq_item_unit where is_default='Y' and item_id in (").append(itemIds).append(")");
			return this.query4Json(tenentId, sql.toString());
		}
		return null;
	}

	@Override
	public List<JSONObject> getUnitNameList(String tenancyId, int storeId, List<String> duids) throws Exception
	{
		String duidStr = StringUtils.collectionToDelimitedString(duids, ",");
		if (!StringUtils.isEmpty(duidStr))
		{
			StringBuilder sql = new StringBuilder();
			sql.append("select id,unit_name from hq_item_unit where id in (").append(duidStr).append(")");
			return this.query4Json(tenancyId, sql.toString());
		}
		return null;
	}

	@Override
	public List<JSONObject> getItemComboDetails(String tenantId, List<Integer> itemIdList) throws Exception
	{
		String itemIds = StringUtils.collectionToDelimitedString(itemIdList, ",");
		if (!StringUtils.isEmpty(itemIds))
		{
			StringBuilder sql = new StringBuilder();
			sql.append(" select d.* from hq_item_combo_details d where d.iitem_id in (" + itemIds + ")");
			return this.query4Json(tenantId, sql.toString());
		}
		return null;
	}

	@Override
	public void savePosBill(String tenancyId, int storeId, PosBill bill) throws Exception
	{
		StringBuffer saveBill = new StringBuffer(
				"insert into pos_bill(tenancy_id,store_id,bill_num,batch_num,serial_num,report_date,table_code,guest,opentable_time,payment_time,payment_num,open_pos_num,pos_num,waiter_num,open_opt,cashier_num,shift_id,item_menu_id,service_id,service_amount,service_discount,order_num,subtotal,bill_amount,payment_amount,difference,discountk_amount,discountr_amount,maling_amount,single_discount_amount,discount_amount,free_amount,givi_amount,more_coupon,average_amount,discount_num,discount_case_id,discount_rate,discount_mode_id,transfer_remark,sale_mode,bill_state,bill_property,upload_tag,source,payment_state,shop_real_amount,total_fees,platform_charge_amount,bill_taste,fictitious_table,discount_reason_id,dinner_type,coupon_buy_price,due,tenancy_assume,third_assume,third_fee,item_discountr_amount,service_discountr_amount,order_source,payment_source,package_box_fee)");
		saveBill.append(" values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");

		this.update(saveBill.toString(), new Object[]
		{ tenancyId, storeId, bill.getBill_num(), bill.getBatch_num(), bill.getSerial_num(), bill.getReport_date(), bill.getTable_code(), bill.getGuest(), bill.getOpentable_time(), bill.getPayment_time(), bill.getPayment_num(), bill.getOpen_pos_num(), bill.getPos_num(), bill.getWaiter_num(),
				bill.getOpen_opt(), bill.getCashier_num(), bill.getShift_id(), bill.getItem_menu_id(), bill.getService_id(), bill.getService_amount(), bill.getService_discount(), bill.getOrder_num(), bill.getSubtotal(), bill.getBill_amount(), bill.getPayment_amount(), bill.getDifference(),
				bill.getDiscountk_amount(), bill.getDiscountr_amount(), bill.getMaling_amount(), bill.getSingle_discount_amount(), bill.getDiscount_amount(), bill.getFree_amount(), bill.getGivi_amount(), bill.getMore_coupon(), bill.getAverage_amount(), bill.getDiscount_num(),
				bill.getDiscount_case_id(), bill.getDiscount_rate(), bill.getDiscount_mode_id(), bill.getTransfer_remark(), bill.getSale_mode(), bill.getBill_state(), bill.getBill_property(), bill.getUpload_tag(), bill.getSource(), bill.getPayment_state(), bill.getShop_real_amount(),
				bill.getTotal_fees(), bill.getPlatform_charge_amount(), bill.getBill_taste(), bill.getFictitious_table(), bill.getDiscount_reason_id(), bill.getDinner_type(), bill.getCoupon_buy_price(), bill.getDue(), bill.getTenancy_assume(), bill.getThird_assume(), bill.getThird_fee(),
				bill.getItem_discountr_amount(), bill.getService_discountr_amount(), bill.getOrder_source(), bill.getPayment_source(), bill.getPackage_box_fee() });
	}

	@Override
	public void updatePosBillByPay(String tenancyId, int storeId, PosBill bill) throws Exception
	{
		String updateSql = new String("update pos_bill set pos_num=?,cashier_num=?,payment_time=?,payment_source=?,payment_state=?,transfer_remark=? where tenancy_id=? and store_id=? and bill_num=?");
		this.update(updateSql.toString(), new Object[]
		{ bill.getPos_num(), bill.getCashier_num(), bill.getPayment_time(), bill.getPayment_source(), bill.getPayment_state(), bill.getTransfer_remark(), tenancyId, storeId, bill.getBill_num() });
	}

	@Override
	public PosBill getPosBillBeanByBillnum(String tenancyId, int storeId, String billNum) throws Exception
	{
		StringBuilder billAmountSql = new StringBuilder();
		billAmountSql.append(" select b.fictitious_table, b.id,b.waiter_num,coalesce(b.bill_amount,0) as bill_amount,round(coalesce(b.payment_amount,0),2) as payment_amount,coalesce(b.discount_rate,100) as discount_rate,coalesce(b.discount_case_id,0) as discount_case_id,coalesce(b.discount_amount,0) as discount_amount,b.source,b.bill_property");
		billAmountSql.append(" ,coalesce(b.table_code,'') as table_code,coalesce(b.difference,0)  as difference,sale_mode,coalesce(b.order_num,'') as order_num,b.copy_bill_num,b.payment_state,b.batch_num,b.discount_mode_id,b.remark,coalesce(b.recover_count,0) as recover_count,bill_num,payment_time,b.bill_state");
		billAmountSql.append(" ,b.service_amount,b.discountk_amount,b.discountr_amount,b.givi_amount,b.maling_amount,b.subtotal,b.print_count,b.transfer_remark,b.pay_no,b.report_date,b.guest,b.discount_reason_id,b.discount_num,b.pos_num,b.cashier_num,b.shift_id ");
		billAmountSql.append(" from pos_bill b where b.bill_num = ? and b.store_id = ? and b.tenancy_id = ?");

		List<PosBill> list = this.query(billAmountSql.toString(), new Object[]
		{ billNum, storeId, tenancyId }, BeanPropertyRowMapper.newInstance(PosBill.class));

		if (list != null && list.size() > 0)
		{
			return list.get(0);
		}
		return null;
	}

	/**
	 * 根据小程序out_order_id 与pos_bill 中order_num 查询账单号---小程序先付跳单问题
	 * 
	 * @param tenantId
	 * @param storeId
	 * @param outOrderId
	 * @return
	 */
	public JSONObject getBillNumByOutOrderId(String tenantId, Integer storeId, String outOrderId) throws Exception
	{
		StringBuilder querySql = new StringBuilder();
		JSONObject jsonObject = null;
		querySql.append(" select bill_num,serial_num from pos_bill where tenancy_id=? and store_id=? and order_num=?");
		List<JSONObject> list = this.query4Json(tenantId, querySql.toString(), new Object[]
		{ tenantId, storeId, outOrderId });
		if (list != null && list.size() > 0)
		{
			jsonObject = list.get(0);
		}
		return jsonObject;
	}

	@Override
	public void savePosBillService(String tenancyId, int storeId, List<PosBillService> serviceList) throws Exception
	{
		if (serviceList == null || serviceList.size() <= 0)
		{
			return;
		}
		
		StringBuilder sql = new StringBuilder();
		sql.append("INSERT INTO pos_bill_service(tenancy_id,store_id,bill_num,table_code,stable_code,service_id,service_type,taken_mode,service_scale,service_amount,service_count,service_total,service_rate,upload_tag) VALUES");
		for (int i = 0; i < serviceList.size(); i++)
		{
			if (i != 0)
			{
				sql.append(",");
			}
			sql.append("(?,?,?,?,?,?,?,?,?,?,?,?,?,?)");
		}
		
		List<Object> paramList = new ArrayList<>();
		for (PosBillService serviceBean : serviceList)
		{
			paramList.add(serviceBean.getTenancy_id());
			paramList.add(serviceBean.getStore_id());
			paramList.add(serviceBean.getBill_num());
			paramList.add(serviceBean.getTable_code());
			paramList.add(serviceBean.getStable_code());
			paramList.add(serviceBean.getService_id());
			paramList.add(serviceBean.getService_type());
			paramList.add(serviceBean.getTaken_mode());
			paramList.add(serviceBean.getService_scale());
			paramList.add(serviceBean.getService_amount());
			paramList.add(serviceBean.getService_count());
			paramList.add(serviceBean.getService_total());
			paramList.add(serviceBean.getService_rate());
			paramList.add(serviceBean.getUpload_tag());
		}
		this.update(sql.toString(), paramList.toArray());
	}

	@Override
	public void savePosBillDiscount(String tenancyId, int storeId, List<PosBillDiscount> discountList) throws Exception
	{

		if (discountList == null || discountList.size() <= 0)
		{
			return;
		}
		StringBuilder sql = new StringBuilder();
		sql.append(
				"INSERT INTO pos_bill_discount(tenancy_id,store_id,report_date,pos_num,opt_num,bill_num,discount_type,discount_mode,discount_label,discount_amount,discount_count,payment_uid,last_updatetime,upload_tag) VALUES");
		for (int i = 0; i < discountList.size(); i++)
		{
			if (i != 0)
			{
				sql.append(",");
			}
			sql.append("(?,?,?,?,?,?,?,?,?,?,?,?,?,?)");
		}
		List<Object> paramList = new ArrayList<>();
		for (PosBillDiscount discountBean : discountList)
		{
			paramList.add(discountBean.getTenancy_id());
			paramList.add(discountBean.getStore_id());
			paramList.add(discountBean.getReport_date());
			paramList.add(discountBean.getPos_num());
			paramList.add(discountBean.getOpt_num());
			paramList.add(discountBean.getBill_num());
			paramList.add(discountBean.getDiscount_type());
			paramList.add(discountBean.getDiscount_mode());
			paramList.add(discountBean.getDiscount_label());
			paramList.add(discountBean.getDiscount_amount());
			paramList.add(discountBean.getDiscount_count());
			paramList.add(discountBean.getPayment_uid());
			paramList.add(discountBean.getLast_updatetime());
			paramList.add(discountBean.getUpload_tag());
		}
		this.update(sql.toString(), paramList.toArray());
	}

	@Override
	public void savePosBillPayment(List<PosBillPayment> list) throws Exception
	{
		if (list == null || list.size() <= 0)
		{
			return;
		}
		StringBuilder sql = new StringBuilder();
		sql.append(
				"INSERT INTO pos_bill_payment ( tenancy_id, store_id, bill_num, table_code, type, jzid, name, name_english, amount, count, number, phone, report_date, shift_id, pos_num, cashier_num, last_updatetime, is_ysk, rate, currency_amount, upload_tag, customer_id, bill_code, remark, payment_state, param_cach, batch_num, more_coupon, fee, fee_rate, coupon_type, yjzid, coupon_buy_price, due, tenancy_assume, third_assume, third_fee,payment_uid ) VALUES");
		for (int i = 0; i < list.size(); i++)
		{
			if (i != 0)
			{
				sql.append(",");
			}
			sql.append("(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");
		}
		List<Object> paramList = new ArrayList<>();
		for (PosBillPayment posBillPayment : list)
		{
			paramList.add(posBillPayment.getTenancy_id());
			paramList.add(posBillPayment.getStore_id());
			paramList.add(posBillPayment.getBill_num());
			paramList.add(posBillPayment.getTable_code());
			paramList.add(posBillPayment.getType());
			paramList.add(posBillPayment.getJzid());
			paramList.add(posBillPayment.getName());
			paramList.add(posBillPayment.getName_english());
			paramList.add(posBillPayment.getAmount());
			paramList.add(posBillPayment.getCount());
			paramList.add(posBillPayment.getNumber());
			paramList.add(posBillPayment.getPhone());
			paramList.add(posBillPayment.getReport_date());
			paramList.add(posBillPayment.getShift_id());
			paramList.add(posBillPayment.getPos_num());
			paramList.add(posBillPayment.getCashier_num());
			paramList.add(posBillPayment.getLast_updatetime());
			paramList.add(posBillPayment.getIs_ysk());
			paramList.add(posBillPayment.getRate());
			paramList.add(posBillPayment.getCurrency_amount());
			paramList.add(posBillPayment.getUpload_tag());
			paramList.add(posBillPayment.getCustomer_id());
			paramList.add(posBillPayment.getBill_code());
			paramList.add(posBillPayment.getRemark());
			paramList.add(posBillPayment.getPayment_state());
			paramList.add(posBillPayment.getParam_cach());
			paramList.add(posBillPayment.getBatch_num());
			paramList.add(posBillPayment.getMore_coupon());
			paramList.add(posBillPayment.getFee());
			paramList.add(posBillPayment.getFee_rate());
			paramList.add(posBillPayment.getCoupon_type());
			paramList.add(posBillPayment.getYjzid());
			paramList.add(posBillPayment.getCoupon_buy_price());
			paramList.add(posBillPayment.getDue());
			paramList.add(posBillPayment.getTenancy_assume());
			paramList.add(posBillPayment.getThird_assume());
			paramList.add(posBillPayment.getThird_fee());
			paramList.add(posBillPayment.getPayment_uid());
		}
		this.update(sql.toString(), paramList.toArray());
	}

	@Override
	public void savePosBillPaymentCouponsList(List<PosBillPaymentCoupons> list) throws Exception
	{
		if (list == null || list.size() <= 0)
		{
			return;
		}
		StringBuilder sql = new StringBuilder();
		sql.append("INSERT INTO pos_bill_payment_coupons ( tenancy_id, store_id, bill_num, report_date, payment_id, coupons_code, deal_value, deal_name, last_updatetime, remark, upload_tag, is_cancel, class_id, type_id, discount_money, discount_num, chanel, price, item_id, item_num, coupons_pro, coupon_type, coupon_buy_price, due, tenancy_assume, third_assume, third_fee, request_state )values");
		for (int i = 0; i < list.size(); i++)
		{
			if (i != 0)
			{
				sql.append(",");
			}
			sql.append("(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");
		}
		List<Object> paramList = new ArrayList<>();
		for (PosBillPaymentCoupons posBillPaymentCoupons : list)
		{
			paramList.add(posBillPaymentCoupons.getTenancy_id());
			paramList.add(posBillPaymentCoupons.getStore_id());
			paramList.add(posBillPaymentCoupons.getBill_num());
			paramList.add(posBillPaymentCoupons.getReport_date());
			paramList.add(posBillPaymentCoupons.getPayment_id());
			paramList.add(posBillPaymentCoupons.getCoupons_code());
			paramList.add(posBillPaymentCoupons.getDeal_value());
			paramList.add(posBillPaymentCoupons.getDeal_name());
			paramList.add(posBillPaymentCoupons.getLast_updatetime());
			paramList.add(posBillPaymentCoupons.getRemark());
			paramList.add(posBillPaymentCoupons.getUpload_tag());
			paramList.add(posBillPaymentCoupons.getIs_cancel());
			paramList.add(posBillPaymentCoupons.getClass_id());
			paramList.add(posBillPaymentCoupons.getType_id());
			paramList.add(posBillPaymentCoupons.getDiscount_money());
			paramList.add(posBillPaymentCoupons.getDiscount_num());
			paramList.add(posBillPaymentCoupons.getChanel());
			paramList.add(posBillPaymentCoupons.getPrice());
			paramList.add(posBillPaymentCoupons.getItem_id());
			paramList.add(posBillPaymentCoupons.getItem_num());
			paramList.add(posBillPaymentCoupons.getCoupons_pro());
			paramList.add(posBillPaymentCoupons.getCoupon_type());
			paramList.add(posBillPaymentCoupons.getCoupon_buy_price());
			paramList.add(posBillPaymentCoupons.getDue());
			paramList.add(posBillPaymentCoupons.getTenancy_assume());
			paramList.add(posBillPaymentCoupons.getThird_assume());
			paramList.add(posBillPaymentCoupons.getThird_fee());
			paramList.add(posBillPaymentCoupons.getRequest_state());
		}
		this.update(sql.toString(), paramList.toArray());
	}

	@Override
	public void saveCrmCardTradingList(List<CrmCardTradingListEntity> list) throws Exception
	{
		if (list == null || list.size() <= 0)
		{
			return;
		}
		StringBuilder sql = new StringBuilder();
		sql.append(
				"INSERT INTO crm_card_trading_list ( tenancy_id,card_id, card_code, bill_code, chanel, store_id, business_date, main_trading, reward_trading, operat_type, main_original, reward_original, deposit, operator, operate_time, bill_money, third_bill_code, bill_code_original, activity_id, customer_id, revoked_trading, batch_num, last_updatetime, store_updatetime, card_class_id, name, mobil, operator_id, shift_id, total_balance, reward_balance, main_balance, pay_type, salesman, commission_saler_money, commission_store_money, invoice_balance, is_invoice, payment_state, recharge_state, request_status, request_code, request_msg )values");
		for (int i = 0; i < list.size(); i++)
		{
			if (i != 0)
			{
				sql.append(",");
			}
			sql.append("(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");
		}
		List<Object> paramList = new ArrayList<>();
		for (CrmCardTradingListEntity crmCardTradingList : list)
		{
			paramList.add(crmCardTradingList.getTenancy_id());
			paramList.add(crmCardTradingList.getCard_id());
			paramList.add(crmCardTradingList.getCard_code());
			paramList.add(crmCardTradingList.getBill_code());
			paramList.add(crmCardTradingList.getChanel());
			paramList.add(crmCardTradingList.getStore_id());
			paramList.add(crmCardTradingList.getBusiness_date());
			paramList.add(crmCardTradingList.getMain_trading());
			paramList.add(crmCardTradingList.getReward_trading());
			paramList.add(crmCardTradingList.getOperat_type());
			paramList.add(crmCardTradingList.getMain_original());
			paramList.add(crmCardTradingList.getReward_original());
			paramList.add(crmCardTradingList.getDeposit());
			paramList.add(crmCardTradingList.getOperator());
			paramList.add(crmCardTradingList.getOperate_time());
			paramList.add(crmCardTradingList.getBill_money());
			paramList.add(crmCardTradingList.getThird_bill_code());
			paramList.add(crmCardTradingList.getBill_code_original());
			paramList.add(crmCardTradingList.getActivity_id());
			paramList.add(crmCardTradingList.getCustomer_id());
			paramList.add(crmCardTradingList.getRevoked_trading());
			paramList.add(crmCardTradingList.getBatch_num());
			paramList.add(crmCardTradingList.getLast_updatetime());
			paramList.add(crmCardTradingList.getStore_updatetime());
			paramList.add(crmCardTradingList.getCard_class_id());
			paramList.add(crmCardTradingList.getName());
			paramList.add(crmCardTradingList.getMobil());
			paramList.add(crmCardTradingList.getOperator_id());
			paramList.add(crmCardTradingList.getShift_id());
			paramList.add(crmCardTradingList.getTotal_balance());
			paramList.add(crmCardTradingList.getReward_balance());
			paramList.add(crmCardTradingList.getMain_balance());
			paramList.add(crmCardTradingList.getPay_type());
			paramList.add(crmCardTradingList.getSalesman());
			paramList.add(crmCardTradingList.getCommission_saler_money());
			paramList.add(crmCardTradingList.getCommission_store_money());
			paramList.add(crmCardTradingList.getInvoice_balance());
			paramList.add(crmCardTradingList.getIs_invoice());
			paramList.add(crmCardTradingList.getPayment_state());
			paramList.add(crmCardTradingList.getRecharge_state());
			paramList.add(crmCardTradingList.getRequest_status());
			paramList.add(crmCardTradingList.getRequest_code());
			paramList.add(crmCardTradingList.getRequest_msg());
		}
		this.update(sql.toString(), paramList.toArray());
	}

}
