package com.tzx.clientorder.wechatprogram.bo.impl;

import javax.annotation.Resource;

import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import com.tzx.clientorder.wechatprogram.bo.PromptGeneralService;
import com.tzx.clientorder.wechatprogram.common.constant.PromptConstant;
import com.tzx.clientorder.wechatprogram.dao.PromptGenericDao;
import com.tzx.framework.common.constant.SysParameterCode;
import com.tzx.pos.base.constant.SysDictionary;

import net.sf.json.JSONObject;

/**
 * Created by qingui on 2018-06-26.
 */
@Service(PromptGeneralService.NAME)
public class PromptGeneralServiceImp implements PromptGeneralService
{
	private Logger				logger	= Logger.getLogger(PromptGeneralServiceImp.class);

	@Resource(name = PromptGenericDao.name)
	private PromptGenericDao		posGenericDao;

	@Override
	public JSONObject getOrganStatus(String tenancyId, int organId,String channel) throws Exception
	{
		JSONObject responseJson = new JSONObject();
		// logger.info("微生活小程序查询门店是否营业");
		// 判断门店是否启用微生活小程序
		String orderType = posGenericDao.getSysParameter(tenancyId, organId, SysParameterCode.USER_ORDER_TYPE_KEY);
		
		if (SysDictionary.CHANEL_QIMAI.equals(channel))
		{
			if (!SysDictionary.USER_ORDER_TYPE_QM_PROGRAM.equals(orderType))
			{
				responseJson.put(PromptConstant.SUCCESS, 0);
				responseJson.put(PromptConstant.MSG, "门店未启用企迈小程序点餐");
				logger.info("微生活小程序查询门店是否营业的返回结果" + responseJson);
				return responseJson;
			}
		}
		else
		{
			if ("0".equals(orderType))
			{
				responseJson.put(PromptConstant.SUCCESS, 0);
				responseJson.put(PromptConstant.MSG, "门店未启用小程序点餐");
				logger.info("微生活小程序查询门店是否营业的返回结果" + responseJson);
				return responseJson;
			}
		}
		
		JSONObject organ = posGenericDao.getOrganStatus(tenancyId, organId);
		// 未日始的门店服务不可用
		if (null == organ)
		{
			responseJson.put(PromptConstant.SUCCESS, 0);
			responseJson.put(PromptConstant.MSG, "门店服务不可用，查看是否日始");
			logger.info("微生活小程序查询门店是否营业的返回结果" + responseJson);
		}
		else
		{
			responseJson.put(PromptConstant.SUCCESS, 1);
			responseJson.put(PromptConstant.MSG, "门店服务可用");
			logger.info("微生活小程序查询门店是否营业的返回结果" + responseJson);
		}

		// logger.info("微生活小程序查询门店是否营业的返回结果" + responseJson);
		return responseJson;
	}
}
