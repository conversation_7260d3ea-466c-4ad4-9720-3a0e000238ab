package com.tzx.clientorder.wechatprogram.common.entity.program;

import java.util.List;

/**菜品信息
 * <AUTHOR>
 *
 */
public class OrderDishEntity
{
	private Integer					dishid;
	private String					dishno;
	private String					dish_name;
	private Integer					kindid;
	private Integer					unitid;
	private String					unit_name;
	private Double					number;
	private Double					price;
	private Double					member_price;
	private Double					orgprice;
	private List<OrderCookEntity>	cooks;
	private Double					cook_price;
	private String[]				memo;
	private Integer					is_member_price		= 0;
	private Integer					is_bargain_price	= 0;
	private Integer					active_type	= 0;
	private String 					openid;

	public Integer getDishid()
	{
		return dishid;
	}

	public void setDishid(Integer dishid)
	{
		this.dishid = dishid;
	}

	public String getDishno()
	{
		return dishno;
	}

	public void setDishno(String dishno)
	{
		this.dishno = dishno;
	}

	public String getDish_name()
	{
		return dish_name;
	}

	public void setDish_name(String dish_name)
	{
		this.dish_name = dish_name;
	}

	public Integer getKindid()
	{
		return kindid;
	}

	public void setKindid(Integer kindid)
	{
		this.kindid = kindid;
	}

	public Integer getUnitid()
	{
		return unitid;
	}

	public void setUnitid(Integer unitid)
	{
		this.unitid = unitid;
	}

	public String getUnit_name()
	{
		return unit_name;
	}

	public void setUnit_name(String unit_name)
	{
		this.unit_name = unit_name;
	}

	public Double getNumber()
	{
		return number;
	}

	public void setNumber(Double number)
	{
		this.number = number;
	}

	public Double getPrice()
	{
		return price;
	}

	public void setPrice(Double price)
	{
		this.price = price;
	}

	public Double getMember_price()
	{
		return member_price;
	}

	public void setMember_price(Double member_price)
	{
		this.member_price = member_price;
	}

	public Double getOrgprice()
	{
		return orgprice;
	}

	public void setOrgprice(Double orgprice)
	{
		this.orgprice = orgprice;
	}

	public List<OrderCookEntity> getCooks()
	{
		return cooks;
	}

	public void setCooks(List<OrderCookEntity> cooks)
	{
		this.cooks = cooks;
	}

	public Double getCook_price()
	{
		return cook_price;
	}

	public void setCook_price(Double cook_price)
	{
		this.cook_price = cook_price;
	}

	public String[] getMemo()
	{
		return memo;
	}

	public void setMemo(String[] memo)
	{
		this.memo = memo;
	}

	public Integer getIs_member_price()
	{
		return is_member_price;
	}

	public void setIs_member_price(Integer is_member_price)
	{
		this.is_member_price = is_member_price;
	}

	public Integer getIs_bargain_price()
	{
		return is_bargain_price;
	}

	public void setIs_bargain_price(Integer is_bargain_price)
	{
		this.is_bargain_price = is_bargain_price;
	}

	public Integer getActive_type()
	{
		return active_type;
	}

	public void setActive_type(Integer active_type)
	{
		this.active_type = active_type;
	}

	public String getOpenid() {
		return openid;
	}

	public void setOpenid(String openid) {
		this.openid = openid;
	}
}
