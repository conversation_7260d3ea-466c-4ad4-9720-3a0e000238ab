package com.tzx.clientorder.wechatprogram.bo;

import net.sf.json.JSONObject;

import java.util.List;

/**
 * 微生活后付业务层
 */
public interface AfterPaymentService
{
	String NAME = "com.tzx.clientorder.wechatprogram.bo.impl.AfterPaymentServiceImpl";

	/**
	 * 查询门店桌台的订单信息
	 * 
	 * @param jsobj
	 * @return
	 * @throws Exception
	 */
	JSONObject getTableOrder(String tenancyId, Integer storeId, JSONObject jsobj, String channel) throws Exception;

	/**
	 * 下单
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param param
	 * @param printJson
	 * @param responseJson
	 * @throws Exception
	 */
	JSONObject saveOrUpdateOrder(String tenancyId, Integer storeId, JSONObject param, JSONObject printJson, String channel) throws Exception;

	/**
	 * 订单查询
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param param
	 * @param responseJson
	 * @throws Exception
	 */
	JSONObject payUploadBill(String tenancyId, Integer storeId, JSONObject param, String channel) throws Exception;

	/**
	 * 获取订单信息
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param tableCode
	 * @param billObject
	 * @return
	 * @throws Exception
	 */
	JSONObject getOrderInfo(String tenancyId, int storeId, String tableCode, JSONObject billObject) throws Exception;

	/**
	 * 清台
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param param
	 * @param responseJson
	 * @throws Exception
	 */
	JSONObject paymentClose(String tenancyId, Integer storeId, JSONObject param, String channel) throws Exception;

	/**
	 * 锁单
	 * 
	 * @param param
	 * @param responseJson
	 * @throws Exception
	 */
	JSONObject lockOrder(String tenancyId, Integer storeId, JSONObject param) throws Exception;

	/**
	 * 解锁
	 * 
	 * @param param
	 * @param responseJson
	 * @throws Exception
	 */
	JSONObject unlockOrder(String tenancyId, Integer storeId, JSONObject param) throws Exception;

	/**
	 * 获取付款流水
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @return
	 * @throws Exception
	 */
	List<JSONObject> getBilPayment(String tenancyId, int storeId, String billNum) throws Exception;
}
