package com.tzx.clientorder.common.entity;

import java.util.List;

import com.tzx.clientorder.wlifeprogram.common.entity.WlifeDishPromotion;

public class AcewillOrderNormalitem {

	private String id;
	private String did;
	private String dishsno;
	private String name;
	private String duid;
	private String duName;
	private Integer number;
	private Double price;
	private String orgprice;
	private String memberprice;
	private String dishimg;
	private Object cooks;
	private String type;
	private List<String> membergid;
	private Integer bgift;
	private Integer isWeigh;
	private String bbuySno;
	private String bgiftSno;
	private Double bmemberprice;
	private Double bargainprice;
	private Double aprice;
	private String pkid;
	private String remark;
	private String realprice;
	private String maindish;
	private String mandatory;
	private String optional;
	private List<AcewillOrderItemMemo>	memo;
	private Integer xcxUnionId;
	private String batchid;
	private WlifeDishPromotion dishPromotion;

	public Integer getXcxUnionId() {
		return xcxUnionId;
	}

	public void setXcxUnionId(Integer xcxUnionId) {
		this.xcxUnionId = xcxUnionId;
	}

	public String getId()
	{
		return id;
	}

	public void setId(String id)
	{
		this.id = id;
	}

	public String getDid() {
		return did;
	}
	public void setDid(String did) {
		this.did = did;
	}
	public String getDishsno() {
		return dishsno;
	}
	public void setDishsno(String dishsno) {
		this.dishsno = dishsno;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getDuid() {
		return duid;
	}
	public void setDuid(String duid) {
		this.duid = duid;
	}
	public Integer getNumber() {
		return number;
	}
	public void setNumber(Integer number) {
		this.number = number;
	}
	public Double getPrice() {
		return price;
	}
	public void setPrice(Double price) {
		this.price = price;
	}
	public String getOrgprice() {
		return orgprice;
	}
	public void setOrgprice(String orgprice) {
		this.orgprice = orgprice;
	}
	public String getMemberprice() {
		return memberprice;
	}
	public void setMemberprice(String memberprice) {
		this.memberprice = memberprice;
	}
	public String getDishimg() {
		return dishimg;
	}
	public void setDishimg(String dishimg) {
		this.dishimg = dishimg;
	}
	public Object getCooks() {
		return cooks;
	}
	public void setCooks(Object cooks) {
		this.cooks = cooks;
	}
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
	public List<String> getMembergid() {
		return membergid;
	}
	public void setMembergid(List<String> membergid) {
		this.membergid = membergid;
	}
	public Integer getBgift() {
		return bgift;
	}
	public void setBgift(Integer bgift) {
		this.bgift = bgift;
	}
	public Integer getIsWeigh() {
		return isWeigh;
	}
	public void setIsWeigh(Integer isWeigh) {
		this.isWeigh = isWeigh;
	}
	public String getBbuySno() {
		return bbuySno;
	}
	public void setBbuySno(String bbuySno) {
		this.bbuySno = bbuySno;
	}
	public String getBgiftSno() {
		return bgiftSno;
	}
	public void setBgiftSno(String bgiftSno) {
		this.bgiftSno = bgiftSno;
	}
	public Double getBmemberprice() {
		return bmemberprice;
	}
	public void setBmemberprice(Double bmemberprice) {
		this.bmemberprice = bmemberprice;
	}
	public Double getBargainprice() {
		return bargainprice;
	}
	public void setBargainprice(Double bargainprice) {
		this.bargainprice = bargainprice;
	}
	public Double getAprice() {
		return aprice;
	}
	public void setAprice(Double aprice) {
		this.aprice = aprice;
	}
	public String getPkid() {
		return pkid;
	}
	public void setPkid(String pkid) {
		this.pkid = pkid;
	}
	public String getRemark() {
		return remark;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}
	public String getRealprice() {
		return realprice;
	}
	public void setRealprice(String realprice) {
		this.realprice = realprice;
	}
	public String getMaindish() {
		return maindish;
	}
	public void setMaindish(String maindish) {
		this.maindish = maindish;
	}
	public String getMandatory() {
		return mandatory;
	}
	public void setMandatory(String mandatory) {
		this.mandatory = mandatory;
	}
	public String getOptional() {
		return optional;
	}
	public void setOptional(String optional) {
		this.optional = optional;
	}
	public String getDuName() {
		return duName;
	}
	public void setDuName(String duName) {
		this.duName = duName;
	}
	public List<AcewillOrderItemMemo> getMemo()
	{
		return memo;
	}
	public void setMemo(List<AcewillOrderItemMemo> memo)
	{
		this.memo = memo;
	}
	
	public String getItemMemo()
	{
		if (null != this.memo && 0 < this.memo.size())
		{
			StringBuilder itemTaste = new StringBuilder();
			for (AcewillOrderItemMemo itemMemo : memo)
			{
				if (null != itemMemo.getItems() && 0 < itemMemo.getItems().size())
				{
					for (AcewillOrderItemMemoDetail itemMemoDetail : itemMemo.getItems())
					{
						if (0 < itemTaste.length())
						{
							itemTaste.append(",");
						}
						itemTaste.append(itemMemoDetail.getOrdermemo());
					}
				}
			}
			return itemTaste.toString();
		}
		else
		{
			return this.remark;
		}
	}

	public WlifeDishPromotion getDishPromotion()
	{
		return dishPromotion;
	}

	public void setDishPromotion(WlifeDishPromotion dishPromotion)
	{
		this.dishPromotion = dishPromotion;
	}

	public String getBatchid()
	{
		return batchid;
	}

	public void setBatchid(String batchid)
	{
		this.batchid = batchid;
	}
}
