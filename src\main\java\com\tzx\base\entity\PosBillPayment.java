package com.tzx.base.entity;

import java.lang.reflect.Field;
import java.sql.Timestamp;
import java.util.Date;

import org.apache.commons.beanutils.BeanUtils;

import com.tzx.framework.common.util.CommonUtil;
import com.tzx.pos.base.util.ParamUtil;

import net.sf.json.JSONObject;

/**
 * 账单付款明细表映射实体
 * 
 * <AUTHOR>
 * <AUTHOR>
 */
public class PosBillPayment
{
	private String		tenancy_id;
	private Integer		store_id;
	private Integer		id;
	private Date		report_date;
	private Integer		shift_id;
	private String		pos_num;
	private String		cashier_num;
	private String		bill_num;
	private String		batch_num;
	private String		table_code;
	private String		type;
	private Integer		jzid;
	private Integer		yjzid;
	private String		name;
	private String		name_english;
	private Double		amount;
	private Double		currency_amount;
	private Integer		count				= 1;
	private String		number;
	private String		phone;
	private Timestamp	last_updatetime;
	private String		is_ysk				= "N";
	private Double		rate				= 1d;
	private Integer		upload_tag			= 0;
	private Integer		customer_id;
	private String		bill_code;
	private String		remark;
	private String		payment_state;
	private String		param_cach;
	private Double		more_coupon			= 0d;
	private Double		fee;
	private Double		fee_rate;
	private Integer		coupon_type;
	private Double		coupon_buy_price	= 0d;
	private Double		due					= 0d;
	private Double		tenancy_assume		= 0d;
	private Double		third_assume		= 0d;
	private Double		third_fee			= 0d;
	private String		payment_uid;
	private String		out_trade_no;

	public PosBillPayment()
	{
		super();
	}

	public PosBillPayment(String tenancy_id, Integer store_id, Date report_date, Integer shift_id, String pos_num, String cashier_num, String bill_num, String batch_num, String table_code, String type, Integer jzid, String name, String name_english, Double amount, Double currency_amount,
			Integer count, String number, String phone, Timestamp last_updatetime, String payment_state)
	{
		super();
		this.tenancy_id = tenancy_id;
		this.store_id = store_id;
		this.report_date = report_date;
		this.shift_id = shift_id;
		this.pos_num = pos_num;
		this.cashier_num = cashier_num;
		this.bill_num = bill_num;
		this.batch_num = batch_num;
		this.table_code = table_code;
		this.type = type;
		this.jzid = jzid;
		this.name = name;
		this.name_english = name_english;
		this.amount = amount;
		this.currency_amount = currency_amount;
		this.count = count;
		this.number = number;
		this.phone = phone;
		this.last_updatetime = last_updatetime;
		this.payment_state = payment_state;
	}

	public String getTenancy_id()
	{
		return tenancy_id;
	}

	public void setTenancy_id(String tenancy_id)
	{
		this.tenancy_id = tenancy_id;
	}

	public Integer getStore_id()
	{
		return store_id;
	}

	public void setStore_id(Integer store_id)
	{
		this.store_id = store_id;
	}

	public Integer getId()
	{
		return id;
	}

	public void setId(Integer id)
	{
		this.id = id;
	}

	public String getBill_num()
	{
		return bill_num;
	}

	public void setBill_num(String bill_num)
	{
		this.bill_num = bill_num;
	}

	public String getTable_code()
	{
		return table_code;
	}

	public void setTable_code(String table_code)
	{
		this.table_code = table_code;
	}

	public String getType()
	{
		return type;
	}

	public void setType(String type)
	{
		this.type = type;
	}

	public Integer getJzid()
	{
		return jzid;
	}

	public void setJzid(Integer jzid)
	{
		this.jzid = jzid;
	}

	public String getName()
	{
		return name;
	}

	public void setName(String name)
	{
		this.name = name;
	}

	public String getName_english()
	{
		return name_english;
	}

	public void setName_english(String name_english)
	{
		this.name_english = name_english;
	}

	public Double getAmount()
	{
		if (null != amount && amount.isNaN())
		{
			return 0d;
		}
		return amount;
	}

	public void setAmount(Double amount)
	{
		this.amount = amount;
	}

	public Integer getCount()
	{
		return count;
	}

	public void setCount(Integer count)
	{
		this.count = count;
	}

	public String getNumber()
	{
		return number;
	}

	public void setNumber(String number)
	{
		this.number = number;
	}

	public String getPhone()
	{
		return phone;
	}

	public void setPhone(String phone)
	{
		this.phone = phone;
	}

	public Date getReport_date()
	{
		return report_date;
	}

	public void setReport_date(Date report_date)
	{
		this.report_date = report_date;
	}

	public Integer getShift_id()
	{
		return shift_id;
	}

	public void setShift_id(Integer shift_id)
	{
		this.shift_id = shift_id;
	}

	public String getPos_num()
	{
		return pos_num;
	}

	public void setPos_num(String pos_num)
	{
		this.pos_num = pos_num;
	}

	public String getCashier_num()
	{
		return cashier_num;
	}

	public void setCashier_num(String cashier_num)
	{
		this.cashier_num = cashier_num;
	}

	public Timestamp getLast_updatetime()
	{
		return last_updatetime;
	}

	public void setLast_updatetime(Timestamp last_updatetime)
	{
		this.last_updatetime = last_updatetime;
	}

	public String getIs_ysk()
	{
		return is_ysk;
	}

	public void setIs_ysk(String is_ysk)
	{
		this.is_ysk = is_ysk;
	}

	public Double getRate()
	{
		return rate;
	}

	public void setRate(Double rate)
	{
		this.rate = rate;
	}

	public Double getCurrency_amount()
	{
		if (null != currency_amount && currency_amount.isNaN())
		{
			return 0d;
		}
		return currency_amount;
	}

	public void setCurrency_amount(Double currency_amount)
	{
		this.currency_amount = currency_amount;
	}

	public Integer getUpload_tag()
	{
		return upload_tag;
	}

	public void setUpload_tag(Integer upload_tag)
	{
		this.upload_tag = upload_tag;
	}

	public Integer getCustomer_id()
	{
		return customer_id;
	}

	public void setCustomer_id(Integer customer_id)
	{
		this.customer_id = customer_id;
	}

	public String getBill_code()
	{
		return bill_code;
	}

	public void setBill_code(String bill_code)
	{
		this.bill_code = bill_code;
	}

	public String getRemark()
	{
		return remark;
	}

	public void setRemark(String remark)
	{
		this.remark = remark;
	}

	public String getPayment_state()
	{
		return payment_state;
	}

	public void setPayment_state(String payment_state)
	{
		this.payment_state = payment_state;
	}

	public String getParam_cach()
	{
		return param_cach;
	}

	public void setParam_cach(String param_cach)
	{
		this.param_cach = param_cach;
	}

	public String getBatch_num()
	{
		return batch_num;
	}

	public void setBatch_num(String batch_num)
	{
		this.batch_num = batch_num;
	}

	public Double getMore_coupon()
	{
		if (null != more_coupon && more_coupon.isNaN())
		{
			return 0d;
		}
		return more_coupon;
	}

	public void setMore_coupon(Double more_coupon)
	{
		this.more_coupon = more_coupon;
	}

	public Double getFee()
	{
		return fee;
	}

	public void setFee(Double fee)
	{
		this.fee = fee;
	}

	public Double getFee_rate()
	{
		return fee_rate;
	}

	public void setFee_rate(Double fee_rate)
	{
		this.fee_rate = fee_rate;
	}

	public Integer getYjzid()
	{
		return yjzid;
	}

	public void setYjzid(Integer yjzid)
	{
		this.yjzid = yjzid;
	}

	public Integer getCoupon_type()
	{
		return coupon_type;
	}

	public void setCoupon_type(Integer coupon_type)
	{
		this.coupon_type = coupon_type;
	}

	public Double getCoupon_buy_price()
	{
		if (null != coupon_buy_price && coupon_buy_price.isNaN())
		{
			return 0d;
		}
		return coupon_buy_price;
	}

	public void setCoupon_buy_price(Double coupon_buy_price)
	{
		this.coupon_buy_price = coupon_buy_price;
	}

	public Double getDue()
	{
		if (null != due && due.isNaN())
		{
			return 0d;
		}
		return due;
	}

	public void setDue(Double due)
	{
		this.due = due;
	}

	public Double getTenancy_assume()
	{
		if (null != tenancy_assume && tenancy_assume.isNaN())
		{
			return 0d;
		}
		return tenancy_assume;
	}

	public void setTenancy_assume(Double tenancy_assume)
	{
		this.tenancy_assume = tenancy_assume;
	}

	public Double getThird_assume()
	{
		if (null != third_assume && third_assume.isNaN())
		{
			return 0d;
		}
		return third_assume;
	}

	public void setThird_assume(Double third_assume)
	{
		this.third_assume = third_assume;
	}

	public Double getThird_fee()
	{
		if (null != third_fee && third_fee.isNaN())
		{
			return 0d;
		}
		return third_fee;
	}

	public void setThird_fee(Double third_fee)
	{
		this.third_fee = third_fee;
	}

	public String getPayment_uid()
	{
		return payment_uid;
	}

	public void setPayment_uid(String payment_uid)
	{
		this.payment_uid = payment_uid;
	}

	public String getOut_trade_no()
	{
		return out_trade_no;
	}

	public void setOut_trade_no(String out_trade_no)
	{
		this.out_trade_no = out_trade_no;
	}
	
	public JSONObject getParamCachJson()
	{
		if (CommonUtil.hv(param_cach))
		{
			return JSONObject.fromObject(param_cach);
		}
		return new JSONObject();
	}

	public void setParamCach(String key, Object val)
	{
		JSONObject paramCachJson = this.getParamCachJson();
		paramCachJson.put(key, val);
		this.param_cach = paramCachJson.toString();
	}

	public PosBillPayment deepCopy()
	{
		PosBillPayment bean = null;
		try
		{
			bean = (PosBillPayment) BeanUtils.cloneBean(this);
			bean.setId(null);
		}
		catch (Exception e)
		{
			//e.printStackTrace();
		}
		return bean;
	}
	
	public static PosBillPayment getBean(JSONObject paramJson) throws Exception
	{
		PosBillPayment bean = new PosBillPayment();

		Class<? extends PosBillPayment> beanClass = bean.getClass();

		Field[] fields = beanClass.getDeclaredFields();
		for (Field f : fields)
		{
			String type = f.getType().toString();

			if (type.endsWith("String"))
			{
				f.set(bean, ParamUtil.getStringValueByObject(paramJson, f.getName()));
			}
			else if (type.endsWith("int") || type.endsWith("Integer"))
			{
				f.set(bean, ParamUtil.getIntegerValueByObject(paramJson, f.getName()));
			}
			else if (type.endsWith("double") || type.endsWith("Double"))
			{
				f.set(bean, ParamUtil.getDoubleValueByObject(paramJson, f.getName()));
			}
			else if (type.endsWith("Date"))
			{
				f.set(bean, ParamUtil.getDateValueByObject(paramJson, f.getName()));
			}
			else if (type.endsWith("Timestamp"))
			{
				f.set(bean, ParamUtil.getTimestampValueByObject(paramJson, f.getName()));
			}
			else
			{
				f.set(bean, paramJson.opt(f.getName()));
			}
		}
		return bean;
	}
}
