package com.tzx.clientorder.common.constant;

/**
 * 提供微生活对接的url等常量信息
 * Created by qin-<PERSON>ui on 2018-03-13.
 */
public class WLifeConstant {

	/** 上传订单操作状态:微信点餐新订单 */
	public static final String	OPT_TYPE_NEW		= "1";
	/** 上传订单操作状态:加菜 */
	public static final String	OPT_TYPE_ADD		= "2";
	/** 上传订单操作状态:拉取订单 */
	public static final String	OPT_TYPE_LOAD		= "7";
	/** 上传订单操作状态:锁单上传订单 */
	public static final String	OPT_TYPE_LOCK		= "10";
	/** 上传订单操作状态:预结上传订单 */
	public static final String	OPT_TYPE_PAY		= "11";

	/** 桌态表的锁单状态，对应字段  */
	public static final String  LOCK_POS_NUM		="-1";
	public static final String  OPT_NAME			="微信锁单";
	public static final String  LOCK_OPT_NUM		="-1";

	/** 订单来源：支付宝  */
	public static final String PAY_SOURCE_ALIPAY = "alipay";
	/** 订单来源：微信  */
	public static final String PAY_SOURCE_WEIXIN = "weixin";
	/** 订单来源：微生活储值  */
	public static final String PAY_SOURCE_BALANCE = "balance";
	/** 订单来源：微生活积分  */
	public static final String PAY_SOURCE_CREDIT = "credit";
	/** 订单来源：微生活代金卷 */
	public static final String PAY_SOURCE_COUPON = "coupon";
	/** 订单来源：微生菜品卷  */
	public static final String PAY_SOURCE_PRODUCT = "product";
	/** 订单来源：微生活支付 */
	public static final String PAY_SOURCE_WLIFE = "wlife";
	/** 订单来源：美味不用等支付宝  */
	public static final String PAY_SOURCE_MEIWEI_ALIPAY = "ali_pay_meiwei";
	/** 订单来源：美味不用等微信  */
	public static final String PAY_SOURCE_MEIWEI_WEIXIN = "wechat_pay_meiwei";
	
	
}
