package com.tzx.member.yazuo.request.vo.impl;

import java.util.List;

import com.tzx.member.yazuo.common.entity.YazuoPayInfoEntity;
import com.tzx.member.yazuo.request.vo.YazuoParam;

/**
 * 6.24 新-会员卡消费接口
 * 
 * <AUTHOR>
 *
 */
public class CardConsumVo extends YazuoParam
{
	private String						merchantNo;
	private String						terminalNo;
	private String						cardNo;
	private String						mobile;
	private String						cashierSerial;
	private String						password;
	private Double						billAmount;//账单实付金额
	private Double						cashValue				= 0d;	// 现金额(此处金额为扣除优惠、积分、储值外的实收现金部分)
	private Double						integralValue			= 0d;	// 需扣除的雅座积分额
	private Double						storeValue				= 0d;	// 需扣除的雅座储值额
	private List<Object[]>				ticketInfoArray;				// 本次使用的雅座优惠券信息列表（id，张数，优惠金额）
	private List<Object[]>				dishArray;						// 菜品列表本次消费菜品信息列表,每项包含（菜品编码,菜品中文名,菜品数量，实付金额，应付金额，券id)
	private Integer						payway;
	private String						remark;
	private Integer						source;
	private String						thirdMerchant;					// 开票门店编号
	private Double						undiscountableAmount	= 0d;	// 现金消费中不参与优惠金额的部分
	private String						orderId;
	private List<YazuoPayInfoEntity>	mixPayList;
	private String						financeDate;
	private boolean						settleFlag;
	private String						voucherNumber;
	private Double						integralAmount;
	private String						transOrderTime;

	public String getMerchantNo()
	{
		return merchantNo;
	}

	public void setMerchantNo(String merchantNo)
	{
		this.merchantNo = merchantNo;
	}

	public String getTerminalNo()
	{
		return terminalNo;
	}

	public void setTerminalNo(String terminalNo)
	{
		this.terminalNo = terminalNo;
	}

	public String getCardNo()
	{
		return cardNo;
	}

	public void setCardNo(String cardNo)
	{
		this.cardNo = cardNo;
	}

	public String getMobile()
	{
		return mobile;
	}

	public void setMobile(String mobile)
	{
		this.mobile = mobile;
	}

	public String getCashierSerial()
	{
		return cashierSerial;
	}

	public void setCashierSerial(String cashierSerial)
	{
		this.cashierSerial = cashierSerial;
	}

	public String getPassword()
	{
		return password;
	}

	public void setPassword(String password)
	{
		this.password = password;
	}

	public Double getCashValue()
	{
		return cashValue;
	}

	public void setCashValue(Double cashValue)
	{
		this.cashValue = cashValue;
	}

	public Double getIntegralValue()
	{
		return integralValue;
	}

	public void setIntegralValue(Double integralValue)
	{
		this.integralValue = integralValue;
	}

	public Double getStoreValue()
	{
		return storeValue;
	}

	public void setStoreValue(Double storeValue)
	{
		this.storeValue = storeValue;
	}

	public List<Object[]> getTicketInfoArray()
	{
		return ticketInfoArray;
	}

	public void setTicketInfoArray(List<Object[]> ticketInfoArray)
	{
		this.ticketInfoArray = ticketInfoArray;
	}

	public List<Object[]> getDishArray()
	{
		return dishArray;
	}

	public void setDishArray(List<Object[]> dishArray)
	{
		this.dishArray = dishArray;
	}

	public Integer getPayway()
	{
		return payway;
	}

	public void setPayway(Integer payway)
	{
		this.payway = payway;
	}

	public String getRemark()
	{
		return remark;
	}

	public void setRemark(String remark)
	{
		this.remark = remark;
	}

	public Integer getSource()
	{
		return source;
	}

	public void setSource(Integer source)
	{
		this.source = source;
	}

	public String getThirdMerchant()
	{
		return thirdMerchant;
	}

	public void setThirdMerchant(String thirdMerchant)
	{
		this.thirdMerchant = thirdMerchant;
	}

	public Double getUndiscountableAmount()
	{
		return undiscountableAmount;
	}

	public void setUndiscountableAmount(Double undiscountableAmount)
	{
		this.undiscountableAmount = undiscountableAmount;
	}

	public String getOrderId()
	{
		return orderId;
	}

	public void setOrderId(String orderId)
	{
		this.orderId = orderId;
	}

	public String getFinanceDate()
	{
		return financeDate;
	}

	public void setFinanceDate(String financeDate)
	{
		this.financeDate = financeDate;
	}

	public List<YazuoPayInfoEntity> getMixPayList()
	{
		return mixPayList;
	}

	public void setMixPayList(List<YazuoPayInfoEntity> mixPayList)
	{
		this.mixPayList = mixPayList;
	}

	public boolean isSettleFlag()
	{
		return settleFlag;
	}

	public void setSettleFlag(boolean settleFlag)
	{
		this.settleFlag = settleFlag;
	}

	public String getVoucherNumber()
	{
		return voucherNumber;
	}

	public void setVoucherNumber(String voucherNumber)
	{
		this.voucherNumber = voucherNumber;
	}

	public Double getIntegralAmount()
	{
		return integralAmount;
	}

	public void setIntegralAmount(Double integralAmount)
	{
		this.integralAmount = integralAmount;
	}

	public String getTransOrderTime()
	{
		return transOrderTime;
	}

	public void setTransOrderTime(String transOrderTime)
	{
		this.transOrderTime = transOrderTime;
	}

	public Double getBillAmount()
	{
		return billAmount;
	}

	public void setBillAmount(Double billAmount)
	{
		this.billAmount = billAmount;
	}
}
