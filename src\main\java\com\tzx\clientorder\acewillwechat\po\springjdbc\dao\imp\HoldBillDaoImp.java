package com.tzx.clientorder.acewillwechat.po.springjdbc.dao.imp;

import com.tzx.framework.common.util.DateUtil;
import com.tzx.framework.common.util.dao.impl.GenericDaoImpl;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.clientorder.acewillwechat.po.springjdbc.dao.HoldBillDao;
import com.tzx.clientorder.common.constant.WLifeConstant;

import net.sf.json.JSONObject;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by qin-gui on 2018-03-14.
 */
@Repository(HoldBillDao.NAME)
public class HoldBillDaoImp extends GenericDaoImpl implements HoldBillDao {

    @Override
    public JSONObject getBillLockStatus(String tenancyId, String orderNum) throws Exception{
        StringBuffer sql = new StringBuffer();
        sql.append(" select pb.bill_num,t.table_code,(case when t.lock_pos_num is null or t.lock_pos_num = '' then 0 else 1 end) as lock_state,coalesce(pbl.lock_type,'MD01') lock_type,(pbl.id is null) is_null_lock ")
            .append(" from pos_bill pb")
            .append(" left join pos_tablestate t on pb.store_id = t.store_id and pb.table_code = t.table_code")
            .append(" left join pos_bill_lock pbl on pb.bill_num = pbl.bill_num and pb.store_id = pbl.store_id")
            .append(" where pb.order_num = '"+ orderNum +"' and pb.bill_property = 'OPEN'");
        List<JSONObject> list = this.query4Json(tenancyId, sql.toString());
        if (null != list && list.size() > 0)
            return list.get(0);
        return null;
    }

    @Override
    public void updateTableState(String tenancyId, int storeId, String tableCode) throws Exception {
        StringBuffer sql = new StringBuffer();
        sql.append(" update pos_tablestate set lock_pos_num = '"+ WLifeConstant.LOCK_POS_NUM +"',opt_name = '"+ WLifeConstant.OPT_NAME +"',lock_opt_num = '"+ WLifeConstant.LOCK_OPT_NUM +"',last_updatetime = '"+ DateUtil.getNowDateYYDDMMHHMMSS() +"'")
            .append(" where store_id = "+ storeId +" and table_code = '"+ tableCode +"'");
        this.execute(tenancyId, sql.toString());
    }

    @Override
    public void updateBillLock(String tenancyId, String billNum, int storeId) throws Exception {
        StringBuffer sql = new StringBuffer();
        sql.append(" update pos_bill_lock set lock_state='1',lock_type = '"+ SysDictionary.CHANEL_WX02 +"',lock_time = '"+ DateUtil.getNowDateYYDDMMHHMMSS() +"' where bill_num = '"+ billNum +"' and store_id = " + storeId);
        this.execute(tenancyId, sql.toString());
    }

    @Override
    public List<JSONObject> getBillLockOpenId(String tenancyId, int storeId, String tableCode) throws Exception {
        StringBuffer sql = new StringBuffer();
        sql.append(" select pbl.id,pbl.store_id,pbl.open_id from pos_bill_lock pbl")
            .append(" left join pos_bill pb on pbl.bill_num = pb.bill_num")
            .append(" where pb.store_id = "+ storeId +" and pb.table_code = '"+ tableCode +"' and pb.bill_property = 'OPEN'");
        return this.query4Json(tenancyId, sql.toString());
    }

    @Override
    public String getBillNum(String tenancyId, int storeId, String tableCode) throws Exception {
        StringBuffer sql = new StringBuffer();
        sql.append(" select bill_num from pos_bill where store_id = "+ storeId +" and table_code = '"+ tableCode +"' and bill_property = 'OPEN' limit 1");
        return this.getString(tenancyId, sql.toString());
    }
}
