package com.tzx.clientorder.wechatprogram.common.entity.program;

/**
 * Created by zds on 2018-07-31.
 */
public class MemberCouponsEntity
{
	private String	coupon_id;
	private String	coupon_code;
	/**
	 * 1:代金券 2:礼品券
	 */
	private String	coupon_pro;
	/**
	 * 名称
	 */
	private String	coupon_name;
	/**
	 * 券面额(单位:元)
	 */
	private Double	coupon_value;
	/**
	 * 使用条件与限制
	 */
	private String	coupon_summary;
	/**
	 * 数量
	 */
	private Integer	coupon_count;

	public String getCoupon_id()
	{
		return coupon_id;
	}

	public void setCoupon_id(String coupon_id)
	{
		this.coupon_id = coupon_id;
	}

	public String getCoupon_code()
	{
		return coupon_code;
	}

	public void setCoupon_code(String coupon_code)
	{
		this.coupon_code = coupon_code;
	}

	public String getCoupon_pro()
	{
		return coupon_pro;
	}

	public void setCoupon_pro(String coupon_pro)
	{
		this.coupon_pro = coupon_pro;
	}

	public String getCoupon_name()
	{
		return coupon_name;
	}

	public void setCoupon_name(String coupon_name)
	{
		this.coupon_name = coupon_name;
	}

	public Double getCoupon_value()
	{
		return coupon_value;
	}

	public void setCoupon_value(Double coupon_value)
	{
		this.coupon_value = coupon_value;
	}

	public String getCoupon_summary()
	{
		return coupon_summary;
	}

	public void setCoupon_summary(String coupon_summary)
	{
		this.coupon_summary = coupon_summary;
	}

	public Integer getCoupon_count()
	{
		return coupon_count;
	}

	public void setCoupon_count(Integer coupon_count)
	{
		this.coupon_count = coupon_count;
	}
}
