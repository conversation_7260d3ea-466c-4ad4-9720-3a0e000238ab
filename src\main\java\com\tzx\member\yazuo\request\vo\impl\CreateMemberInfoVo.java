package com.tzx.member.yazuo.request.vo.impl;

import com.tzx.member.yazuo.common.entity.YazuoMemberInfoEntity;
import com.tzx.member.yazuo.request.vo.YazuoParam;

/**
 * 5.2 创建会员
 * 
 * <AUTHOR>
 *
 */
public class CreateMemberInfoVo extends YazuoParam
{
	private String					thirtyType;		// 会员来源类型1：手机号码 2：支付宝ID3:微信OPENID
	private String					thirtyMemberId;	// 根据来源类型传相应的手机号、支付宝ID、微信OPENID
	private YazuoMemberInfoEntity	membership;
	
	public String getThirtyType()
	{
		return thirtyType;
	}
	public void setThirtyType(String thirtyType)
	{
		this.thirtyType = thirtyType;
	}
	public String getThirtyMemberId()
	{
		return thirtyMemberId;
	}
	public void setThirtyMemberId(String thirtyMemberId)
	{
		this.thirtyMemberId = thirtyMemberId;
	}
	public YazuoMemberInfoEntity getMembership()
	{
		return membership;
	}
	public void setMembership(YazuoMemberInfoEntity membership)
	{
		this.membership = membership;
	}
}
