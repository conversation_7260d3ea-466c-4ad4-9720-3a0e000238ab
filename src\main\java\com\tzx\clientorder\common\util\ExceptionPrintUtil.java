package com.tzx.clientorder.common.util;

import com.tzx.framework.common.entity.Data;
import org.apache.log4j.Logger;

import com.tzx.framework.common.exception.ErrorCode;
import com.tzx.framework.common.exception.ExceptionMessage;
import com.tzx.framework.common.exception.OtherSystemException;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.pos.base.Constant;

import net.sf.json.JSONObject;

/**
 * 异常输出统一调用
 */
public class ExceptionPrintUtil {

	private static final String			SUCCESS					= "success";
	private static final String			MSG						= "msg";
    /**
     * 自定义其它异常
     * @param logger
     * @param se
     * @param responseJson
     * @param message
     */
    public static void buildOtherSysExceptionData(Logger logger, OtherSystemException se, JSONObject responseJson, String message)
    {
        responseJson.put("code", se.getCode());
        responseJson.put("msg", se.getMsg());
        logger.error(message + ",原因：" + se.getMsg() + ",错误码：" + se.getCode());
        logger.error(ExceptionMessage.getExceptionMessage(se));
    }

    /**
     * 自定义异常
     * @param logger
     * @param se
     * @param responseJson
     * @param message
     */
    public static void buildSysExceptionData(Logger logger, SystemException se, JSONObject responseJson, String message)
    {
        ErrorCode error = se.getErrorCode();
        String msg = se.getErrorMsg();
        responseJson.put("code", error.getNumber());
        responseJson.put("msg", msg);
        logger.error(message + ",原因：" + msg + ",错误码：" + error.getNumber());
        logger.error(ExceptionMessage.getExceptionMessage(se));
    }

    /**
     * 系统异常
     * @param logger
     * @param e
     * @param responseJson
     * @param message
     */
    public static void buildExceptionData(Logger logger, Exception e, JSONObject responseJson, String message)
    {
        responseJson.put("code", Constant.CODE_INNER_EXCEPTION);
        responseJson.put("msg", message);
        logger.error("系统内部错误：" + ExceptionMessage.getExceptionMessage(e));
    }
    
    /**
     * @param logger
     * @param se
     * @param responseJson
     * @param message
     */
    public static void buildProgramOtherSysExceptionData(Logger logger, OtherSystemException se, JSONObject responseJson, String message)
    {
        responseJson.put(SUCCESS, 0);
        responseJson.put(MSG, se.getMsg());
        logger.error(message + ",原因：" + se.getMsg() + ",错误码：" + se.getCode());
        logger.error(ExceptionMessage.getExceptionMessage(se));
    }
    
    /**
     * @param logger
     * @param se
     * @param responseJson
     * @param message
     */
    public static void buildProgramSysExceptionData(Logger logger, SystemException se, JSONObject responseJson, String message)
    {
        ErrorCode error = se.getErrorCode();
        String msg = "未知错误";
        try
		{
        	msg = se.getErrorMsg();
		}
		catch (Exception e)
		{
			logger.info("获取错误信息失败: "+se.getErrorCode().toString());
		}
        responseJson.put(SUCCESS, 0);
        responseJson.put(MSG, msg);
        logger.error(message + ",原因：" + msg + ",错误码：" + error.getNumber());
        logger.error(ExceptionMessage.getExceptionMessage(se));
    }
   
    /**
     * @param logger
     * @param e
     * @param responseJson
     * @param message
     */
    public static void buildProgramExceptionData(Logger logger, Exception e, JSONObject responseJson, String message)
    {
        responseJson.put(SUCCESS, 0);
        responseJson.put(MSG, message);
        logger.error("系统内部错误：" + ExceptionMessage.getExceptionMessage(e));
    }


    /**
     * 自定义异常
     * @param logger
     * @param se
     * @param result
     * @param message
     */
    public static void buildNewSysExceptionData(Logger logger, OtherSystemException se, Data result, String message)
    {
        result.setCode(se.getCode());
        result.setMsg(se.getMsg());
        logger.error(message + ",原因：" + se.getMsg() + ",错误码：" + se.getCode());
        logger.error(ExceptionMessage.getExceptionMessage(se));
    }
}
