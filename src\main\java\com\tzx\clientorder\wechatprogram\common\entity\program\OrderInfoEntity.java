package com.tzx.clientorder.wechatprogram.common.entity.program;

import java.util.List;

/** 订单信息
 * <AUTHOR>
 *
 */
public class OrderInfoEntity
{
	private String							tableno;
	private String							ordernum;
	private String							billnum;
	private Integer							people;
	private String[]						ordermemo;
	private Double							total_amount;
	private Double							cost_amount;
	private Double							discount_money;
	private List<OrderDiscountInfoEntity>	discount_info;
	private Double							mealfee;
	private List<OrderMealfeeInfoEntity>	mealfee_info;
	private List<OrderDishEntity>			normalitems;
	private List<OrderSetmealEntity>		setmeal;
	private MemberEntity					member;
	
	public String getTableno()
	{
		return tableno;
	}
	public void setTableno(String tableno)
	{
		this.tableno = tableno;
	}
	public String getOrdernum()
	{
		return ordernum;
	}
	public void setOrdernum(String ordernum)
	{
		this.ordernum = ordernum;
	}
	public String getBillnum()
	{
		return billnum;
	}
	public void setBillnum(String billnum)
	{
		this.billnum = billnum;
	}
	public Integer getPeople()
	{
		return people;
	}
	public void setPeople(Integer people)
	{
		this.people = people;
	}
	public String[] getOrdermemo()
	{
		return ordermemo;
	}
	public void setOrdermemo(String[] ordermemo)
	{
		this.ordermemo = ordermemo;
	}
	public Double getTotal_amount()
	{
		return ((null != total_amount && !total_amount.isNaN()) ? total_amount : 0d);
	}
	public void setTotal_amount(Double total_amount)
	{
		this.total_amount = total_amount;
	}
	public Double getCost_amount()
	{
		return ((null != cost_amount && !cost_amount.isNaN()) ? cost_amount : 0d);
	}
	public void setCost_amount(Double cost_amount)
	{
		this.cost_amount = cost_amount;
	}
	public Double getDiscount_money()
	{
		return ((null != discount_money && !discount_money.isNaN()) ? discount_money : 0d);
	}
	public void setDiscount_money(Double discount_money)
	{
		this.discount_money = discount_money;
	}
	public List<OrderDiscountInfoEntity> getDiscount_info()
	{
		return discount_info;
	}
	public void setDiscount_info(List<OrderDiscountInfoEntity> discount_info)
	{
		this.discount_info = discount_info;
	}
	public Double getMealfee()
	{
		return ((null != mealfee && !mealfee.isNaN()) ? mealfee : 0d);
	}
	public void setMealfee(Double mealfee)
	{
		this.mealfee = mealfee;
	}
	public List<OrderMealfeeInfoEntity> getMealfee_info()
	{
		return mealfee_info;
	}
	public void setMealfee_info(List<OrderMealfeeInfoEntity> mealfee_info)
	{
		this.mealfee_info = mealfee_info;
	}
	public List<OrderDishEntity> getNormalitems()
	{
		return normalitems;
	}
	public void setNormalitems(List<OrderDishEntity> normalitems)
	{
		this.normalitems = normalitems;
	}
	public List<OrderSetmealEntity> getSetmeal()
	{
		return setmeal;
	}
	public void setSetmeal(List<OrderSetmealEntity> setmeal)
	{
		this.setmeal = setmeal;
	}
	public MemberEntity getMember()
	{
		return member;
	}
	public void setMember(MemberEntity member)
	{
		this.member = member;
	}
}
