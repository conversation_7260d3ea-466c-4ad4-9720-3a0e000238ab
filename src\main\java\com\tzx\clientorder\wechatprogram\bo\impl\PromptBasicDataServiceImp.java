package com.tzx.clientorder.wechatprogram.bo.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.annotation.Resource;

import com.tzx.pos.po.springjdbc.dao.PosDao;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import com.tzx.clientorder.common.constant.SelectTypeEnum;
import com.tzx.clientorder.wechatprogram.bo.PromptBasicDataService;
import com.tzx.clientorder.wechatprogram.common.constant.PromptConstant;
import com.tzx.clientorder.wechatprogram.common.constant.PromptMsgConstant;
import com.tzx.clientorder.wechatprogram.common.util.PromptUtil;
import com.tzx.clientorder.wechatprogram.dao.BasicDataDao;
import com.tzx.framework.common.constant.SysParameterCode;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.CommonUtil;
import com.tzx.framework.common.util.Tools;
import com.tzx.pos.base.constant.SysDictionary;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

/**
 * 基础数据同步
 */
@Service(PromptBasicDataService.NAME)
public class PromptBasicDataServiceImp implements PromptBasicDataService
{
	private static final Logger	logger	= Logger.getLogger(PromptBasicDataServiceImp.class);

	@Resource(name = BasicDataDao.NAME)
	private BasicDataDao		basicDao;

	@Resource(name = PosDao.NAME)
	private PosDao posDao;

	@Override
	public JSONObject getBasicInfo(String tenancyId, int storeId) throws Exception
	{
		JSONObject data = this.getBasicData(tenancyId, storeId);

		JSONObject responseJson = new JSONObject();
		responseJson.put(PromptConstant.SUCCESS, 1);
		responseJson.put(PromptConstant.MSG, PromptMsgConstant.QUERY_BASIC_SUCCESS);
		responseJson.put(PromptConstant.DATA, data);

		return responseJson;
	}
	
	@Override
	public JSONObject getBasicData(String tenancyId, int storeId) throws Exception
	{
		Map<String, String> sysParaMap = basicDao.getSysParameter(tenancyId, storeId, SysParameterCode.WLIFE_DISH_KINDS_MODE, SysParameterCode.WLIFE_ORDERMODE_KEY);
		String dishKindMode = "2";// mode=1 大类 mode=2小类;默认按小类
		String orderMode = SysDictionary.ORDER_MODE_AFTERPAY;// 微生活点餐订单模式, 2:先付
																// 3:后付
		if (null != sysParaMap)
		{
			dishKindMode = sysParaMap.get(SysParameterCode.WLIFE_DISH_KINDS_MODE);
			orderMode = sysParaMap.get(SysParameterCode.WLIFE_ORDERMODE_KEY);
		}

		JSONObject organJson = basicDao.selectOrganInfo(tenancyId, storeId);

		JSONObject data = new JSONObject();
		this.selectDishs(tenancyId, storeId, data, dishKindMode, SysDictionary.CHANEL_WX02, organJson.optString("price_system"));
		this.selectDishKinds(tenancyId, storeId, data, dishKindMode, SysDictionary.CHANEL_WX02);
		this.selectMemo(tenancyId, storeId, data);
		this.selectTables(tenancyId, storeId, data);
		this.setShopInfo(tenancyId, storeId, data, organJson, orderMode);
		this.selectTableProperty(tenancyId, storeId, data);
		this.selectTableArea(tenancyId, storeId, data);

		return data;
	}

	@Override
	public JSONObject getSoldOutInfo(String tenancyId, int storeId) throws Exception
	{
		// 获取门店信息
		JSONObject organ = basicDao.selectOrganInfo(tenancyId, storeId);
		if (organ == null)
		{
			logger.info("机构不存在");
			throw SystemException.getInstance(PosErrorCode.NOT_EXISTS_ORGAN);
		}
		
		JSONObject soldoutJson = this.getSoldOutData(tenancyId, storeId);

		JSONObject responseJson = new JSONObject();
		responseJson.put(PromptConstant.SUCCESS, 1);
		responseJson.put(PromptConstant.MSG, PromptMsgConstant.QUERY_SOLD_OUT_SUCCESS);
		responseJson.put(PromptConstant.DATA, soldoutJson);
		return responseJson;
	}
	
	@Override
	public JSONObject getSoldOutData(String tenancyId, int storeId) throws Exception
	{
		List<JSONObject> dishs = basicDao.getSoldOutInfo(tenancyId, storeId);

		JSONObject soldoutJson = new JSONObject();
		if (null == dishs || dishs.size() < 1)
		{
			soldoutJson.put("soldout", new JSONArray());
		}
		else
		{
			soldoutJson.put("soldout", dishs);
		}
		return soldoutJson;
	}

	/**
	 * 查询菜品信息
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @return
	 * @throws Exception
	 */
	private void selectDishs(String tenancyId, int storeId, JSONObject context, String classMode, String chanel, String priceSystem) throws Exception
	{
		// 查询出门店餐谱的菜品信息
		List<JSONObject> itemInfos = basicDao.getItemInfosForCombo(tenancyId, storeId, chanel);
//		List<JSONObject> itemInfos = basicDao.getItemInfos(tenancyId, storeId, chanel);
//
//		// 获取套餐菜品ID集合
//		int[] comboItemIds = getComboItemIds(itemInfos);
//
//		// 查询不在餐谱的套餐明细菜品信息
//		List<JSONObject> comboItemList = basicDao.getItemInfosForCombo(tenancyId, storeId, comboItemIds, getItemIds(itemInfos), chanel);
//		if (null != comboItemList && 0 < comboItemList.size())
//		{
//			itemInfos.addAll(comboItemList);
//		}

		JSONObject dishs = new JSONObject();
		for (int i = 0; i < itemInfos.size(); i++)
		{
			JSONObject json = itemInfos.get(i);
			String dishkind = json.optString("pkindid");
			// mode=1 大类 mode=2小类
			// if(2==mode)
			if ("2".equals(classMode))
			{
				dishkind = json.optString("kindid");
			}

			if (dishkind == null || "null".equals(dishkind))
			{
				json.put("pkindid", new String[0]); // 特殊处理
			}
			else
			{
				json.put("pkindid", new String[]
				{ dishkind }); // 特殊处理
			}

			String dishsno = json.optString("dishno"); // 速记码
			dishs.put(dishsno, json); // 菜品信息以速记码做key
		}

		int[] itemIds = getItemIds(itemInfos);

		// 根据item_id查询unit信息
		List<JSONObject> unitInfos = basicDao.getUnitInfos(tenancyId, itemIds, chanel, priceSystem);
		Map<Integer, List<JSONObject>> unitMap = PromptUtil.buildMap(unitInfos, "item_id");

		// 查询做法信息
		List<JSONObject> cooksInfos = basicDao.getMethodInfos(tenancyId, storeId, itemIds, chanel);
		Map<Integer, List<JSONObject>> cooksMap = PromptUtil.buildMap(cooksInfos, "item_id");
		
		// 查询菜品备注
		List<JSONObject> dishMemoList = basicDao.getDishMemoList(tenancyId, storeId, itemIds);
		Map<Integer, List<JSONObject>> dishMemoMap = PromptUtil.buildMap(dishMemoList, "item_id");

		// 查询套餐信息
		int[] comboItemIds = getComboItemIds(itemInfos);
		List<JSONObject> selectCombInfos = selectCombInfos(tenancyId, comboItemIds, chanel, priceSystem);
		Map<Integer, List<JSONObject>> combMap = PromptUtil.buildMap(selectCombInfos, "item_id");

		// 组装
		String[] keys = Tools.getJsonKey(dishs);
		List<JSONObject> dishList = new ArrayList<JSONObject>();
		for (String key : keys)
		{
			JSONObject json = dishs.getJSONObject(key);
			int itemId = json.optInt("dishid");
			String itemName = json.optString("dish_name");
			
			if ("2".equals(json.optString("type")))
			{
//				if (unitMap.containsKey(itemId) && CommonUtil.hv(unitMap.get(itemId)))
//				{
//					JSONObject defaultUnit = unitMap.get(itemId).get(0);
//					unitFor : for (JSONObject unitJson : unitMap.get(itemId))
//					{
//						if ("1".equals(unitJson.optString("is_default")))
//						{
//							defaultUnit = unitJson;
//							break unitFor;
//						}
//					}
//
//					List<JSONObject> unitList = new ArrayList<JSONObject>();
//					unitList.add(defaultUnit);
//					json.put("norms", unitList);
//				}
//				else
//				{
//					dishs.remove(key);
//					continue;
//				}
				//套餐菜品
				json.put("norms", new JSONArray());

				if (combMap.containsKey(itemId))
				{
					json.put("setmeals", combMap.get(itemId).get(0));
				}
				else
				{
					dishs.remove(key);
					logger.info(String.format("套餐 %s 不存在套餐明细",itemName));
					continue;
				}

				json.put("cooks", new JSONArray());
			}
			else
			{
				//非套餐菜品
				if (unitMap.containsKey(itemId) && CommonUtil.hv(unitMap.get(itemId)))
				{
					//规格
					json.put("norms", unitMap.get(itemId));
				}
				else
				{
					logger.info(String.format("菜品 %s 规格不存在",itemName));
					dishs.remove(key);
					continue;
				}

				json.put("setmeals", new JSONObject());

				//做法
				if (cooksMap.containsKey(itemId))
				{
					json.put("cooks", cooksMap.get(itemId));
				}
				else
				{
					json.put("cooks", new JSONArray());
				}
			}
			
			//口味备注
			if (dishMemoMap.containsKey(itemId))
			{
				json.put("memo", dishMemoMap.get(itemId));
			}
			else
			{
				json.put("memo", new JSONArray());
			}
			
			json.put("areaid", new JSONArray());
			
			dishList.add(json);
		}

		context.put(SelectTypeEnum.DISHS.name, dishList);
	}

	/** 组织套餐
	 * @param tenancyId
	 * @param comboItemIds
	 * @param chanel
	 * @param price_system
	 * @return
	 * @throws Exception
	 */
	private List<JSONObject> selectCombInfos(String tenancyId, int[] comboItemIds, String chanel, String price_system) throws Exception
	{
		// 查询套餐信息
		List<JSONObject> defaultUnitInfos = basicDao.getComboBaseInfo(tenancyId, comboItemIds, chanel, price_system);

		// 套餐的主菜和辅菜都查询出来
		List<JSONObject> detailsInfo = basicDao.selectDetailsInfo(tenancyId, comboItemIds);
		// 查询主菜和辅菜
		List<JSONObject> maindish = new ArrayList<JSONObject>();
		Map<Integer, Integer> isGroup = new HashMap<Integer, Integer>();
		for (JSONObject detail : detailsInfo)
		{
			int itemId = detail.optInt("item_id");
			if ("N".equals(detail.optString("is_itemgroup")))
			{
				// 套餐主菜
				maindish.add(detail);
			}
			else if ("Y".equals(detail.optString("is_itemgroup")))
			{
				// 辅菜
				isGroup.put(detail.optInt("combo_id"), itemId);
			}
		}
		// 查询主菜信息
		Map<Integer, List<JSONObject>> map0 = PromptUtil.buildMap(maindish, "item_id"); // id为菜品信息id

		// 查询辅菜信息
		int[] comboIds = PromptUtil.transferIntegerArray(isGroup.keySet());
		List<JSONObject> mandatoryInfos = getMandatoryInfos(tenancyId, comboIds);
		for (JSONObject json : mandatoryInfos)
		{
			json.put("item_id", isGroup.get(json.optInt("combo_id")));
		}
		Map<Integer, List<JSONObject>> map1 = PromptUtil.buildMap(mandatoryInfos, "item_id");

		// 将主菜和辅菜挂到对应菜品信息下
		for (JSONObject json : defaultUnitInfos)
		{
			Integer itemId = json.optInt("item_id");
			if (map0.containsKey(itemId))
			{
				json.put("maindish", map0.get(itemId));
			}
			else
			{
				json.put("maindish", new JSONArray());
			}
			
			if (map1.containsKey(itemId))
			{
				json.put("mandatory", map1.get(itemId));
			}
			else
			{
				json.put("mandatory", new JSONArray());
			}
		}
		return defaultUnitInfos;
	}

	/** 可选项目组
	 * @param tenancyId
	 * @param hicdIds
	 * @return
	 * @throws Exception
	 */
	private List<JSONObject> getMandatoryInfos(String tenancyId, int[] hicdIds) throws Exception
	{
		// 查询套餐辅菜
		List<JSONObject> list = basicDao.getMandatoryBaseInfo(tenancyId, hicdIds);
		Set<Integer> groupIdSet = new HashSet<Integer>();
		for (JSONObject json : list)
		{
			Integer id = json.optInt("rpdid");
			groupIdSet.add(id);
		}
		int[] groupIds = PromptUtil.transferIntegerArray(groupIdSet);
		// 查询套餐辅菜的菜品组详细
		List<JSONObject> groupDetails = basicDao.getGroupDetails(tenancyId, groupIds);
		Map<Integer, List<JSONObject>> groupDetailMap = PromptUtil.buildMap(groupDetails, "rpdid");
		for (JSONObject json : list)
		{
			Integer group_id = json.optInt("rpdid");
			if (groupDetailMap.containsKey(group_id))
			{
				json.put("items", groupDetailMap.get(group_id));
			}
		}
		return list;
	}

	/**套餐ID
	 * @param item_infos
	 * @return
	 */
	private int[] getComboItemIds(List<JSONObject> item_infos)
	{
		Set<Integer> itemIdSet = new HashSet<Integer>();
		for (JSONObject json : item_infos)
		{
			String isComb = json.optString("type", "1");
			if ("2".equals(isComb))
			{
				Integer item_id = json.optInt("dishid");
				itemIdSet.add(item_id);
			}
		}
		int[] ids = PromptUtil.transferIntegerArray(itemIdSet);
		return ids;
	}

	/** 菜品ID
	 * @param item_infos
	 * @return
	 */
	private int[] getItemIds(List<JSONObject> item_infos)
	{
		Set<Integer> itemIdSet = new HashSet<Integer>();
		for (JSONObject json : item_infos)
		{
			Integer item_id = json.optInt("dishid");
			itemIdSet.add(item_id);
		}
		int[] ids = PromptUtil.transferIntegerArray(itemIdSet);
		return ids;
	}

	/**
	 * 菜品备注
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param context
	 * @throws Exception
	 */
	private void selectMemo(String tenancyId, int storeId, JSONObject context) throws Exception
	{
		List<JSONObject> datas = basicDao.selectMemo(tenancyId, storeId);
		context.put(SelectTypeEnum.MEMO.name, datas);
	}

	/**
	 * 查询菜品类别
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param context
	 * @param classMode
	 * @param chanel
	 * @throws Exception
	 */
	private void selectDishKinds(String tenancyId, int storeId, JSONObject context, String classMode, String chanel) throws Exception
	{
		// 查询菜品类别
		List<JSONObject> itemClassList = basicDao.getDishKinds(tenancyId, storeId, chanel);

		// mode=1 大类 mode=2小类;默认按小类
		List<JSONObject> retList = new ArrayList<JSONObject>();
		JSONObject dishKindsJson = null;
		for (JSONObject itemClassJson : itemClassList)
		{
			String pdkid = itemClassJson.optString("father_id");
			if (CommonUtil.isNullOrEmpty(pdkid))
			{
				pdkid = "0";
			}

			if ("1".equals(classMode) && "0".equals(pdkid))
			{
				String classId = itemClassJson.optString("id");

				List<String> children = new ArrayList<String>();
				Integer dishCount = 0;
				for (JSONObject childrenJson : itemClassList)
				{
					String childrenId = childrenJson.optString("id");
					String fatherId = childrenJson.optString("father_id");
					if (classId.equals(fatherId) && false == children.contains(childrenId))
					{
						children.add(childrenId);
						dishCount = dishCount + childrenJson.optInt("item_count");
					}
				}

				dishKindsJson = new JSONObject();
				dishKindsJson.put("kindid", classId);
				dishKindsJson.put("kindno", itemClassJson.optString("itemclass_code"));
				dishKindsJson.put("kind_name", itemClassJson.optString("itemclass_name"));
				dishKindsJson.put("kind_seq", itemClassJson.optInt("classorder"));
				dishKindsJson.put("must", "0");
				dishKindsJson.put("must_seq", "0");
				dishKindsJson.put("suggest", "0");
				dishKindsJson.put("icon", "");
				dishKindsJson.put("pkindid", "0");
				dishKindsJson.put("children", children);
				dishKindsJson.put("dish_count", dishCount);
				retList.add(dishKindsJson);
			}
			else if (!"0".equals(pdkid))
			{
				dishKindsJson = new JSONObject();
				dishKindsJson.put("kindid", itemClassJson.optString("id"));
				dishKindsJson.put("kindno", itemClassJson.optString("itemclass_code"));
				dishKindsJson.put("kind_name", itemClassJson.optString("itemclass_name"));
				dishKindsJson.put("kind_seq", itemClassJson.optInt("classorder"));
				dishKindsJson.put("must", "0");
				dishKindsJson.put("must_seq", "0");
				dishKindsJson.put("suggest", "0");
				dishKindsJson.put("icon", "");
				dishKindsJson.put("pkindid", pdkid);
				dishKindsJson.put("children", "[]");
				dishKindsJson.put("dish_count", itemClassJson.optInt("item_count"));
				retList.add(dishKindsJson);
			}
		}
		context.put(SelectTypeEnum.DISH_KINDS.name, retList);
	}

	/**
	 * 桌台信息
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param context
	 * @throws Exception
	 */
	private void selectTables(String tenancyId, int storeId, JSONObject context) throws Exception
	{
		List<JSONObject> datas = basicDao.getTables(tenancyId, storeId);
		context.put(SelectTypeEnum.TABLES.name, datas);
	}

	/**
	 * 桌台属性信息
	 *
	 * @param tenancyId
	 * @param storeId
	 * @param context
	 * @throws Exception
	 */
	private void selectTableProperty(String tenancyId, int storeId, JSONObject context) throws Exception
	{
		List<JSONObject> datas = posDao.getTablePropertyByOrganId(tenancyId, storeId);
		context.put(SelectTypeEnum.TABLES_PROPERTY.name, datas.get(0).getJSONArray("table_property"));
	}

	/**
	 * 桌台区域信息
	 *
	 * @param tenancyId
	 * @param storeId
	 * @param context
	 * @throws Exception
	 */
	private void selectTableArea(String tenancyId, int storeId, JSONObject context) throws Exception
	{
		List<JSONObject> datas = posDao.getBussinessArea(tenancyId, storeId);
		context.put(SelectTypeEnum.TABLES_AREA.name, datas.get(0).getJSONArray("business_area"));
	}


	/**机构信息
	 * @param context
	 * @param orderMode
	 * @param areaId
	 * @return
	 * @throws Exception
	 */
	private void setShopInfo(String tenancyId, int storeId, JSONObject context, JSONObject organJson, String orderMode) throws Exception
	{
		JSONObject shopJson = new JSONObject();
		shopJson.put("shopid", organJson.optString("id"));
		shopJson.put("brandid", "");// 品牌id
		shopJson.put("shop_name", organJson.optString("org_full_name"));
		shopJson.put("shop_address", organJson.optString("address"));
		shopJson.put("lng", organJson.optString("longitude"));
		shopJson.put("lat", organJson.optString("latitude"));
		shopJson.put("is_bind_user", "0");// 是否需要用户绑定手机号, 0:否 1:是, 默认:0
		shopJson.put("ordermode", orderMode);

		context.put(SelectTypeEnum.SHOPS.name, shopJson);
	}
}
