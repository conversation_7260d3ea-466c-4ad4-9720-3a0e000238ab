package com.tzx.clientorder.wechatprogram.runnable;

import org.apache.log4j.Logger;

import com.tzx.clientorder.wechatprogram.bo.adapter.PromptServiceAdapter;
import com.tzx.clientorder.wechatprogram.common.util.PromptUtil;

public class SyncBaseDataRunnable implements Runnable
{
	private static final Logger logger = Logger.getLogger(SyncBaseDataRunnable.class);
	
	private String	tenancyId;
	private Integer	storeId;
	private String	orderType;

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param orderType 小程序类型
	 */
	public SyncBaseDataRunnable(String tenancyId, Integer storeId, String orderType)
	{
		super();
		this.tenancyId = tenancyId;
		this.storeId = storeId;
		this.orderType = orderType;
	}

	@Override
	public void run()
	{
		try
		{
			// 休眠500毫秒,等待数据提交完成;
			Thread.sleep(500);

			PromptServiceAdapter adapter = new PromptServiceAdapter(PromptUtil.getChannel(orderType));
			adapter.syncDishData(tenancyId, storeId);
		}
		catch (Exception e)
		{
			logger.info("同步基础数据失败:", e);
		}
	}

	public void setTenancyId(String tenancyId)
	{
		this.tenancyId = tenancyId;
	}

	public void setStoreId(Integer storeId)
	{
		this.storeId = storeId;
	}

	public void setOrderType(String orderType)
	{
		this.orderType = orderType;
	}
}
