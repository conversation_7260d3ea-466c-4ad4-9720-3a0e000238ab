package com.tzx.clientorder.wlifeprogram.bo.impl;

import com.tzx.base.cache.util.CacheTableUtil;
import com.tzx.base.common.util.PosPropertyUtil;
import com.tzx.clientorder.common.constant.WLifeConstant;
import com.tzx.clientorder.wlifeprogram.bo.AfterPaymentService;
import com.tzx.clientorder.wlifeprogram.bo.WlifeService;
import com.tzx.clientorder.wlifeprogram.common.constant.WlifePromptConstant;
import com.tzx.clientorder.wlifeprogram.common.util.WlifePromptUtil;
import com.tzx.clientorder.wlifeprogram.dao.PosGenericDao;
import com.tzx.framework.common.util.HttpUtil;
import com.tzx.framework.common.util.Md5Utils;
import com.tzx.framework.common.constant.SysParameterCode;
import com.tzx.framework.common.util.CommonUtil;
import com.tzx.orders.base.util.OrderUtil;
import com.tzx.pos.base.Constant;
import com.tzx.pos.base.constant.SysDictionary;
import net.sf.json.JSONObject;
import org.apache.commons.codec.binary.Base64;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;


/**
 * Created by qingui on 2018-07-24.
 */
@Service(WlifeService.NAME)
public class WlifeServiceImp implements WlifeService{

	private Logger				logger	= Logger.getLogger(WlifeServiceImp.class);
	
	@Resource(name = PosGenericDao.name)
	private PosGenericDao		posGenericDao;
    @Resource(name = AfterPaymentService.NAME)
    private AfterPaymentService	afterPaymentService;
	
	/**
	 * @param url
	 * @return
	 * @throws Exception
	 */
	private String getWlifeRequestUrl(String url) throws Exception
	{
		return PosPropertyUtil.getMsg(WlifePromptConstant.WLIFE_REQUEST_URL_KEY) + url;
	}

	/**
	 * 判断是否微生活小程序点餐订单
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param orderSource
	 * @return
	 * @throws Exception
	 */
	private boolean isWlifeProgramOrder(String tenancyId, int storeId, String orderSource) throws Exception
	{
		String orderType = posGenericDao.getSysParameter(tenancyId, storeId, SysParameterCode.USER_ORDER_TYPE_KEY);
		return (SysDictionary.USER_ORDER_TYPE_WLIFE_PROGRAM.equals(orderType) && SysDictionary.CHANEL_WSP17.equals(orderSource));
	}

	/**
	 * 判断是否启用微生活小程序点餐
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @return
	 * @throws Exception
	 */
	private boolean isUserWlifeProgram(String tenancyId, int storeId) throws Exception
	{
		String orderType = posGenericDao.getSysParameter(tenancyId, storeId, SysParameterCode.USER_ORDER_TYPE_KEY);
		return SysDictionary.USER_ORDER_TYPE_WLIFE_PROGRAM.equals(orderType);
	}

	@Override
	public JSONObject completeOrder(String tenancyId, int storeId, String billNum) throws Exception
	{
        JSONObject order = posGenericDao.getOrderInfo(tenancyId, billNum);
        if (CommonUtil.isNullOrEmpty(order)){
            logger.info("通知微生活结账查询不到该账单号" + billNum +"对应的账单");
            return null;
        }
        String orderSource = order.optString("order_source");
        //该门店是否开启了微生活小程序以及是不是小程序订单
        boolean flag = isWlifeProgramOrder(tenancyId, storeId, orderSource);
        //未开启微生活小程序或者账单不是微生活小程序来源的账单
        if (!flag)
		    return null;

        String out_order_id = order.optString("order_num");
        String meal_number = order.optString("fictitious_table");
        
        if (!CommonUtil.hasText(out_order_id)){
            logger.info("POS结账，平台订单号为" + out_order_id + ",不通知微生活");
            return null;
        }
        String posMark = posGenericDao.getBillPayNames(tenancyId, billNum);
        
        return this.completeOrder(out_order_id, meal_number, posMark);
	}
	
	@Override
	public JSONObject completeOrder(String outOrderId,String mealNumber,String posmark) throws Exception
	{
		String url = this.getWlifeRequestUrl(WlifePromptConstant.WLIFE_COMPLETE_ORDER_URL);
        logger.info("POS结账通知微生活" + url + "的平台订单号是" + outOrderId + ",取餐号是" + mealNumber + ",posMark=" + posmark);
        Map<String,String> param = new HashMap<String,String>();
        param.put("out_order_id", outOrderId);
        param.put("meal_number", mealNumber);
        param.put("posmark", posmark);
        String msg = HttpUtil.sendPostRequest(url, param);
        logger.info("微生活平台返回的结账信息是：" + msg);
        
        JSONObject result = new JSONObject();
        if (!CommonUtil.isNullOrEmpty(msg)){
            return JSONObject.fromObject(msg);
        }else {
            result.put("errcode", -1);
            result.put("errmsg", "结账失败");
            result.put("result", new JSONObject());
            return result;
        }
	}

	@Override
	public JSONObject unlockOrder(String tenancyId, int storeId, String billNum) throws Exception
	{
        JSONObject order = posGenericDao.getOrderInfo(tenancyId, billNum);
        if (CommonUtil.isNullOrEmpty(order)){
            logger.info("通知微生活解锁查询不到该账单号" + billNum +"对应的账单");
            return null;
        }
        String orderSource = order.optString("order_source");
        //该门店是否开启了微生活小程序以及是不是小程序订单
        boolean flag = isWlifeProgramOrder(tenancyId, storeId, orderSource);
        //未开启微生活小程序或者账单不是微生活小程序来源的账单
        if (!flag)
            return null;

        String out_order_id = order.optString("order_num");
        JSONObject result = new JSONObject();
        String url = this.getWlifeRequestUrl(WlifePromptConstant.WLIFE_UNLOCK_ORDER_URL);
        logger.info("POS通知微生活"+ url +"解锁订单的平台订单号是" + out_order_id );
        Map<String,String> param = new HashMap<String,String>();
        param.put("out_order_id", out_order_id);
        String msg = HttpUtil.sendPostRequest(url, param);
        logger.info("微生活平台返回的解锁订单信息是：" + msg);
        if (!CommonUtil.isNullOrEmpty(msg)){
            return JSONObject.fromObject(msg);
        }else {
            result.put("errcode", -1);
            result.put("errmsg", "解锁失败");
            result.put("result", new JSONObject());
            return result;
        }
	}

	@Override
	public JSONObject changeTable(String tenancyId, int storeId, String billNum, String targetTableCode) throws Exception
	{
        JSONObject order = posGenericDao.getOrderInfo(tenancyId, billNum);
        if (CommonUtil.isNullOrEmpty(order)){
            logger.info("通知微生活转台查询不到该账单号" + billNum +"对应的账单");
            return null;
        }
        String orderSource = order.optString("order_source");
        //该门店是否开启了微生活小程序以及是不是小程序订单
        boolean flag = isWlifeProgramOrder(tenancyId, storeId, orderSource);
        //未开启微生活小程序或者账单不是微生活小程序来源的账单
        if (!flag)
            return null;

        String out_order_id = order.optString("order_num");
        JSONObject result = new JSONObject();
        String url = this.getWlifeRequestUrl(WlifePromptConstant.CHANGE_TABLE_URL);
        logger.info("POS通知微生活"+ url +"转台的平台订单号是" + out_order_id +",转到桌台的速记码是" + targetTableCode);
        Map<String,String> param = new HashMap<String,String>();
        param.put("out_order_id", out_order_id);
        param.put("table_sno", targetTableCode);
        String msg = HttpUtil.sendPostRequest(url, param);
        logger.info("微生活平台返回的转台信息是：" + msg);
        if (!CommonUtil.isNullOrEmpty(msg)){
            return JSONObject.fromObject(msg);
        }else {
            result.put("errcode", -1);
            result.put("errmsg", "转台失败");
            result.put("result", new JSONObject());
            return result;
        }
	}

	@Override
	public JSONObject combineOrder(String tenancyId, int storeId, String billNum, String targetTableCode) throws Exception
	{
        JSONObject order = posGenericDao.getOrderInfo(tenancyId, billNum);
        if (CommonUtil.isNullOrEmpty(order)){
            logger.info("通知微生活并台查询不到该账单号" + billNum +"对应的账单");
            return null;
        }
        String orderSource = order.optString("order_source");
        //该门店是否开启了微生活小程序以及是不是小程序订单
        boolean flag = isWlifeProgramOrder(tenancyId, storeId, orderSource);
        //未开启微生活小程序或者账单不是微生活小程序来源的账单
        if (!flag)
            return null;

        String out_order_id = order.optString("order_num");
        JSONObject result = new JSONObject();
        String url = this.getWlifeRequestUrl(WlifePromptConstant.COMBINE_ORDER_URL);
        logger.info("POS通知微生活"+ url +"并台的平台订单号是" + out_order_id +",并到桌台速记码是" + targetTableCode);
        Map<String,String> param = new HashMap<String,String>();
        param.put("out_order_id", out_order_id);
        param.put("table_sno", targetTableCode);
        String msg = HttpUtil.sendPostRequest(url, param);
        logger.info("微生活平台返回的并台信息是：" + msg);
        if (!CommonUtil.isNullOrEmpty(msg)){
            return JSONObject.fromObject(msg);
        }else {
            result.put("errcode", -1);
            result.put("errmsg", "并台失败");
            result.put("result", new JSONObject());
            return result;
        }
	}

	@Override
	public JSONObject orderQuit(String tenancyId, int storeId, String billNum) throws Exception
	{
        JSONObject order = posGenericDao.getOrderInfo(tenancyId, billNum);
        if (CommonUtil.isNullOrEmpty(order)){
            logger.info("通知微生活退单查询不到该账单号" + billNum +"对应的账单");
            return null;
        }
        String orderSource = order.optString("order_source");
		String out_order_id = order.optString("order_num");
		
		return this.orderQuit(tenancyId, storeId, out_order_id, orderSource);
	}
	
	@Override
	public JSONObject orderQuit(String tenancyId, int storeId, String out_order_id,String orderSource) throws Exception
	{
//        JSONObject order = posGenericDao.getOrderInfo(tenancyId, billNum);
//        if (Tools.isNullOrEmpty(order)){
//            logger.info("通知微生活退单查询不到该账单号" + billNum +"对应的账单");
//            return null;
//        }
//        String orderSource = order.optString("order_source");
        //该门店是否开启了微生活小程序以及是不是小程序订单
        boolean flag = isWlifeProgramOrder(tenancyId, storeId, orderSource);
        //未开启微生活小程序或者账单不是微生活小程序来源的账单
        if (!flag)
            return null;

//        String out_order_id = order.optString("order_num");
        JSONObject result = new JSONObject();
        String url = this.getWlifeRequestUrl(WlifePromptConstant.ORDER_QUIT_URL);
        logger.info("POS通知微生活"+ url +"退单的平台订单号是" + out_order_id );
        Map<String,String> param = new HashMap<String,String>();
        param.put("out_order_id", out_order_id);
        String msg = HttpUtil.sendPostRequest(url, param);
        logger.info("微生活平台返回的退单信息是：" + msg);
        if (!CommonUtil.isNullOrEmpty(msg)){
            return JSONObject.fromObject(msg);
        }else {
            result.put("errcode", -1);
            result.put("errmsg", "退单失败");
            result.put("result", new JSONObject());
            return result;
        }
	}

    @Override
	public JSONObject orderRefund(String tenancyId, int storeId, String billNum) throws Exception
	{
		JSONObject order = posGenericDao.getOrderInfo(tenancyId, billNum);
		if (CommonUtil.isNullOrEmpty(order))
		{
			logger.info("通知微生活退款查询不到该账单号" + billNum + "对应的账单");
			return null;
		}
		String orderSource = order.optString("order_source");
		String out_order_id = order.optString("order_num");
		
		return this.orderRefund(tenancyId, storeId, out_order_id, orderSource);
	}
    
    @Override
    public JSONObject orderRefund(String tenancyId, int storeId, String out_order_id,String orderSource) throws Exception{
//        JSONObject order = posGenericDao.getOrderInfo(tenancyId, billNum);
//        if (Tools.isNullOrEmpty(order)){
//            logger.info("通知微生活退款查询不到该账单号" + billNum +"对应的账单");
//            return null;
//        }
//        String orderSource = order.optString("order_source");
        //该门店是否开启了微生活小程序以及是不是小程序订单
        boolean flag = isWlifeProgramOrder(tenancyId, storeId, orderSource);
        //未开启微生活小程序或者账单不是微生活小程序来源的账单
        if (!flag)
            return null;

//        String out_order_id = order.optString("order_num");
        
        String url = this.getWlifeRequestUrl(WlifePromptConstant.ORDER_REFUND_URL);
        logger.info("POS通知微生活"+ url +"退款的平台订单号是" + out_order_id );
        
        //微生活点餐小程序退款appkey
        String appKey = OrderUtil.getSysPara(tenancyId,storeId,SysParameterCode.WLIFE_PROGRAM_APP_KEY);
        String shopKey =  OrderUtil.getSysPara(tenancyId,storeId,SysParameterCode.SHOP_KEY);
        
        // 获取签名
        JSONObject obj = new JSONObject();
        obj.put("outorderid", out_order_id);

        String sign = getSign(obj, appKey);
        if (sign == null || "".equals(sign)) {
            logger.info("获取签名为空");
        }
        
        Map<String,String> param = new HashMap<String,String>();
        //点餐平台的shopkey
        param.put("shopKey", shopKey);
        param.put("out_order_id", out_order_id);
        //设置签名
        param.put("sign", sign);

        String msg = HttpUtil.sendPostRequest(url, param);
        logger.info("微生活平台返回的退款信息是：" + msg);
        if (!CommonUtil.isNullOrEmpty(msg)){
            return JSONObject.fromObject(msg);
        }else {
        	JSONObject result = new JSONObject();
            result.put("errcode", -1);
            result.put("errmsg", "退款失败");
            result.put("result", new JSONObject());
            return result;
        }
    }


    /**
     * 获取签名
     * @param obj
     * @return
     * @throws Exception
     */
    private String getSign(JSONObject obj, String appKey) throws Exception {
        // MD5加密appkey
        String newAppKey = Md5Utils.md5(appKey);
        String buff = obj.toString();

        String param = newAppKey + "#" + buff;
        logger.info("转换前的参数是:::: " + param);
        Base64 base64 = new Base64();
        byte[] paramByte = param.getBytes("UTF-8");
        String sign = new String(base64.encode(paramByte));
        return sign;
    }

	@Override
	public void synchrodataDish(String tenancyId, int storeId,String syncType,String syncForce) throws Exception
	{
		if(false == this.isUserWlifeProgram(tenancyId, storeId))
		{
			return ;
		}
		
		String bussinessId = OrderUtil.getSysPara(tenancyId, storeId, SysParameterCode.BUSSINESS_ID);
		String brandId =  OrderUtil.getSysPara(tenancyId, storeId, SysParameterCode.BRAND_ID);
		String shopId =  OrderUtil.getSysPara(tenancyId, storeId, SysParameterCode.SHOP_ID);
		String appKey =  OrderUtil.getSysPara(tenancyId, storeId, SysParameterCode.WLIFE_PROGRAM_APP_KEY);

		JSONObject signJson = new JSONObject();
		// 商户id_品牌id_门店id
		signJson.put("outorderid", (bussinessId + "_" + brandId + "_" + shopId));

		String sign = this.getSign(signJson, appKey);

		JSONObject requestJson = new JSONObject();
		requestJson.put("type", syncType);
		requestJson.put("force", syncForce);
		requestJson.put("sid", shopId);
		requestJson.put("sign", sign);

		String url = this.getWlifeRequestUrl(WlifePromptConstant.SYNCHRO_DATA_DISH_URL);

        logger.info("收银同步菜品接口请求: "+requestJson);
		String result = HttpUtil.sendPostRequest(url, requestJson.toString(),5000,30*1000);
		logger.info("收银同步菜品接口返回 =============> " + result);
	}
	
	private String getSysParameter(String tenancyId, int storeId, String paraCode) throws Exception
	{
		String val = CacheTableUtil.getSysParameter(paraCode);
		if (CommonUtil.isNullOrEmpty(val))
		{
			val = posGenericDao.getSysParameter(tenancyId, storeId, paraCode);
		}
		return val;
	}

	@Override
	public void synchrodataDish(String tenancyId, int storeId) throws Exception
	{
		this.synchrodataDish(tenancyId, storeId, WlifePromptConstant.SYNC_DISH_DATA_TYPE_ALL, WlifePromptConstant.SYNC_DISH_DATA_FORCE_RESET);
	}

    @Override
    public JSONObject uploadOrder(String tenancyId, int storeId, String billNum) throws Exception {
        JSONObject order = posGenericDao.getOrderInfo(tenancyId, billNum);
        if (CommonUtil.isNullOrEmpty(order)){
            logger.info("上传微生活账单查不到该账单号" + billNum +"对应的账单");
            return null;
        }
        String orderSource = order.optString("order_source");
        //该门店是否开启了微生活小程序以及是不是小程序订单
        boolean flag = isWlifeProgramOrder(tenancyId, storeId, orderSource);
        //未开启微生活小程序或者账单不是微生活小程序来源的账单
        if (!flag){
            logger.info("上传微生活账单失败，不是微生活小程序来源");
            return null;
        }

        String out_order_id = order.optString("order_num");
        String meal_number = order.optString("fictitious_table");

        if (!CommonUtil.hasText(out_order_id)){
            logger.info("POS结账，平台订单号为" + out_order_id + ",不通知微生活");
            return null;
        }
        JSONObject uploadJson = new JSONObject();
        uploadJson.put("out_order_id", out_order_id);
        uploadJson.put("msgBody", "上传通知");
        uploadJson.put("oid", billNum);
        JSONObject orderData = afterPaymentService.getClosedOrderDetail(tenancyId, storeId, billNum, WLifeConstant.OPT_TYPE_PAY);
        String order_info = orderData.optString("order_info")+ "";
        uploadJson.put("order_info", String.format("\"%s\"",order_info));


        JSONObject result = HttpUtil.post(WlifePromptUtil.getWlifeRequestUrl("/api/Order/uploadOrder"), uploadJson, 3, true);
        if ("0".equals(result.optString("errcode"))) {
            return result;
        } else {
            result.put("errcode", -1);
            result.put("errmsg", "上传失败");
            result.put("result", new JSONObject());
            return result;
        }

    }

    /*@Override
    public JSONObject uploadOrder(String tenancyId, int storeId, String billNum) throws Exception {
        JSONObject order = posGenericDao.getOrderInfo(tenancyId, billNum);
        if (Tools.isNullOrEmpty(order)){
            logger.info("通知微生活上传订单查询不到该账单号" + billNum +"对应的账单");
            return null;
        }
        String orderSource = order.optString("order_source");
        //该门店是否开启了微生活小程序以及是不是小程序订单
        boolean flag = isWlifeProgramOrder(tenancyId, storeId, orderSource);
        //未开启微生活小程序或者账单不是微生活小程序来源的账单
        if (!flag)
            return null;

        String out_order_id = order.optString("order_num");
        String tableCode = order.optString("fictitious_table");
        //查询桌台账单
        JSONObject billObject = posGenericDao.getBillInfo(tenancyId, tableCode, storeId);

        JSONObject result = new JSONObject();
        String url = this.getWlifeRequestUrl(WLifeConstant.UPLOAD_ORDER_URL);
        logger.info("POS通知微生活"+ url +"上传订单的平台订单号是" + out_order_id );
        JSONObject param = new JSONObject();
        param.put("out_order_id", out_order_id);
        JSONObject orderInfo = new JSONObject();
        //设置订单的会员信息
        JSONObject member = orderDao.getWLifeMember(tenancyId, tableCode, storeId);

        String[] paraCodes = new String[]{
                WLifeConstant.BUSSINESS_ID,	//商户id
                WLifeConstant.BRAND_ID,		//品牌id
                WLifeConstant.SHOP_ID			//门店id
        };

        List<JSONObject> values = wshPosEntranceDao.selectStoreParams(tenancyId, storeId, paraCodes);
        JSONObject sysParam = new JSONObject();
        for(JSONObject json : values){
            sysParam.put(json.optString("para_code"), json.optString("para_value", json.optString("para_defaut")));
        }
        if (!Tools.isNullOrEmpty(param)){
            orderInfo.put("business_id", sysParam.optString(WLifeConstant.BUSSINESS_ID));
            orderInfo.put("brand_id", sysParam.optString(WLifeConstant.BRAND_ID));
            orderInfo.put("shop_id", sysParam.optString(WLifeConstant.SHOP_ID));
        }
        orderInfo.put("shop_name", billObject.optString("shop_name"));

        setOrderInfo(orderInfo, billObject, tableCode, member);

        //设置订单的菜品信息
        setDish(tenancyId, orderInfo);

        param.put("order_info", orderInfo);
        String msg = HttpUtil.sendPostRequest(url, param);
        logger.info("微生活平台返回的上传订单信息是：" + msg);
        if (!Tools.isNullOrEmpty(msg)){
            return JSONObject.fromObject(msg);
        }else {
            result.put("errcode", -1);
            result.put("errmsg", "上传订单失败");
            result.put("result", new JSONObject());
            return result;
        }
    }


    *//**
     * 订单信息
     * @param orderInfo
     * @param billObject
     * @param tableCode
     *//*
    private void setOrderInfo(JSONObject orderInfo, JSONObject billObject, String tableCode, JSONObject member){
        orderInfo.put("oid", billObject.optString("oid"));
        orderInfo.put("identify", billObject.optString("out_order_id"));
        orderInfo.put("out_order_id", billObject.optString("out_order_id"));
        orderInfo.put("tableno", tableCode);
        orderInfo.put("ordermemo", new JSONObject());
        //orderInfo.put("ordermemo", billObject.optString("text"));
        //应收
        orderInfo.put("total", billObject.optDouble("total"));
        //实收
        orderInfo.put("cost", billObject.optDouble("cost"));
        orderInfo.put("people", billObject.optString("people"));
        orderInfo.put("mealfee", billObject.optString("mealfee"));
        //会员信息
        if (!Tools.isNullOrEmpty(member)){
            orderInfo.put("openid", member.optString("openid"));
            orderInfo.put("name", member.optString("name"));
            orderInfo.put("mobile", member.optString("mobile"));
            orderInfo.put("credit", member.optString("credit"));
            orderInfo.put("balance", member.optString("balance"));
            //会员等级
            JSONObject upgrade = new JSONObject();
            upgrade.put("cardnum", member.optString("cno"));
            upgrade.put("orginlevel", member.optString("grade"));
            orderInfo.put("upgrade", upgrade);
        }else {
            orderInfo.put("openid", "");
            orderInfo.put("name", "");
            orderInfo.put("mobile", "");
            orderInfo.put("credit", 0);
            orderInfo.put("balance", 0);
            orderInfo.put("upgrade", new JSONObject());
        }

        orderInfo.put("discount_money", billObject.optDouble("discount_money"));
        orderInfo.put("memberPrice", billObject.optDouble("memberprice",0));
        orderInfo.put("weiXinPay", billObject.optDouble("weixinpay", 0));


        //活动信息
        orderInfo.put("discount_info", new JSONObject());
    }

    *//**
     * 设置订单中的菜品信息
     * @param orderObject
     *//*
    private void setDish(String tenancyId,JSONObject orderObject) throws Exception{
        //桌台的未结账的账单号
        String billNum = orderObject.optString("oid");
        //查询套餐菜品信息(可能一笔订单，多个套餐)
        List<JSONObject> setmealList = posGenericDao.getSetmeal(tenancyId, billNum);

        if(null == setmealList)
        {
            setmealList = new ArrayList<JSONObject>();
        }
        //查询套餐中的主菜和必选菜
        if (setmealList.size() > 0){
            for (int i=0; i < setmealList.size(); i++){

                //setmealList.get(i).put("bbuySno", "");
                //setmealList.get(i).put("bgiftSno", "");
                //可使用会员价的会员等级
                setmealList.get(i).put("membergid", new JSONArray());

                //查询菜品做法
                List<JSONObject> cookList = posGenericDao.getCooks(tenancyId, setmealList.get(i).optInt("id"));
                setmealList.get(i).put("cooks", cookList);
                //查询套餐里的主菜信息
                //主菜里的数量，要除以套餐的数量。
                int stemealCount = setmealList.get(i).optInt("number", 1);
                List<JSONObject> mainList = orderDao.getSetmealDish(tenancyId, billNum, "main", stemealCount);
                if(null == mainList)
                {
                    mainList = new ArrayList<JSONObject>();
                }
                //查询套餐中的必选菜品
                List<JSONObject> mandatoryList = orderDao.getSetmealDish(tenancyId, billNum, "mandatory", stemealCount);
                if(null == mandatoryList)
                {
                    mandatoryList = new ArrayList<JSONObject>();
                }
                setmealList.get(i).put("maindish", mainList);
                setmealList.get(i).put("mandatory", mandatoryList);
                setmealList.get(i).put("optional", new JSONArray());

                setmealList.get(i).put("isWeigh", setmealList.get(i).optString("is_weigh"));
                setmealList.get(i).remove("is_weigh");
            }
        }
        //设置订单中的套餐信息
        orderObject.put("setmeal", setmealList);

        //非套餐菜品的查询
        List<JSONObject> normalList = posGenericDao.getNormalitems(tenancyId, billNum);
        if (null != normalList && normalList.size() > 0){
            for (int i=0; i < normalList.size(); i++){
                normalList.get(i).put("bbuySno", "");
                normalList.get(i).put("bgiftSno", "");
                //可使用会员价的会员等级
                normalList.get(i).put("membergid", new JSONArray());
                //查询菜品做法
                List<JSONObject> cookList = posGenericDao.getCooks(tenancyId, normalList.get(i).optInt("id"));
                normalList.get(i).put("cooks", cookList);

                normalList.get(i).put("centPrice", normalList.get(i).optDouble("price",0) * 100);

                normalList.get(i).put("isWeigh", normalList.get(i).optString("is_weigh"));
                normalList.get(i).remove("is_weigh");

                //菜品是否可使用菜品券
                normalList.get(i).put("cpqflag", false);
            }
            orderObject.put("normalitems", normalList);
        }else {
            orderObject.put("normalitems", new JSONArray());
        }

    }*/

}
