package com.tzx.base.common.util;

import java.io.*;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import com.tzx.framework.common.util.io.PropertiesUtil;

/**
 * 配置信息读取工具类
 *
 * <AUTHOR>
 * @since 2016年11月2日18:07:07
 */
public class PosPropertyUtil {
    /**
     *
     */
    private static final Logger logger = Logger.getLogger(PosPropertyUtil.class);

    /**
     *
     */
    private static volatile Properties posSystemProperties = new Properties();

    /**
     *
     */
    private static volatile Properties properties = new Properties();
    
	/**
	 * 
	 */
	private static volatile Map<String, String>	versionPropertiesMap	= new HashMap<String, String>();

    /**
     * @param key
     * @param strs
     * @return
     */
    public static String getMsg(String key, String... strs) {
        return String.format(get(key), strs);
    }

    /**
     * 获取配置值
     *
     * @param key
     * @return
     */
    private static String get(String key) {
        if (properties.isEmpty()) {
            File file = FileUtils.toFile(PosPropertyUtil.class.getResource("/pos"));
            File[] files = file.listFiles(new FilenameFilter() {
                @Override
                public boolean accept(File dir, String name) {
                    return FilenameUtils.isExtension(name, "properties");
                }
            });
            FileInputStream inStream = null;
            for (File f : files) {
                try {
                    inStream = FileUtils.openInputStream(f);
                    properties.load(inStream);
                } catch (IOException e) {
                    logger.error("加载配置文件" + f.getName() + "错误！", e);
                } finally {
                    IOUtils.closeQuietly(inStream);
                }
            }
        }
        return StringUtils.defaultIfEmpty(properties.getProperty(key), StringUtils.EMPTY);
    }

    /**
     * @param key
     * @return
     */
    public static String loadPosSystemProperties(String key) {
		if (posSystemProperties.isEmpty()) {
			File file = new File(System.getProperty("catalina.home") + "/webapps/ROOT/config/systemParam.properties");
			FileInputStream inStream = null;
			try {
				if (!file.exists()) {
					file = new File(PosPropertyUtil.class.getClassLoader().getResource("/").getPath().replace("WEB-INF/classes","config/systemParam.properties"));
				}
				inStream = FileUtils.openInputStream(file);
				posSystemProperties.load(inStream);
			} catch (IOException e) {
				logger.error("加载systemParam.properties文件出错!", e);
			} finally {
				IOUtils.closeQuietly(inStream);
			}
		}
		return posSystemProperties.getProperty(key);
	}

	public static Properties load(File file)
	{
		Properties properties = new Properties();
		
		Reader in = null;
		
		try
		{
			in = new InputStreamReader(new FileInputStream(file), "UTF-8");
			
			properties.load(in);
		}
		catch(Exception e)
		{
			//e.printStackTrace();
		}
		finally
		{
			try
			{
				if(in != null)in.close();
			}
			catch (IOException e)
			{
				//e.printStackTrace();
			}
		}
		
		return properties;
	}

	public static void save(Properties properties, File file, String comments)
	{
		Writer out = null;
		
		try
		{
			out = new OutputStreamWriter(new FileOutputStream(file), "UTF-8");
			
			properties.store(out, comments);
		}
		catch(Exception e)
		{
			//e.printStackTrace();
		}
		finally
		{
			try
			{
				if(out != null)out.close();
			}
			catch (IOException e)
			{
				//e.printStackTrace();
			}
		}
	}
	
	/** 获取门店版本配置信息
	 * @return
	 */
	public static Map<String, String> getVersionProperties()
	{
		try
		{
			if (null == versionPropertiesMap || versionPropertiesMap.isEmpty())
			{
				String filePath = System.getProperty("catalina.home") + System.getProperty("file.separator") + "webapps/ROOT/config/version.properties";
				if ((new File(filePath)).exists())
				{
					versionPropertiesMap = PropertiesUtil.setPropertiesFileToMap(filePath);
				}
			}
			return versionPropertiesMap;
		}
		catch (FileNotFoundException e)
		{
			logger.info("获取版本参数配置失败:" + e.getLocalizedMessage());
			return null;
		}
	}

}
