package com.tzx.clientorder.wechatprogram.bo.adapter;

import java.util.List;

import com.tzx.base.entity.PosSoldOut;
import com.tzx.clientorder.wechatprogram.bo.PromptService;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.exception.WxErrorCode;
import com.tzx.framework.common.util.CommonUtil;
import com.tzx.framework.common.util.SpringConext;
import com.tzx.pos.base.constant.SysDictionary;

import net.sf.json.JSONObject;

public class PromptServiceAdapter
{
	private PromptService promptService;

	public PromptServiceAdapter(String channel)
	{
		if (CommonUtil.hv(channel))
		{
			switch (channel)
			{
				case SysDictionary.CHANEL_QIMAI:
					promptService = (PromptService) SpringConext.getApplicationContext().getBean(PromptService.QIMAI_NAME);
					break;

				default:
					throw SystemException.getInstance(WxErrorCode.CHANNEL_NOT_SUPPORT_ERROR);
			}
		}
	}

	/**
	 * 同步菜品基础数据接口
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @throws Exception
	 */
	public void syncDishData(String tenancyId, int storeId) throws Exception
	{
		if (null == promptService)
		{
			return;
		}
		promptService.syncDishData(tenancyId, storeId);
	}

	/**
	 * 同步估清数据
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param channel
	 * @throws Exception
	 */
	public JSONObject syncSoldoutData(String tenancyId, int storeId, List<PosSoldOut> soldList) throws Exception
	{
		if (null == promptService)
		{
			return null;
		}
		return promptService.syncSoldoutData(tenancyId, storeId, soldList);
	}

	/** 取消估清同步
	 * @param tenancyId
	 * @param storeId
	 * @param soldList
	 * @return
	 * @throws Exception
	 */
	public JSONObject cancelSoldoutData(String tenancyId, int storeId, List<PosSoldOut> soldList) throws Exception
	{
		if (null == promptService)
		{
			return null;
		}
		return promptService.cancelSoldoutData(tenancyId, storeId, soldList);
	}

	/**
	 * 门店退单
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param out_order_id
	 * @param orderSource
	 * @param channel
	 * @return
	 * @throws Exception
	 */
	public JSONObject orderRefund(String tenancyId, int storeId, String billNum, String orderNum, String tableCode, String channel) throws Exception
	{
		if (null == promptService)
		{
			return null;
		}
		return promptService.orderRefund(tenancyId, storeId, billNum, orderNum, tableCode, channel);
	}

	/**
	 * 结账通知
	 * 
	 * @param storeId
	 * @param billNum
	 * @param channel
	 * @return
	 * @throws Exception
	 */
	public JSONObject completeOrder(String tenancyId, int storeId, String billNum) throws Exception
	{
		if (null == promptService)
		{
			return null;
		}
		return promptService.completeOrder(tenancyId, storeId, billNum);
	}

	/**
	 * 上传订单
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @param channel
	 * @return
	 * @throws Exception
	 */

	public JSONObject uploadOrder(String tenancyId, int storeId, String billNum, int operateType,String sTable,String rTable, String sBillNum) throws Exception
	{
		if (null == promptService)
		{
			return null;
		}
		return promptService.uploadOrder(tenancyId, storeId, billNum, operateType, sTable, rTable,sBillNum);
	}

	/**
	 * 锁定订单
	 * 
	 * @param billNum
	 * @param channel
	 * @return
	 * @throws Exception
	 */
	public JSONObject lockOrder(String tenancyId, int storeId, String billNum) throws Exception
	{
		if (null == promptService)
		{
			return null;
		}
		return promptService.lockOrder(tenancyId, storeId, billNum);
	}

	/**
	 * 解锁订单
	 * 
	 * @param billNum
	 * @param channel
	 * @return
	 * @throws Exception
	 */
	public JSONObject unlockOrder(String tenancyId, int storeId, String billNum) throws Exception
	{
		if (null == promptService)
		{
			return null;
		}
		return promptService.unlockOrder(tenancyId, storeId, billNum);
	}

	/**
	 * 修改付款通知
	 *
	 * @param storeId
	 * @param billNum
	 * @param channel
	 * @return
	 * @throws Exception
	 */
	public JSONObject modifyPayOrder(String tenancyId, int storeId, String billNum) throws Exception
	{
		if (null == promptService)
		{
			return null;
		}
		return promptService.modifyPayOrder(tenancyId, storeId, billNum);
	}
}
