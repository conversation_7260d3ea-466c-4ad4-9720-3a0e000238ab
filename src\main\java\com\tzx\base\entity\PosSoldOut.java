package com.tzx.base.entity;

import java.sql.Timestamp;
import java.util.Date;

/**估清表实体映射
 * <AUTHOR>
 *
 */
public class PosSoldOut
{
	private String		tenancy_id;
	private Integer		store_id;
	private Integer		id;
	private Integer		item_id;
	private Double		num;
	private Double		count;
	private Integer		item_unit_id;
	private Date		setdate;
	private String		remark;
	private String		chanel;
	private String		report_date;
	private String		last_operator;
	private Timestamp	last_updatetime;
	private String		soldout_type;
	
	public String getTenancy_id()
	{
		return tenancy_id;
	}
	public void setTenancy_id(String tenancy_id)
	{
		this.tenancy_id = tenancy_id;
	}
	public Integer getStore_id()
	{
		return store_id;
	}
	public void setStore_id(Integer store_id)
	{
		this.store_id = store_id;
	}
	public Integer getId()
	{
		return id;
	}
	public void setId(Integer id)
	{
		this.id = id;
	}
	public Integer getItem_id()
	{
		return item_id;
	}
	public void setItem_id(Integer item_id)
	{
		this.item_id = item_id;
	}
	public Double getNum()
	{
		return ((null != num && false == num.isNaN()) ? num : 0d);
	}
	public void setNum(Double num)
	{
		this.num = num;
	}
	public Double getCount()
	{
		return count;
	}
	public void setCount(Double count)
	{
		this.count = count;
	}
	public Integer getItem_unit_id()
	{
		return item_unit_id;
	}
	public void setItem_unit_id(Integer item_unit_id)
	{
		this.item_unit_id = item_unit_id;
	}
	public Date getSetdate()
	{
		return setdate;
	}
	public void setSetdate(Date setdate)
	{
		this.setdate = setdate;
	}
	public String getRemark()
	{
		return remark;
	}
	public void setRemark(String remark)
	{
		this.remark = remark;
	}
	public String getChanel()
	{
		return chanel;
	}
	public void setChanel(String chanel)
	{
		this.chanel = chanel;
	}
	public String getReport_date()
	{
		return report_date;
	}
	public void setReport_date(String report_date)
	{
		this.report_date = report_date;
	}
	public String getLast_operator()
	{
		return last_operator;
	}
	public void setLast_operator(String last_operator)
	{
		this.last_operator = last_operator;
	}
	public Timestamp getLast_updatetime()
	{
		return last_updatetime;
	}
	public void setLast_updatetime(Timestamp last_updatetime)
	{
		this.last_updatetime = last_updatetime;
	}
	public String getSoldout_type()
	{
		return soldout_type;
	}
	public void setSoldout_type(String soldout_type)
	{
		this.soldout_type = soldout_type;
	}
}
