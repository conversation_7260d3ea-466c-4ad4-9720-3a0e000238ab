package com.tzx.clientorder.wechatprogram.bo;

import net.sf.json.JSONObject;

/**
 * Created by qingui on 2018-06-27.
 */
public interface PromptBasicDataService
{
	String NAME = "com.tzx.clientorder.wechatprogram.bo.impl.PromptBasicDataServiceImp";

	/**
	 * 查询门店的基础资料信息
	 * 
	 * @param responseJson
	 * @param jsobj
	 * @throws Exception
	 */
	public JSONObject getBasicInfo(String tenancyId, int storeId) throws Exception;
	
	/**	查询门店的基础资料信息
	 * @param tenancyId
	 * @param storeId
	 * @return
	 * @throws Exception
	 */
	public JSONObject getBasicData(String tenancyId, int storeId) throws Exception;

	/**
	 * 查询门店的菜品沽清信息
	 * 
	 * @param responseJson
	 * @param jsobj
	 * @throws Exception
	 */
	public JSONObject getSoldOutInfo(String tenancyId, int storeId) throws Exception;
	
	/** 查询门店的菜品沽清信息
	 * @param tenancyId
	 * @param storeId
	 * @return
	 * @throws Exception
	 */
	public JSONObject getSoldOutData(String tenancyId, int storeId) throws Exception;
}
