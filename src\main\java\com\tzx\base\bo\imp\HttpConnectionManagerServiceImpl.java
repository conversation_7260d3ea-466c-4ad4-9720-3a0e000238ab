/** 
 * @(#)CodeFormat.java    1.0   2018-10-11
 * Copyright 北京普照天星科技有限公司. All rights reserved. 
 * PROPRIETARY/CONFIDENTIAL. Use is subject to license terms. 
 */
package com.tzx.base.bo.imp;

import java.security.NoSuchAlgorithmException;
import javax.annotation.PostConstruct;
import javax.net.ssl.SSLContext;

import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.LayeredConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.springframework.stereotype.Service;
import com.tzx.base.bo.HttpConnectionManagerService;
import com.tzx.pos.base.controller.HttpClientConnectionMonitorThread;

/**
 * 连接池管理实现类，支持https协议
 * 
 * <AUTHOR> email:<EMAIL>
 * @version 1.0 2018-10-11
 * @see
 * @since JDK7.0
 * @update
 */
@Service(HttpConnectionManagerService.NAME)
public class HttpConnectionManagerServiceImpl implements HttpConnectionManagerService {
	
	PoolingHttpClientConnectionManager cm = null;
	HttpClientConnectionMonitorThread thread;
	
	public static final int MAX_TOTAL = 50;         // Http线程池最大连接数
	public static final int MAX_PER_ROUTE = 10;     // http线程池每个路由最大连接数
	
	/**
	 * @Description 在spring容器启动的时候执行http连接池初始化。
	 * @param    
	 * @return 
	 * @exception NoSuchAlgorithmException
	 * <AUTHOR> email:<EMAIL>
	 * @version 1.0  2018-10-11
	 * @see
	 */
	@PostConstruct
    public void init() {
        LayeredConnectionSocketFactory sslsf = null;
        try {
            sslsf = new SSLConnectionSocketFactory(SSLContext.getDefault());
        } catch (NoSuchAlgorithmException e) {
           //不要使用 //e.printStackTrace();，如果有必要，用logger.error替代;
        }        
        Registry<ConnectionSocketFactory> socketFactoryRegistry = RegistryBuilder.<ConnectionSocketFactory> create()
                .register("https", sslsf)
                .register("http", new PlainConnectionSocketFactory())
                .build();
        cm =new PoolingHttpClientConnectionManager(socketFactoryRegistry);
        cm.setMaxTotal(MAX_TOTAL);
        cm.setDefaultMaxPerRoute(MAX_PER_ROUTE);
        
        /** 管理 http连接池 */
        thread = new HttpClientConnectionMonitorThread(cm);
    }

	/**
	 * @Description 从Http连接池中获取http连接。
	 * @param    
	 * @return CloseableHttpClient
	 * @exception 
	 * <AUTHOR> email:<EMAIL>
	 * @version 1.0  2018-10-12
	 * @see
	 */
	@Override
	public CloseableHttpClient getHttpClient() {
		CloseableHttpClient httpClient = HttpClients.custom().setConnectionManager(cm).build();
		return httpClient;
	}

}
