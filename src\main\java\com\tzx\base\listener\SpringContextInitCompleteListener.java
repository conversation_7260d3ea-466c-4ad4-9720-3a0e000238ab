package com.tzx.base.listener;

import java.util.Map;

import javax.servlet.ServletContextEvent;
import javax.servlet.ServletContextListener;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import com.tzx.base.scoket.acewill.WebSocketClientUtil;
import com.tzx.clientorder.acewillwechat.bo.WshPosEntranceService;
import com.tzx.framework.common.constant.Constant;
import com.tzx.framework.common.util.SpringConext;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;
//@Component
public class SpringContextInitCompleteListener
		implements ApplicationListener<ContextRefreshedEvent>, ServletContextListener {
	
	private static final Logger LOGGER = LoggerFactory.getLogger(SpringContextInitCompleteListener.class);

	// private ServletContext context = null;
	public void onApplicationEvent(ContextRefreshedEvent event) {
		LOGGER.info("SpringContext initiated complete");
		if (event.getApplicationContext().getParent() == null) {
			try{	
				// 等待Bean初始化完成
				while(SpringConext.getApplicationContext() == null){
					Thread.sleep(1000);
				}  
				LOGGER.info("判断是否启用微生活点餐平台对接......");
				Map<String, String> systemMap = Constant.getSystemMap();
	            String tenent_id= systemMap.get("tenent_id");
	            String store_id= systemMap.get("store_id");
	            
	            DBContextHolder.setTenancyid(tenent_id);
//	            PosPaymentDaoImp posPaymentServiceImp = (PosPaymentDaoImp) SpringConext.getApplicationContext().getBean(PosPaymentDao.NAME);
//	            String isUserWlife= posPaymentServiceImp.getSysParameter(tenent_id,Integer.parseInt(store_id), WLifeConstant.IS_USER_WLIFE);
//	            String userWlifeOrderType= posPaymentServiceImp.getSysParameter(tenent_id,Integer.parseInt(store_id), WLifeConstant.USER_WLIFE_ORDER_TYPE_KEY);
	            WshPosEntranceService wshService = (WshPosEntranceService) SpringConext.getApplicationContext().getBean(WshPosEntranceService.NAME);
	            //微生活H5点餐开关
				if (wshService.isWlifeOrderH5(tenent_id, Integer.valueOf(store_id))){
					WebSocketClientUtil.connect();
				}
			}catch(Exception e){
				LOGGER.error("判断是否启用微生活点餐平台对接出现异常",e);
			}		
			
		}
	}

	// servlet上下文初始化完毕
	public void contextInitialized(ServletContextEvent event) {
		LOGGER.info("ServletContext initiated complete");
	}

	// servlet上下文销毁完毕
	public void contextDestroyed(ServletContextEvent event) {
		LOGGER.info("ServletContext destroyed complete");
	}
}