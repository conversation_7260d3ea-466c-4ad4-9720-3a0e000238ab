package com.tzx.member.yazuo.request.vo.impl;

import com.tzx.member.yazuo.request.vo.YazuoParam;

/** 6.5 会员卡激活
 * <AUTHOR>
 *
 */
public class ActivateCardVo extends YazuoParam
{
	private String merchantNo;//雅座交易门店编号
	private String terminalNo;//门店终端号
	private String cardNo;//卡号
	private String cashierSerial;//第三方订单号（收银流水号）
	private String mobile;//交易手机号
	private Integer cardtypeId;//卡类型ID
	private String idNumber;//身份证号（是否必填见卡类型要求）
	private String password;//交易密码（是否必填见卡类型要求，默认设置000000）
	
	public String getMerchantNo()
	{
		return merchantNo;
	}
	public void setMerchantNo(String merchantNo)
	{
		this.merchantNo = merchantNo;
	}
	public String getTerminalNo()
	{
		return terminalNo;
	}
	public void setTerminalNo(String terminalNo)
	{
		this.terminalNo = terminalNo;
	}
	public String getCardNo()
	{
		return cardNo;
	}
	public void setCardNo(String cardNo)
	{
		this.cardNo = cardNo;
	}
	public String getCashierSerial()
	{
		return cashierSerial;
	}
	public void setCashierSerial(String cashierSerial)
	{
		this.cashierSerial = cashierSerial;
	}
	public String getMobile()
	{
		return mobile;
	}
	public void setMobile(String mobile)
	{
		this.mobile = mobile;
	}
	public Integer getCardtypeId()
	{
		return cardtypeId;
	}
	public void setCardtypeId(Integer cardtypeId)
	{
		this.cardtypeId = cardtypeId;
	}
	public String getIdNumber()
	{
		return idNumber;
	}
	public void setIdNumber(String idNumber)
	{
		this.idNumber = idNumber;
	}
	public String getPassword()
	{
		return password;
	}
	public void setPassword(String password)
	{
		this.password = password;
	}
}
