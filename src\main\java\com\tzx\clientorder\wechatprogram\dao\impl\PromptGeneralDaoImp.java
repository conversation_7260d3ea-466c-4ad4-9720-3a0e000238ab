package com.tzx.clientorder.wechatprogram.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.tzx.clientorder.wechatprogram.dao.PromptGenericDao;
import com.tzx.pos.base.dao.imp.BaseDaoImp;

import net.sf.json.JSONObject;

/**
 * Created by qingui on 2018-06-26.
 */
@Repository(PromptGenericDao.name)
public class PromptGeneralDaoImp extends BaseDaoImp implements PromptGenericDao {

    @Override
    public JSONObject getOrganStatus(String tenancyId, int organId) throws Exception {
        StringBuilder sql = new StringBuilder();
        sql.append(" select * from pos_opt_state pt ");
        sql.append(" where id >");
        sql.append(" (case when (select count(1) from pos_opt_state ");
        sql.append(" where store_id =pt.store_id and content = 'DAYEND') = 0 then 0 else");
        sql.append(" (select id  from pos_opt_state ");
        sql.append(" where store_id =pt.store_id and content = 'DAYEND'");
        sql.append(" order by id desc limit 1) end)");
        sql.append(" and content = 'DAYBEGAIN' and store_id = " + organId);
        List<JSONObject> list = this.query4Json(tenancyId, sql.toString());
        if (null != list && list.size() > 0)
            return list.get(0);
        return null;
    }
}
