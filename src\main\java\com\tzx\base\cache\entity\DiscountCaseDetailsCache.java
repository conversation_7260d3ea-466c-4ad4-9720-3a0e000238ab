package com.tzx.base.cache.entity;

public class DiscountCaseDetailsCache
{
	public static String getSql()
	{
		String sql = new String("select distinct hdcd.item_id,hdcd.unit as item_unit_name,hdcd.rate,hdcd.derate,hdc.id as discount_case_id,hdc.discount_case_name,hdc.discount_case_type from hq_discount_case_details hdcd left join hq_discount_case hdc on hdcd.tenancy_id=hdc.tenancy_id and hdcd.discount_case_id=hdc.id left join hq_discount_case_org hdco on hdc.tenancy_id=hdco.tenancy_id and hdc.id=hdco.discount_case_id where hdc.valid_state='1' and hdco.tenancy_id=::tenancy_id and hdco.store_id=::store_id");
		return sql;
	}
	
	public static String getKey()
	{
		return "discount_case_id,item_id,item_unit_name";
	}
}
