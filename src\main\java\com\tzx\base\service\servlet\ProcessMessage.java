package com.tzx.base.service.servlet;

import com.tzx.base.cache.SoftCache;
import com.tzx.clientorder.wlifeprogram.bo.impl.BasicServiceImp;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import com.tzx.base.bo.ExerciseDataService;
import com.tzx.base.bo.MqTtransferLogService;
import com.tzx.base.bo.ProcessMessageService;
import com.tzx.base.bo.SpecialService;
import com.tzx.clientorder.acewillwechat.bo.WshPosEntranceService;
import com.tzx.clientorder.wlifeprogram.bo.WlifeService;
import com.tzx.framework.common.constant.Constant;
import com.tzx.framework.common.constant.Type;
import com.tzx.framework.common.file.MqFileDownload;
import com.tzx.framework.common.util.SpringConext;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;
import com.tzx.pos.base.service.ThirdPaymentOrderService;
import com.tzx.pos.bo.QuickPassService;
import com.tzx.pos.bo.imp.QuickPassServiceImp;
import com.tzx.pos.bo.payment.PosPaymentService;
import com.tzx.pos.bo.payment.imp.PosPaymentServiceImp;
import com.tzx.pos.service.notify.BohStateObservable;

import net.sf.json.JSONObject;

// @Component

public class ProcessMessage // extends GenericDaoImpl
{
	private Logger				logger		= Logger.getLogger(ProcessMessage.class);
	private Logger				tipLogger	= Logger.getLogger("tip");
	private static final Logger	posThirdPaymentLogger	= Logger.getLogger("pos_third_payment");
	
	public ProcessMessage()
	{
	}

	/**
	 * 
	 * @param data
	 * @return
	 * @throws Exception
	 */
	public String doExecute(String data) throws Exception
	{
		String returnRes = "";

		JSONObject getJson = null;
		
		boolean newVersion = false;
		try
		{
			getJson = JSONObject.fromObject(data);
			if (getJson.isEmpty() || !getJson.containsKey("tenancy_id") || "".equals(getJson.optString("tenancy_id")))
			{
				logger.info("新的下发版本!");
//				return "参数信息为空!";
				newVersion = true;
			}
		}
		catch (Exception e)
		{
			logger.info("得到的消息是：" + data);
			returnRes = "下发转换出错，字符串转json对象出错";
			return returnRes;
		}

		
		String tenancy_id =  null;
		Integer organId =  null;
		
		
		if(newVersion){//新版本下发 mq 通过文件下发内容
			//mq 新版本接收消息，通过文件获取消息
			String url = getJson.optString("url");
			int len = getJson.optInt("l");
			tenancy_id = getJson.optString("t");
			organId = getJson.optInt("sid");
			if(len <= 0){
				logger.error("文件长度错误!长度:"+len);
				tipLogger.info("下发资料失败，未传文件长度!");
				return "文件长度错误!长度:"+len;
			}
			if(url == null || "".equals(url)){
				logger.error("mq 发送消息版本错误,未获取到url!");
				tipLogger.info("下发资料失败，未获取到url!");
				return "mq 发送消息版本错误,未获取到url!";
			}
			String msg = null ;
			try {
				logger.info("文件下载地址:"+url);
				msg = MqFileDownload.downloadMqStrByBlock(url,len,true);
			} catch (Exception e) {
				//e.printStackTrace();
				logger.error("下载文件失败，文件下载地址:"+url);
				tipLogger.info("下发资料失败，下载文件失败，文件下载地址:"+url);
				return "下载文件失败，文件下载地址:"+url;
			}
			if(msg != null && !"".equals(msg)){
				try {
					getJson = JSONObject.fromObject(msg);
				} catch (Exception e) {
					//e.printStackTrace();
					logger.error("文件解析json出错!");
					tipLogger.info("下发资料失败，文件解析json出错!");
					return "文件解析json出错!";
				}
				if (getJson.isEmpty() || !getJson.containsKey("tenancy_id") || "".equals(getJson.optString("tenancy_id")))
				{
					logger.warn("参数信息为空!");
					tipLogger.info("下发资料失败，参数信息为空!");
					return "参数信息为空!";
				}
			} else {
				logger.error("下载失败或者文件为空!"+url);
				tipLogger.info("下发资料失败，文件内容为空!");
				//记录到数据库中
				DBContextHolder.setTenancyid(tenancy_id);
				MqTtransferLogService mqTtransferLogService = (MqTtransferLogService) SpringConext.getBean(MqTtransferLogService.NAME);
				mqTtransferLogService.addDownLogs(tenancy_id, organId, url, len, MqFileDownload.batch_down_size_kb / MqFileDownload.try_times, null);				
				return "文件内容为空!";
			}
		} else {//原来版本下发
			tenancy_id = getJson.optString("tenancy_id");
			organId = getJson.optInt("store_id");
		}
		
		//处理消息体
		try {
			if(organId == null || StringUtils.isEmpty(tenancy_id)){
				logger.error("商户号或者门店id错误!");
				return "商户号或者门店id错误!";
			}
			
			handleMsg(tenancy_id, organId, getJson);
		} catch (Exception e) {
			//e.printStackTrace();
			logger.error("处理消息体异常",e);
		}

		return returnRes;
	}
	
	/**
	 * 处理消息体
	 * @param tenancy_id
	 * @param organId
	 * @param getJson
	 * @return
	 * @throws Exception 
	 */
	public String handleMsg(final String tenancyId,final Integer organId,JSONObject getJson) throws Exception{
		String returnRes = "";
		String data = getJson.toString();
		String type = getJson.optString("type");
		
		if (false == tenancyId.equals(Constant.getSystemMap().get("tenent_id")) || false == organId.equals(Integer.parseInt(Constant.getSystemMap().get("store_id"))))
		{
			return "下发数据商户号或门店ID与门店不一致!";
		}
			
		// 下发要计算下版本
		if (Type.INIT.name().equals(type.toUpperCase()))
		{
			DBContextHolder.setTenancyid(tenancyId);
			ProcessMessageService processMessageService = (ProcessMessageService) SpringConext.getBean(ProcessMessageService.NAME);
			try{
                BohStateObservable.doNotify(Type.BOH_HQ_SYNCHRONIZING);
				processMessageService.updateDataVersion(tenancyId, organId, "1");
				long t1 = System.currentTimeMillis();
				processMessageService.initBasicData(tenancyId, organId, getJson);
				//
				processMessageService.updateBasicVersion(tenancyId, organId);
				//
				processMessageService.syncBasicDataToOnline(tenancyId, organId, getJson);

				returnRes = "下发资料成功!";
				tipLogger.info(returnRes);
				logger.info(returnRes);
				
				// 打印重启需要5秒时间间隔
				long t2 = System.currentTimeMillis();
				if(t2 - t1 < 12000){	
					Thread.sleep(12000-(t2-t1));
				}
				return returnRes;
			}
			catch (Exception e){
                returnRes = "下发资料失败!";
				tipLogger.error(returnRes);
                logger.debug(returnRes, e);  //下发sql异常输出内容过多，改为debug级别
				return returnRes;
			}
			finally	{
                BohStateObservable.doNotify(Type.BOH_NORMAL_RUNNING);
				processMessageService.updateDataVersion(tenancyId, organId, "0");
			}
		}
		else if (Type.SPECIAL.name().equals(type.toUpperCase()))
		{
//			if (getJson.containsKey("data"))
//			{
//				JSONArray specialsJson = getJson.optJSONArray("data");
//				for (int i = 0; i < specialsJson.size(); i++)
//				{
//					String special = specialsJson.optString(i);
//					if (Special.updateproductimgaction.name().equals(special.toLowerCase()))
//					{
//						// 加载图片
//						new Thread()
//						{
//							@Override
//							public void run()
//							{
//								try
//								{
//									DBContextHolder.setTenancyid(tenancyId);
//									ProcessMessageService processMessageService = (ProcessMessageService) SpringConext.getBean(ProcessMessageService.NAME);
//									processMessageService.updateProductPhoto(tenancyId, organId);
//								}
//								catch (BeansException e)
//								{
//									//e.printStackTrace();
//								}
//								catch (Exception e)
//								{
//									//e.printStackTrace();
//								}
//
//							}
//						}.start();
//					}
//					else if (Special.updateversionaction.name().equals(special.toLowerCase()))
//					{
//						String filePath = System.getProperty("catalina.base") + System.getProperty("file.separator") + "update.lock";
//						File file = new File(filePath);
//						if (file.exists())
//						{
//							file.delete();
//						}
//						// file.getParentFile().mkdirs();
//						file.createNewFile();
//					}
//					else if (Special.boh_img_padpc.name().equals(special.toLowerCase()))
//					{
//						new Thread()
//						{
//							@Override
//							public void run()
//							{
//								try
//								{
//									DBContextHolder.setTenancyid(tenancyId);
//									ProcessMessageService processMessageService = (ProcessMessageService) SpringConext.getBean(ProcessMessageService.NAME);
//									processMessageService.downloadViceScreenPhoto(tenancyId, organId);
//								}
//								catch (BeansException e)
//								{
//									//e.printStackTrace();
//								}
//								catch (Exception e)
//								{
//									//e.printStackTrace();
//								}
//
//							}
//						}.start();
//					}
//					else
//					{
//						logger.warn("指令:" + special + "无效!");
//					}
//				}
//			}
//			returnRes = "指令执行成功!";
			
			DBContextHolder.setTenancyid(tenancyId);
			SpecialService messageService = (SpecialService) SpringConext.getBean(SpecialService.NAME);
			returnRes = messageService.specialManagement(tenancyId, organId, getJson);
		}
		else if (Type.ORDER.name().equals(type.toUpperCase())) {
//			if (Oper.add.name().equals(getJson.getString("oper"))) {
//				logger.info("接收订单:" + data);
//				try {
//					DBContextHolder.setTenancyid(tenancyId);
//					ProcessMessageService processMessageService = (ProcessMessageService) SpringConext
//							.getBean(ProcessMessageService.NAME);
//					processMessageService.addOrders(tenancyId, organId, getJson);
//					returnRes = "接收订单成功";
//				} catch (Exception e) {
//					logger.error("==========接收订单失败!\n", e);
//					returnRes = "接收订单失败";
//				}
//			} else if (Oper.add_modify.name().equals(getJson.getString("oper"))) {
//				logger.info("重新接收订单:" + data);
//				try {
//					DBContextHolder.setTenancyid(tenancyId);
//					ProcessMessageService processMessageService = (ProcessMessageService) SpringConext
//							.getBean(ProcessMessageService.NAME);
//					processMessageService.modifyOrder(tenancyId, organId, getJson);
//					returnRes = "重新接收订单";
//				} catch (Exception e) {
//					logger.error("==========重新接收订单!\n", e);
//					returnRes = "重新接收订单";
//				}
//			} else if (Oper.updatedish.name().equals(getJson.getString("oper"))) {
//				logger.info("接收订单[加菜]:" + data);
//				try {
//					DBContextHolder.setTenancyid(tenancyId);
//					ProcessMessageService processMessageService = (ProcessMessageService) SpringConext
//							.getBean(ProcessMessageService.NAME);
//					processMessageService.updateDish(tenancyId, organId, getJson);
//					returnRes = "接收订单成功";
//				} catch (Exception e) {
//					logger.error("==========接收订单失败!\n", e);
//					returnRes = "接收订单失败";
//				}
//			} else if (Oper.cancle.name().equals(getJson.getString("oper"))) {
//				try {
//					logger.info("订单取消得到的消息是：" + data);
//					DBContextHolder.setTenancyid(tenancyId);
//					ProcessMessageService processMessageService = (ProcessMessageService) SpringConext
//							.getBean(ProcessMessageService.NAME);
//					processMessageService.cancleOrders(tenancyId, organId, getJson);
//					returnRes = "取消订单成功";
//				} catch (Exception e) {
//					logger.info("得到的消息是：" + data);
//					logger.error("取消订单失败", e);
//					//e.printStackTrace();
//					returnRes = "取消订单失败";
//				}
//			} else if (Oper.cancle_modify.name().equals(getJson.getString("oper"))) {
//				try {
//					logger.info("订单重新取消得到的消息是：" + data);
//					DBContextHolder.setTenancyid(tenancyId);
//					ProcessMessageService processMessageService = (ProcessMessageService) SpringConext
//							.getBean(ProcessMessageService.NAME);
//					processMessageService.cancleModifyOrders(tenancyId, organId, getJson);
//					returnRes = "订单重新取消";
//				} catch (Exception e) {
//					logger.info("得到的消息是：" + data);
//					logger.error("订单重新取消失败", e);
//					//e.printStackTrace();
//					returnRes = "订单重新取消失败";
//				}
//			}else if (Oper.opinion.name().equals(getJson.getString("oper"))) {
//				try {
//					DBContextHolder.setTenancyid(tenancyId);
//					ProcessMessageService processMessageService = (ProcessMessageService) SpringConext
//							.getBean(ProcessMessageService.NAME);
//					processMessageService.opinionOrders(tenancyId, organId, getJson);
//					returnRes = "投诉订单成功";
//				} catch (Exception e) {
//					logger.info("得到的消息是：" + data);
//					logger.error("投诉订单失败", e);
//					//e.printStackTrace();
//					returnRes = "投诉订单失败";
//				}
//			} else if (Oper.complete.name().equals(getJson.getString("oper"))) {
//				try {
//					DBContextHolder.setTenancyid(tenancyId);
//					ProcessMessageService processMessageService = (ProcessMessageService) SpringConext
//							.getBean(ProcessMessageService.NAME);
//					processMessageService.completeOrders(tenancyId, organId, getJson);
//					returnRes = "订单完成";
//				} catch (Exception e) {
//					logger.info("得到的消息是：" + data);
//					logger.error("完成订单失败", e);
//					//e.printStackTrace();
//					returnRes = "完成订单失败";
//				}
//			} else {
//				returnRes = "Oper无效";
//			}
			
			DBContextHolder.setTenancyid(tenancyId);
			ProcessMessageService processMessageService = (ProcessMessageService) SpringConext
					.getBean(ProcessMessageService.NAME);
			returnRes = processMessageService.ordersManagement(tenancyId, organId, getJson);
			
		}else if (Type.BILL_PAYMENT.name().equals(type.toUpperCase()))
		{
			DBContextHolder.setTenancyid(tenancyId);
			QuickPassServiceImp q = (QuickPassServiceImp) SpringConext.getApplicationContext().getBean(QuickPassService.NAME);
			PosPaymentServiceImp paymentWayService = (PosPaymentServiceImp) SpringConext.getApplicationContext().getBean(PosPaymentService.NAME);
			PosQuickPassMessage posQuickPassMessage=new PosQuickPassMessage();
			posQuickPassMessage.quickPayMent(q, paymentWayService, getJson);

		}
		else if(Type.QUERY_PAY_STATE.name().equals(type.toUpperCase()))
		{

			logger.debug("<<第三方支付回调================>>" + data);
			posThirdPaymentLogger.info("<<第三方支付回调================>>" + data);
			DBContextHolder.setTenancyid(tenancyId);
			ThirdPaymentOrderService posPaymentService = SpringConext.getApplicationContext().getBean(ThirdPaymentOrderService.class);
			
			posPaymentService.thirdPaymentCallback(tenancyId, organId, getJson);
		} else if(Type.WLIFE_SHOP_INFO.name().equals(type.toUpperCase())){
			if("upload".equals(getJson.getString("oper"))){
				logger.info("微生活基础资料上传==========>>" + data);
				DBContextHolder.setTenancyid(tenancyId);
				
				//微生活点餐H5
				WshPosEntranceService wshPosEntranceService = SpringConext.getApplicationContext().getBean(WshPosEntranceService.class);
				JSONObject uploadBaseDataToWsh = wshPosEntranceService.uploadBaseDataToWsh(tenancyId, organId, getJson);
				returnRes = uploadBaseDataToWsh.optString("msg", "...");
				
				//微生活小程序
				WlifeService wlifeProgramService = SpringConext.getApplicationContext().getBean(WlifeService.class);
				wlifeProgramService.synchrodataDish(tenancyId, organId);
			}
		} 
		else if(Type.DELETE_EXERCISE_DATA.name().equals(type.toUpperCase()))
		{
			logger.info("删除业务数据"+getJson.toString());
			try
			{
				DBContextHolder.setTenancyid(tenancyId);
				ExerciseDataService exerciseDataService = (ExerciseDataService) SpringConext.getBean(ExerciseDataService.NAME);
				returnRes = exerciseDataService.exerciseDataDelete(tenancyId, organId, getJson);
			}
			catch (Exception e)
			{
				logger.info("删除业务数据失败"+e.getMessage());
			}
		}
		else
		{
			logger.warn("消息体不能被处理:" + data);
			return null;
		}
		return returnRes;
	}
	
}
