package com.tzx.member.zs.bo.impl;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import com.tzx.member.zs.request.data.impl.DataAddPreCard;
import net.sf.json.JSONArray;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import com.tzx.base.cache.util.CacheTableUtil;
import com.tzx.framework.common.constant.SysParameterCode;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.CommonUtil;
import com.tzx.framework.common.util.DoubleHelper;
import com.tzx.member.zs.bo.ZsCrmService;
import com.tzx.member.zs.common.constant.ZsCrmConstant;
import com.tzx.member.zs.common.util.ZSRequestUtil;
import com.tzx.member.zs.common.util.ZsCrmUtil;
import com.tzx.member.zs.po.springjdbc.dao.ZSCustomerDao;
import com.tzx.member.zs.request.data.IData;
import com.tzx.member.zs.request.data.impl.DataQueryMember;
import com.tzx.member.zs.request.data.impl.DataQueryPreCard;
import com.tzx.pos.base.Constant;
import com.tzx.pos.base.util.ParamUtil;

import net.sf.json.JSONObject;

/**
 * 众赏会员系统服务接口实现类
 * 
 * <AUTHOR> email:<EMAIL>
 * @version 1.0  2018-12-21
 * @see     
 * @since   JDK7.0
 * @update  
 */
@Service(ZsCrmService.NAME)
public class ZsCrmServiceImpl implements ZsCrmService {
	
	private static final Logger	logger	= Logger.getLogger(ZsCrmService.class);

	/** 3.17	查询会员信息 */
	private static final String	QUERY_MEMBER		= "/rs/queryMember";
	/** 3.18 查询储值卡/会员卡信息 */
	private static final String	QUERY_PRECARD		= "/rs/queryPreCard";
	/**
	 * 3.5.4 发送验证码
	 */
	private static final String	balance_check			= "/rs/sendPreVerifyCode";

	@Resource(name = ZSCustomerDao.NAME)
	protected ZSCustomerDao customerDao;
	
	@Override
	public Data queryMember(Data paramData) throws Exception
	{
		String tenancyId = paramData.getTenancy_id();
		Integer storeId = paramData.getStore_id();

		Data resultData = Data.get(paramData);
		List<?> paramList = paramData.getData();
		if (null == paramList || 0 == paramList.size())
		{
			throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
		}

		JSONObject paramJson = JSONObject.fromObject(paramList.get(0));
//		String customerKey = ParamUtil.getStringValueByObject(paramJson, "customer_key");

		this.queryMember(tenancyId, storeId, paramJson, resultData);

//		if ((null == resultData.getData() || 0 < resultData.getData().size()) && CommonUtil.hv(customerKey))
//		{
//			JSONObject queryJson = new JSONObject();
//			queryJson.put("card_code", customerKey);
//			queryJson.put("type", ZsCrmConstant.CARD_TYPE_PRE);
//
//			this.queryPrecard(tenancyId, storeId, queryJson, resultData);
//		}

		return resultData;
	}
	
	private void queryMember(String tenancyId, Integer storeId, JSONObject paramJson, Data resultData) throws Exception
	{
		logger.info("查询会员卡");
		IData<?> data = new DataQueryMember();
		Data responseData = ZSRequestUtil.sendPostRequest(QUERY_MEMBER, data.getParams(tenancyId, storeId, paramJson, customerDao));

		if (Constant.CODE_SUCCESS == responseData.getCode())
		{
			JSONObject bodyJson = ZSRequestUtil.getResponseObject(responseData);

			if (null != bodyJson && !bodyJson.isEmpty())
			{
				JSONObject dataJson = new JSONObject();
				dataJson.put("mobile", ParamUtil.getStringValueForNullByObject(bodyJson, "mobile"));
				dataJson.put("nick_name", ParamUtil.getStringValueForNullByObject(bodyJson, "nickName"));
				dataJson.put("real_name", ParamUtil.getStringValueForNullByObject(bodyJson, "realName"));
				dataJson.put("gender", ZsCrmUtil.getMemberGender(bodyJson.optInt("gender")));
				dataJson.put("cust_level_name", ParamUtil.getStringValueForNullByObject(bodyJson, "custLevelName"));
				dataJson.put("cust_growth_value", ParamUtil.getIntegerValueByObject(bodyJson, "custGrowthValue"));
				dataJson.put("credit_total", ParamUtil.getIntegerValueByObject(bodyJson, "creTotal"));
				dataJson.put("balance", DoubleHelper.convertF2Y(ParamUtil.getLongValueByObject(bodyJson, "preCardMoney")));
				dataJson.put("type", ZsCrmConstant.CARD_TYPE_MEMBER);
				dataJson.put("card_code", ParamUtil.getStringValueByObject(bodyJson, "mobile"));

				List<JSONObject> DataList = new ArrayList<JSONObject>();
				DataList.add(dataJson);

				resultData.setData(DataList);
			}
		}
		else if (Constant.CODE_CONN_EXCEPTION == responseData.getCode())
		{
			resultData.setCode(Constant.CODE_CONN_EXCEPTION);
			resultData.setMsg(Constant.CODE_CONN_EXCEPTION_MSG);
			resultData.setSuccess(false);
		}
		else
		{
			resultData.setCode(Constant.CODE_AUTH_FAILURE);
			resultData.setMsg(responseData.getMsg());
			resultData.setSuccess(false);
		}
	}

	private void queryPrecard(String tenancyId, Integer storeId, JSONObject paramJson,Data resultData) throws Exception
	{
		logger.info("查询储值卡");
		IData<?> data = new DataQueryPreCard();
		Data responseData = ZSRequestUtil.sendPostRequest(QUERY_PRECARD, data.getParams(tenancyId, storeId, paramJson, customerDao));

		if (Constant.CODE_SUCCESS == responseData.getCode())
		{
			JSONObject bodyJson = ZSRequestUtil.getResponseObject(responseData);
			if (null != bodyJson && !bodyJson.isEmpty())
			{
				JSONObject dataJson = new JSONObject();
				dataJson.put("mobile", ParamUtil.getStringValueForNullByObject(bodyJson, "mobile"));
				dataJson.put("nick_name", "");
				dataJson.put("real_name", "");
				dataJson.put("gender", "未知");
				dataJson.put("cust_level_name", "");
				dataJson.put("cust_growth_value", 0);
				dataJson.put("credit_total", 0);
				dataJson.put("balance", DoubleHelper.convertF2Y(ParamUtil.getLongValueByObject(bodyJson, "preCardMoney")));
				dataJson.put("type", ZsCrmConstant.CARD_TYPE_PRE);
				dataJson.put("card_code", ParamUtil.getStringValueByObject(paramJson, "card_code"));

				List<JSONObject> DataList = new ArrayList<JSONObject>();
				DataList.add(dataJson);

				resultData.setData(DataList);
			}
		}
		else if (Constant.CODE_CONN_EXCEPTION == responseData.getCode())
		{
			resultData.setCode(Constant.CODE_CONN_EXCEPTION);
			resultData.setMsg(Constant.CODE_CONN_EXCEPTION_MSG);
			resultData.setSuccess(false);
		}
		else
		{
			resultData.setCode(Constant.CODE_AUTH_FAILURE);
			resultData.setMsg(responseData.getMsg());
			resultData.setSuccess(false);
		}
	}

	@Override
	public boolean isEnableZSMember(String tenancyId, Integer storeId) throws Exception
	{
		return "1".equals(customerDao.getSysParameter(tenancyId, storeId, SysParameterCode.STORE_IS_START_ZS));
	}

	@Override
	public boolean isSetZSMemberParam(String tenancyId, Integer storeId) throws Exception
	{
		return CommonUtil.hv(CacheTableUtil.getSysParameter(SysParameterCode.APPID_ZS)) && CommonUtil.hv(CacheTableUtil.getSysParameter(SysParameterCode.SECRET_KEY_ZS));
	}

	@Override
	public Data checkZhongShangBalance(Data paramData) throws Exception {
		String tenancyId = paramData.getTenancy_id();
		Integer storeId = paramData.getStore_id();
		Data resultData = Data.get(paramData);
		List<?> paramList = paramData.getData();
		if (null == paramList || 0 == paramList.size())
		{
			throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
		}
		JSONObject paramJson = JSONObject.fromObject(paramList.get(0));

		Double consumeBalance = ParamUtil.getDoubleValueByObject(paramJson, "consume_balance");
		JSONArray card_list = ParamUtil.getJSONArray(paramJson,"card_list");
		Integer card_reduce_money = null;
		if(null != card_list ){ //获取优惠券列表中的第一张券面值
			card_reduce_money=card_list.getJSONObject(0).getInt("card_reduce_money");
		}
		//判断是否使用储值 是否使用优惠券(使用发送验证码)
		if((null !=consumeBalance && 0d<consumeBalance)||(null !=card_reduce_money && 0<card_reduce_money))  {
			IData<?> data = new DataAddPreCard();
			Data responseData = ZSRequestUtil.sendPostRequest(balance_check, data.getParams(tenancyId, storeId, paramJson, customerDao));
			if (Constant.CODE_SUCCESS == responseData.getCode()) {
				//状态编码
				resultData.setCode(Constant.CODE_SUCCESS);
				//状态说明
				resultData.setMsg(responseData.getMsg());
				//返回状态
				resultData.setSuccess(true);
				JSONObject resultJson = new JSONObject();
				resultJson.put("verifyCode_status", "1");
				List<JSONObject> list = new ArrayList<JSONObject>();
				list.add(resultJson);
				resultData.setData(list);
			}
			else if(Constant.CODE_SEND_MSG_MORE == responseData.getCode()){
				//状态编码
				resultData.setCode(Constant.CODE_SUCCESS);
				//状态说明
				resultData.setMsg(responseData.getMsg());
				//返回状态
				resultData.setSuccess(true);
				JSONObject resultJson = new JSONObject();
				resultJson.put("verifyCode_status", "1");
				resultJson.put("sendMsg", "1");
				List<JSONObject> list = new ArrayList<JSONObject>();
				list.add(resultJson);
				resultData.setData(list);
			}
			else if (Constant.CODE_CONN_EXCEPTION == responseData.getCode()) {
				resultData.setCode(Constant.CODE_CONN_EXCEPTION);
				resultData.setMsg(responseData.getMsg());
				resultData.setSuccess(false);

				JSONObject resultJson = new JSONObject();
				resultJson.put("verifyCode_status", "0");
				List<JSONObject> list = new ArrayList<JSONObject>();
				list.add(resultJson);
				resultData.setData(list);
			} else {
				resultData.setCode(Constant.CODE_AUTH_FAILURE);
				resultData.setMsg(responseData.getMsg());
				resultData.setSuccess(false);

				JSONObject resultJson = new JSONObject();
				resultJson.put("verifyCode_status", "0");
				List<JSONObject> list = new ArrayList<JSONObject>();
				list.add(resultJson);
				resultData.setData(list);
			}

		}else{
			resultData.setCode(Constant.CODE_SUCCESS);
			resultData.setMsg(Constant.NOT_EXISTS_BALANCE);
			resultData.setSuccess(false);

			JSONObject resultJson = new JSONObject();
			resultJson.put("verifyCode_status", "0");
			List<JSONObject> list = new ArrayList<JSONObject>();
			list.add(resultJson);
			resultData.setData(list);
		}
		return resultData;
	}

}
