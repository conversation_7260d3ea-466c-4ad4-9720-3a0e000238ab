package com.tzx.member.zs.bo;

import com.tzx.framework.common.entity.Data;

/**
 * 众赏会员系统服务接口
 * 
 * <AUTHOR> email:qiuz<PERSON><EMAIL>
 * @version 1.0  2018-12-21
 * @see     
 * @since   JDK7.0
 * @update  
 */
public interface ZsCrmService {
	
	String	NAME	= "com.tzx.member.zs.bo.impl.ZsCrmServiceImpl";
	
	/**3.16	查询会员信息
	 * @param paramData
	 * @return
	 * @throws Exception
	 */
	public Data queryMember(Data paramData) throws Exception;
	
	/** 是否启用众赏会员
	 * @param tenancyId
	 * @param storeId
	 * @return
	 * @throws Exception
	 */
	public boolean isEnableZSMember(String tenancyId, Integer storeId) throws Exception;
	
	/**
	 * @param tenancyId
	 * @param storeId
	 * @return
	 * @throws Exception
	 */
	public boolean isSetZSMemberParam(String tenancyId, Integer storeId) throws Exception;

	/** 判断是否使用储值
	 * @param paramData
	 * @return
	 * @throws Exception
	 */
	public Data checkZhongShangBalance(Data paramData) throws Exception;
}
