<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="RemoteRepositoriesConfiguration">
    <remote-repository>
      <option name="id" value="releases" />
      <option name="name" value="local private nexus" />
      <option name="url" value="http://nexus.acewill.net:8081/repository/maven-public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="snapshots" />
      <option name="name" value="local private nexus" />
      <option name="url" value="http://nexus.acewill.net:8081/repository/maven-public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="tzxsaas" />
      <option name="name" value="tzxsaas" />
      <option name="url" value="http://nexus.acewill.net:8081/repository/maven-public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="Maven Central repository" />
      <option name="url" value="https://repo1.maven.org/maven2" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="central" />
      <option name="url" value="http://nexus.acewill.net:8081/repository/maven-public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="jboss.community" />
      <option name="name" value="JBoss Community repository" />
      <option name="url" value="https://repository.jboss.org/nexus/content/repositories/public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="nexus" />
      <option name="name" value="local private nexus" />
      <option name="url" value="http://nexus.acewill.net:8081/repository/maven-public/" />
    </remote-repository>
  </component>
</project>