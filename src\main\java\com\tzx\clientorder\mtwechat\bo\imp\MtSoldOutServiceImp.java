package com.tzx.clientorder.mtwechat.bo.imp;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import net.sf.json.JSONObject;

import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import com.tzx.clientorder.mtwechat.bo.MtSoldOutService;
import com.tzx.clientorder.mtwechat.common.constant.WxOrderType;
import com.tzx.clientorder.mtwechat.po.springjdbc.dao.MtSoldOutDao;
import com.tzx.framework.common.util.HttpUtil;
import com.tzx.framework.common.constant.SysParameterCode;
import com.tzx.framework.common.util.CommonUtil;
import com.tzx.pos.base.constant.SysDictionary;

/**
 * Created by qingui on 2018-05-31.
 */
@Service(MtSoldOutService.NAME)
public class MtSoldOutServiceImp implements MtSoldOutService {

    private static final Logger logger = Logger.getLogger(MtSoldOutServiceImp.class);

    @Resource(name = MtSoldOutDao.NAME)
    private MtSoldOutDao mtSoldOutDao;

    @Override
    public void soldOutAll(String tenancyId, int storeId, Date reportDate, String itemIds) throws Exception {
    	List<JSONObject> mtDishs = null;
		if (0 < itemIds.length())
		{
			mtDishs  = mtSoldOutDao.getUnits(tenancyId, itemIds);
		}
		this.soldOutAll(tenancyId, storeId, reportDate, mtDishs);
    }
    
    @Override
    public void soldOutAll(String tenancyId, int storeId, Date reportDate, List<JSONObject> dishs) throws Exception {
//        Map<String, String> paramMap = new HashMap<String, String>();
//        //JSONObject param = new JSONObject();

		// 取消当前门店所有菜品沽清
		this.cancelSoldOut(tenancyId, storeId, dishs);

		// 设置估清
		this.soldOut(tenancyId, storeId, reportDate);

//        paramMap.put("shop_id", storeId + "");
//        //查询菜品id和菜品规格id的list
//        List<JSONObject> list = mtSoldOutDao.getItemUnits(tenancyId, storeId, reportDate);
//        if (null != list && list.size() > 0){
//            paramMap.put("dishs", list.toString());
//            String url = PosPropertyUtil.getMsg("mt.request.url");
//            url += WxOrderType.SOLD_OUT_URL;
//            logger.info("开始调用美团的估清"+"入参是:"+paramMap.toString());
//            String msg = HttpUtil.sendPostRequest(WxOrderType.SOLD_OUT_URL, paramMap);
//            logger.info("美团估清接口返回的信息：" + msg);
//        }else {
//            logger.info("不存在沽清为0的菜品");
//        }

    }

    @Override
    public void soldOut(String tenancyId, int storeId, Date reportDate) throws Exception {
//        Map<String, String> paramMap = new HashMap<String, String>();
        //JSONObject param = new JSONObject();

//        paramMap.put("shop_id", storeId + "");
        //查询菜品id和菜品规格id的list
    	if (SysDictionary.USER_ORDER_TYPE_MEIWEI_PROGRAM.equals(mtSoldOutDao.getSysParameter(tenancyId, storeId, SysParameterCode.USER_ORDER_TYPE_KEY)))
    	{
			List<JSONObject> list = mtSoldOutDao.getItemUnits(tenancyId, storeId, reportDate);
			if (null != list && list.size() > 0)
			{
				Map<String, String> paramMap = new HashMap<String, String>();
				paramMap.put("shop_id", storeId + "");
				paramMap.put("dishs", list.toString());
	//            String url = PosPropertyUtil.getMsg("mt.request.url");
	//            url += WxOrderType.SOLD_OUT_URL;
				logger.info("开始调用美团的估清" + WxOrderType.SOLD_OUT_URL + "参数是:" + paramMap.toString());
				String msg = HttpUtil.sendPostRequest(WxOrderType.SOLD_OUT_URL, paramMap);
				logger.info("美团估清接口返回信息：" + msg);
			}
			else
			{
				logger.info("不存在沽清为0的菜品");
			}
    	}
    }
    
    @Override
    public void cancelSoldOut(String tenancyId, int storeId, String itemIds) throws Exception {
		List<JSONObject> mtDishs = null;
		if (0 < itemIds.length())
		{
			mtDishs = mtSoldOutDao.getUnits(tenancyId, itemIds);
		}
		this.cancelSoldOut(tenancyId, storeId, mtDishs);
    }

    @Override
    public void cancelSoldOut(String tenancyId, int storeId, List<JSONObject> dishs) throws Exception {
//        Map<String, String> paramMap = new HashMap<String, String>();
        //JSONObject param = new JSONObject();

//        paramMap.put("shop_id", storeId + "");
//        paramMap.put("dishs", dishs.toString());

//        String url = PosPropertyUtil.getMsg("mt.request.url");
//        url += WxOrderType.SOLD_OUT_CALCEL_URL;
		if (SysDictionary.USER_ORDER_TYPE_MEIWEI_PROGRAM.equals(mtSoldOutDao.getSysParameter(tenancyId, storeId, SysParameterCode.USER_ORDER_TYPE_KEY)) && !CommonUtil.isNullOrEmpty(dishs) && dishs.size() > 0)
		{
			Map<String, String> paramMap = new HashMap<String, String>();
			paramMap.put("shop_id", storeId + "");
			paramMap.put("dishs", dishs.toString());

			logger.info("开始调用美团的取消估清,入参是" + paramMap.toString());
			String msg = HttpUtil.sendPostRequest(WxOrderType.SOLD_OUT_CALCEL_URL, paramMap);
			logger.info("美团取消估清接口返回信息：" + msg);
		}
		else
		{
			logger.info("不存在沽清为0的菜品");
		}
    }

    /**
     * 取消当前门店所有菜品沽清
     * @param tenancyId
     * @throws Exception
     */
    /*private void cancelAllSoldOut(int storeId, String tenancyId, Date reportDate) throws Exception {
        Map<String, String> paramMap = new HashMap<String, String>();
        //JSONObject param = new JSONObject();
        paramMap.put("shop_id", storeId + "");
        String url = PosPropertyUtil.getMsg("mt.request.url");
        url += WxOrderType.SOLD_OUT_CALCEL_URL;

        List<JSONObject> list = mtSoldOutDao.getItemUnits(tenancyId, storeId, reportDate);
        if (null != list && list.size() > 0){
            paramMap.put("dishs", list.toString());
            logger.info("取消当前门店所有菜品沽清接口入参：" + paramMap.toString());
            String msg = HttpUtil.sendPostRequest(WxOrderType.SOLD_OUT_CALCEL_URL, paramMap);
            logger.info("取消当前门店所有菜品沽清接口返回信息：" + msg);
        }
    }*/
}
