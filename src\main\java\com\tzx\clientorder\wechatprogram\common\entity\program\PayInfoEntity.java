package com.tzx.clientorder.wechatprogram.common.entity.program;

import java.util.List;

public class PayInfoEntity
{

	/**
	 * wlife :wx:微信 alipay:支付宝 balance :微生活储值 coupon :代金券 product :菜品券
	 */
	private String						source;
	/**
	 * 支付金额
	 */
	private Double						amount;
	private Double						credit;				// 抵现积分
	/**
	 * 支付流水号
	 */
	private String						serilno;
	private Double						buyer_pay_amount;	// 用户实付
	private Double						receipt_amount;		// 商家实收金额
	private List<ConsumeCouponsEntity>	coupons_info;

	public String getSource()
	{
		return source;
	}

	public void setSource(String source)
	{
		this.source = source;
	}

	public Double getAmount()
	{
		return ((null != amount && !amount.isNaN()) ? amount : 0d);
	}

	public void setAmount(Double amount)
	{
		this.amount = amount;
	}

	public Double getCredit()
	{
		return credit;
	}

	public void setCredit(Double credit)
	{
		this.credit = credit;
	}

	public String getSerilno()
	{
		return serilno;
	}

	public void setSerilno(String serilno)
	{
		this.serilno = serilno;
	}

	public Double getBuyer_pay_amount()
	{
		return ((null != buyer_pay_amount && !buyer_pay_amount.isNaN()) ? buyer_pay_amount : 0d);
	}

	public void setBuyer_pay_amount(Double buyer_pay_amount)
	{
		this.buyer_pay_amount = buyer_pay_amount;
	}

	public Double getReceipt_amount()
	{
		return ((null != receipt_amount && !receipt_amount.isNaN()) ? receipt_amount : 0d);
	}

	public void setReceipt_amount(Double receipt_amount)
	{
		this.receipt_amount = receipt_amount;
	}

	public List<ConsumeCouponsEntity> getCoupons_info()
	{
		return coupons_info;
	}

	public void setCoupons_info(List<ConsumeCouponsEntity> coupons_info)
	{
		this.coupons_info = coupons_info;
	}
}
