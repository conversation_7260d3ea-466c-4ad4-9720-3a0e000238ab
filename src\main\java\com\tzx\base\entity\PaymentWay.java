package com.tzx.base.entity;

import java.util.Date;

/** 付款方式实体映射表
 * <AUTHOR>
 *
 */
public class PaymentWay {

	
	private String	tenancy_id;

	private Integer	id;

	private String	payment_class;

	private String	payment_name1;

	private Double	rate;

	private String	account_id;

	private String	account_code;

	private String	if_invoicing;

	private String	if_jifen;

	private String	if_prepay;

	private String	if_income;

	private String	status;

	private String	comment;

	private Date	last_updatetime;

	private String	last_operator;

	private String	payment_code;

	private Double	bill_limits;

	private Integer	discount_case;

	private String	is_standard_money;

	private Integer	bank;

	private String	payment_name2;

	private String	third_code;

	private String	is_check;

	private String	is_recharge;

	private Integer	fake_id;

	private String	bank_rate;

	private Integer	if_left_money_credit;

	private Integer	is_open_cashbox;

	private Integer	finance_category;

	private Integer	is_del;

	private Integer	pay_type;

	private String	is_blind;


	public String getTenancy_id() {
		return tenancy_id;
	}

	public void setTenancy_id(String tenancy_id) {
		this.tenancy_id = tenancy_id;
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getPayment_class() {
		return payment_class;
	}

	public void setPayment_class(String payment_class) {
		this.payment_class = payment_class;
	}

	public String getPayment_name1() {
		return payment_name1;
	}

	public void setPayment_name1(String payment_name1) {
		this.payment_name1 = payment_name1;
	}

	public Double getRate() {
		return rate;
	}

	public void setRate(Double rate) {
		this.rate = rate;
	}

	public String getAccount_id() {
		return account_id;
	}

	public void setAccount_id(String account_id) {
		this.account_id = account_id;
	}

	public String getAccount_code() {
		return account_code;
	}

	public void setAccount_code(String account_code) {
		this.account_code = account_code;
	}

	public String getIf_invoicing() {
		return if_invoicing;
	}

	public void setIf_invoicing(String if_invoicing) {
		this.if_invoicing = if_invoicing;
	}

	public String getIf_jifen() {
		return if_jifen;
	}

	public void setIf_jifen(String if_jifen) {
		this.if_jifen = if_jifen;
	}

	public String getIf_prepay() {
		return if_prepay;
	}

	public void setIf_prepay(String if_prepay) {
		this.if_prepay = if_prepay;
	}

	public String getIf_income() {
		return if_income;
	}

	public void setIf_income(String if_income) {
		this.if_income = if_income;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getComment() {
		return comment;
	}

	public void setComment(String comment) {
		this.comment = comment;
	}

	public Date getLast_updatetime() {
		return last_updatetime;
	}

	public void setLast_updatetime(Date last_updatetime) {
		this.last_updatetime = last_updatetime;
	}

	public String getLast_operator() {
		return last_operator;
	}

	public void setLast_operator(String last_operator) {
		this.last_operator = last_operator;
	}

	public String getPayment_code() {
		return payment_code;
	}

	public void setPayment_code(String payment_code) {
		this.payment_code = payment_code;
	}

	public Double getBill_limits() {
		return bill_limits;
	}

	public void setBill_limits(Double bill_limits) {
		this.bill_limits = bill_limits;
	}

	public Integer getDiscount_case() {
		return discount_case;
	}

	public void setDiscount_case(Integer discount_case) {
		this.discount_case = discount_case;
	}

	public String getIs_standard_money() {
		return is_standard_money;
	}

	public void setIs_standard_money(String is_standard_money) {
		this.is_standard_money = is_standard_money;
	}

	public Integer getBank() {
		return bank;
	}

	public void setBank(Integer bank) {
		this.bank = bank;
	}

	public String getPayment_name2() {
		return payment_name2;
	}

	public void setPayment_name2(String payment_name2) {
		this.payment_name2 = payment_name2;
	}

	public String getThird_code() {
		return third_code;
	}

	public void setThird_code(String third_code) {
		this.third_code = third_code;
	}

	public String getIs_check() {
		return is_check;
	}

	public void setIs_check(String is_check) {
		this.is_check = is_check;
	}

	public String getIs_recharge() {
		return is_recharge;
	}

	public void setIs_recharge(String is_recharge) {
		this.is_recharge = is_recharge;
	}

	public Integer getFake_id() {
		return fake_id;
	}

	public void setFake_id(Integer fake_id) {
		this.fake_id = fake_id;
	}

	public String getBank_rate() {
		return bank_rate;
	}

	public void setBank_rate(String bank_rate) {
		this.bank_rate = bank_rate;
	}

	public Integer getIf_left_money_credit() {
		return if_left_money_credit;
	}

	public void setIf_left_money_credit(Integer if_left_money_credit) {
		this.if_left_money_credit = if_left_money_credit;
	}

	public Integer getIs_open_cashbox() {
		return is_open_cashbox;
	}

	public void setIs_open_cashbox(Integer is_open_cashbox) {
		this.is_open_cashbox = is_open_cashbox;
	}

	public Integer getFinance_category() {
		return finance_category;
	}

	public void setFinance_category(Integer finance_category) {
		this.finance_category = finance_category;
	}

	public Integer getIs_del() {
		return is_del;
	}

	public void setIs_del(Integer is_del) {
		this.is_del = is_del;
	}

	public Integer getPay_type() {
		return pay_type;
	}

	public void setPay_type(Integer pay_type) {
		this.pay_type = pay_type;
	}

	public String getIs_blind() {
		return is_blind;
	}

	public void setIs_blind(String is_blind) {
		this.is_blind = is_blind;
	}
}
