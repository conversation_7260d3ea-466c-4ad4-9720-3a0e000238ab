package com.tzx.clientorder.acewillwechat.po.springjdbc.dao.imp;

import com.tzx.framework.common.util.dao.impl.GenericDaoImpl;
import com.tzx.clientorder.acewillwechat.po.springjdbc.dao.PreorderDao;
import net.sf.json.JSONObject;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by qin-gui on 2018-03-15.
 */
@Repository(PreorderDao.NAME)
public class PreorderDaoImp extends GenericDaoImpl implements PreorderDao{

    @Override
    public String getMemberType(String tenancyId) throws Exception {
        StringBuffer sql = new StringBuffer();
        sql.append(" select para_value from sys_parameter where para_code = 'CustomerType' limit 1");
        return this.getString(tenancyId, sql.toString());
    }

    @Override
    public JSONObject getBillDiscount(String tenancyId, int storeId, String tableCode) throws Exception {
        StringBuffer sql = new StringBuffer();
        sql.append(" select pb.report_date,COALESCE(pb.shift_id,0) as shift_id,COALESCE(pb.pos_num,'') as pos_num,pb.bill_num,pb.order_num,pb.source,")
            .append(" COALESCE(pb.discount_mode_id,0) as discount_mode_id,COALESCE(pb.discount_case_id,0) as discount_id,COALESCE(pb.discount_rate,100) as discount_rate,COALESCE(pb.discount_reason_id,0) as discount_reason_id,pb.payment_state,")
            .append(" pbm.card_code,COALESCE(pbm.mobil,'') as mobil,COALESCE(pbm.customer_code,'') as customer_code,COALESCE(pbm.customer_name,'') as customer_name,COALESCE(pbm.consume_before_credit,0) as customer_credit")
            .append(" from pos_bill pb")
            .append(" left join pos_bill_member pbm on pb.bill_num = pbm.bill_num")
            .append(" where pb.store_id = "+ storeId +" and pb.fictitious_table = '"+ tableCode +"' and pb.bill_property = 'OPEN'");
        List<JSONObject> list = this.query4Json(tenancyId, sql.toString());
        if (null != list && list.size() > 0)
            return list.get(0);
        return null;
    }
}
