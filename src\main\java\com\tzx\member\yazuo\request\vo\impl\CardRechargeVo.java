package com.tzx.member.yazuo.request.vo.impl;

import java.util.List;

import com.tzx.member.yazuo.common.entity.YazuoPayInfoEntity;
import com.tzx.member.yazuo.common.entity.YazuoPayWayEntity;
import com.tzx.member.yazuo.request.vo.YazuoParam;

/** 6.6 会员卡充值
 * <AUTHOR>
 *
 */
public class CardRechargeVo extends YazuoParam
{
	private String						merchantNo;
	private String						terminalNo;
	private String						cardNo;
	private String						cashierSerial;
	private String						orderId;
	private Double						storeNumber;
	private List<YazuoPayWayEntity>		payWays;
	private List<YazuoPayInfoEntity>	mixPayList;
	private String						financeDate;
	
	public String getMerchantNo()
	{
		return merchantNo;
	}

	public void setMerchantNo(String merchantNo)
	{
		this.merchantNo = merchantNo;
	}

	public String getTerminalNo()
	{
		return terminalNo;
	}

	public void setTerminalNo(String terminalNo)
	{
		this.terminalNo = terminalNo;
	}

	public String getCardNo()
	{
		return cardNo;
	}

	public void setCardNo(String cardNo)
	{
		this.cardNo = cardNo;
	}

	public String getCashierSerial()
	{
		return cashierSerial;
	}

	public void setCashierSerial(String cashierSerial)
	{
		this.cashierSerial = cashierSerial;
	}

	public Double getStoreNumber()
	{
		return storeNumber;
	}

	public void setStoreNumber(Double storeNumber)
	{
		this.storeNumber = storeNumber;
	}

	public String getOrderId()
	{
		return orderId;
	}

	public void setOrderId(String orderId)
	{
		this.orderId = orderId;
	}

	public List<YazuoPayWayEntity> getPayWays()
	{
		return payWays;
	}

	public void setPayWays(List<YazuoPayWayEntity> payWays)
	{
		this.payWays = payWays;
	}

	public String getFinanceDate()
	{
		return financeDate;
	}

	public void setFinanceDate(String financeDate)
	{
		this.financeDate = financeDate;
	}

	public List<YazuoPayInfoEntity> getMixPayList()
	{
		return mixPayList;
	}

	public void setMixPayList(List<YazuoPayInfoEntity> mixPayList)
	{
		this.mixPayList = mixPayList;
	}
}
