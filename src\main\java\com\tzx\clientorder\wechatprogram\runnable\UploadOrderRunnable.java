package com.tzx.clientorder.wechatprogram.runnable;

import org.apache.log4j.Logger;

import com.tzx.clientorder.wechatprogram.bo.adapter.PromptServiceAdapter;

public class UploadOrderRunnable implements Runnable
{
	private static final Logger	logger	= Logger.getLogger(UploadOrderRunnable.class);

	private String				tenancyId;
	private Integer				storeId;
	private String				billNum;
	private String 				sBillNum;
	private String				channel;
	private Integer				operType;
	private String 				sTable;
	private String 				rTable;

	public UploadOrderRunnable(String tenancyId, Integer storeId, String billNum, String channel, Integer operType, String sTable, String rTable, String sBillNum)
	{
		super();
		this.tenancyId = tenancyId;
		this.storeId = storeId;
		this.billNum = billNum;
		this.sBillNum = sBillNum;
		this.channel = channel;
		this.operType = operType;
		this.sTable  = sTable;
		this.rTable  = rTable;

	}

	@Override
	public void run()
	{
		try
		{
			// 休眠500毫秒,等待数据提交完成;
			Thread.sleep(500);

			PromptServiceAdapter adapter = new PromptServiceAdapter(channel);
			adapter.uploadOrder(tenancyId, storeId, billNum, operType, this.sTable, this.rTable,this.sBillNum);
		}
		catch (Exception e)
		{
			logger.info("同步订单失败:", e);
		}
	}

	public void setTenancyId(String tenancyId)
	{
		this.tenancyId = tenancyId;
	}

	public void setStoreId(Integer storeId)
	{
		this.storeId = storeId;
	}

	public void setBillNum(String billNum)
	{
		this.billNum = billNum;
	}

	public void setChannel(String channel)
	{
		this.channel = channel;
	}

	public void setOperType(Integer operType)
	{
		this.operType = operType;
	}

}
