package com.tzx.clientorder.wechatprogram.service.rest;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.apache.log4j.Logger;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import com.tzx.clientorder.common.util.ExceptionPrintUtil;
import com.tzx.clientorder.wechatprogram.bo.PromptBasicDataService;
import com.tzx.clientorder.wechatprogram.bo.PromptGeneralService;
import com.tzx.clientorder.wechatprogram.common.constant.PromptMsgConstant;
import com.tzx.framework.common.constant.Constant;
import com.tzx.framework.common.exception.OtherSystemException;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.exception.WxErrorCode;
import com.tzx.pos.base.util.ParamUtil;

import net.sf.json.JSONObject;

/**
 * 通用业务（门店营业状态、基础资料、沽清信息)
 */
@Controller("PromptGeneralRest")
@RequestMapping("/order/api")
public class PromptGeneralRest
{
	private static final Logger		logger	= Logger.getLogger(PromptGeneralRest.class);

	@Resource(name = PromptGeneralService.NAME)
	private PromptGeneralService	generalService;

	@Resource(name = PromptBasicDataService.NAME)
	private PromptBasicDataService		basicService;

	/**
	 * 查询门店是否营业
	 * 
	 * @param request
	 */
	@RequestMapping(value = "/shopStatus")
	@ResponseBody
	public JSONObject getOrganStatus(HttpServletRequest request, @RequestBody JSONObject jsobj)
	{
		String tenancyId = null;
		if (Constant.systemMap.containsKey("tenent_id"))
		{
			tenancyId = Constant.systemMap.get("tenent_id");
		}

		Integer storeId = Integer.valueOf(Constant.systemMap.get("store_id"));
		if (Constant.systemMap.containsKey("store_id"))
		{
			storeId = Integer.valueOf(Constant.systemMap.get("store_id"));
		}

		// 查询门店营业状态
		JSONObject responseJson = null;
		try
		{
			String orderChannel = ParamUtil.getStringValueByObject(jsobj, "request_source", true, WxErrorCode.NOT_NULL_REQUEST_SOURCE_ERROR);

			responseJson = generalService.getOrganStatus(tenancyId, storeId, orderChannel);
		}
		catch (OtherSystemException se)
		{
			responseJson = new JSONObject();
			ExceptionPrintUtil.buildProgramOtherSysExceptionData(logger, se, responseJson, PromptMsgConstant.QUERY_ORGAN_FAILURE);
		}
		catch (SystemException se)
		{
			responseJson = new JSONObject();
			ExceptionPrintUtil.buildProgramSysExceptionData(logger, se, responseJson, PromptMsgConstant.QUERY_ORGAN_FAILURE);
		}
		catch (Exception e)
		{
			responseJson = new JSONObject();
			ExceptionPrintUtil.buildProgramExceptionData(logger, e, responseJson, PromptMsgConstant.QUERY_ORGAN_FAILURE);
		}

		responseJson.put("request_id", ParamUtil.getStringValueByObject(jsobj, "request_id"));
		// logger.info("查询门店响应出参===" + responseJson.toString());
		return responseJson;

	}

	/**
	 * 查询基础资料
	 * 
	 * @param request
	 */
	@RequestMapping(value = "/shopBaseInfo", method = RequestMethod.POST)
	@ResponseBody
	public JSONObject getBaseData(HttpServletRequest request, @RequestBody JSONObject jsobj)
	{
		logger.info("小程序调用基础资料接口请求==>>" + jsobj.toString());

		String tenancyId = null;
		if (Constant.systemMap.containsKey("tenent_id"))
		{
			tenancyId = Constant.systemMap.get("tenent_id");
		}

		Integer storeId = Integer.valueOf(Constant.systemMap.get("store_id"));
		if (Constant.systemMap.containsKey("store_id"))
		{
			storeId = Integer.valueOf(Constant.systemMap.get("store_id"));
		}

		// 拉取基础资料信息
		JSONObject responseJson = null;
		try
		{
			responseJson = basicService.getBasicInfo(tenancyId, storeId);
		}
		catch (OtherSystemException se)
		{
			responseJson = new JSONObject();
			ExceptionPrintUtil.buildProgramOtherSysExceptionData(logger, se, responseJson, PromptMsgConstant.QUERY_BASIC_FAILURE);
		}
		catch (SystemException se)
		{
			responseJson = new JSONObject();
			ExceptionPrintUtil.buildProgramSysExceptionData(logger, se, responseJson, PromptMsgConstant.QUERY_BASIC_FAILURE);
		}
		catch (Exception e)
		{
			responseJson = new JSONObject();
			ExceptionPrintUtil.buildProgramExceptionData(logger, e, responseJson, PromptMsgConstant.QUERY_BASIC_FAILURE);
		}

		responseJson.put("request_id", ParamUtil.getStringValueByObject(jsobj, "request_id"));
		logger.info("查询基础资料响应出参===" + responseJson.toString());
		return responseJson;
	}

	/**
	 * 查询沽清信息
	 * 
	 * @param request
	 */
	@RequestMapping(value = "/soldoutDish", method = RequestMethod.POST)
	@ResponseBody
	public JSONObject getSoldOut(HttpServletRequest request, @RequestBody JSONObject jsobj)
	{
		logger.info("小程序调用沽清信息接口请求");

		String tenancyId = null;
		if (Constant.systemMap.containsKey("tenent_id"))
		{
			tenancyId = Constant.systemMap.get("tenent_id");
		}

		Integer storeId = Integer.valueOf(Constant.systemMap.get("store_id"));
		if (Constant.systemMap.containsKey("store_id"))
		{
			storeId = Integer.valueOf(Constant.systemMap.get("store_id"));
		}

		// 查询门店菜品的沽清信息
		JSONObject responseJson = null;
		try
		{
			responseJson = basicService.getSoldOutInfo(tenancyId, storeId);
		}
		catch (OtherSystemException se)
		{
			responseJson = new JSONObject();
			ExceptionPrintUtil.buildProgramOtherSysExceptionData(logger, se, responseJson, PromptMsgConstant.QUERY_SOLD_OUT_FAILURE);
		}
		catch (SystemException se)
		{
			responseJson = new JSONObject();
			ExceptionPrintUtil.buildProgramSysExceptionData(logger, se, responseJson, PromptMsgConstant.QUERY_SOLD_OUT_FAILURE);
		}
		catch (Exception e)
		{
			responseJson = new JSONObject();
			ExceptionPrintUtil.buildProgramExceptionData(logger, e, responseJson, PromptMsgConstant.QUERY_SOLD_OUT_FAILURE);
		}

		responseJson.put("request_id", ParamUtil.getStringValueByObject(jsobj, "request_id"));
		logger.info("查询沽清信息响应出参===" + responseJson.toString());
		return responseJson;
	}
}
