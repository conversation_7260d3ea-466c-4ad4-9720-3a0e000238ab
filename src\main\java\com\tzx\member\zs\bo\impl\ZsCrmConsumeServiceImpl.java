package com.tzx.member.zs.bo.impl;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import com.tzx.framework.common.util.SpringConext;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.ExceptionMessage;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.CommonUtil;
import com.tzx.framework.common.util.DateUtil;
import com.tzx.framework.common.util.DoubleHelper;
import com.tzx.member.common.entity.CrmCardTradingListEntity;
import com.tzx.member.zs.bo.ZsCrmConsumeService;
import com.tzx.member.zs.bo.ZsCrmService;
import com.tzx.member.zs.common.constant.ZsCrmConstant;
import com.tzx.member.zs.common.util.ZSRequestUtil;
import com.tzx.member.zs.common.util.ZsCrmUtil;
import com.tzx.member.zs.po.springjdbc.dao.ZSCustomerDao;
import com.tzx.member.zs.request.data.IData;
import com.tzx.member.zs.request.data.impl.DataLockCard;
import com.tzx.member.zs.request.data.impl.DataQueryMemberCard;
import com.tzx.member.zs.request.data.impl.DataReturnOrder;
import com.tzx.member.zs.request.data.impl.DataSyncOrder;
import com.tzx.pos.base.Constant;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.util.ParamUtil;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.springframework.util.CollectionUtils;

/**
 * 众赏会员系统消费服务接口实现类
 *
 * <AUTHOR> email:<EMAIL>
 * @version 1.0  2018-12-21
 * @see
 * @since   JDK7.0
 * @update
 */
@Service(ZsCrmConsumeService.NAME)
public class ZsCrmConsumeServiceImpl implements ZsCrmConsumeService {

	private static final Logger	logger	= Logger.getLogger(ZsCrmConsumeService.class);

	private static final String	QUERY_MEMBER_CARD	= "/rs/queryMemberCard";
	private static final String	SYNC_BILL_DATA		= "/rs/syncOrder";
	private static final String	LOCK_CARD			= "/rs/lockCard";
	private static final String	REFUND_ORDER		= "/rs/refundOrder";

	@Resource(name = ZsCrmService.NAME)
	private ZsCrmService zsCrmService;

	@Resource(name = ZSCustomerDao.NAME)
	protected ZSCustomerDao customerDao;

	/*
	 * POS调用众赏CRMII系统同步订单信息（通过程序调用）
	 */
	@Override
	public Data syncOrderByBillNum(String tenancyId, int storeId, String billNum,String verifyCode,String customerKey) {
		Data resultData = null;
		try{
			if(false==zsCrmService.isEnableZSMember(tenancyId, storeId))
			{
				logger.info("未启用众赏会员");
				throw SystemException.getInstance(PosErrorCode.NOT_ENABLE_ZHONGSHANG_CUSTOMER_ERROR);
			}

			if (false == zsCrmService.isSetZSMemberParam(tenancyId, storeId))
			{
				logger.info("未配置众赏会员AppId或SecretKey!");
				resultData = Data.get(tenancyId, storeId, Constant.CODE_PARAM_FAILURE);
				resultData.setMsg("未配置众赏会员AppId或SecretKey!");
				return resultData;
			}

			List<JSONObject> billJsonList = customerDao.queryNotSyncByBillNo(billNum);
			if (billJsonList == null || billJsonList.size() == 0)
			{
				resultData = Data.get(tenancyId, storeId, Constant.CODE_PARAM_FAILURE);
				resultData.setMsg("账单已经上传众赏!");
				return resultData;
			}
			JSONObject billJson = billJsonList.get(0);
			//验证码
			billJson.put("verifyCode",verifyCode);
			billJson.put("customerKey",customerKey);
			resultData = execSyncMethod(tenancyId, storeId, billJson);

		}catch(Exception e){
			logger.error("账单同步至众赏会员系统失败，账单号："+billNum+",异常信息："+e.getMessage());
		}
		return resultData;
	}

	@Override
	public Data syncOrderByBillNum(String tenancyId, int storeId, String billNum) {
		{
			Data resultData = null;
			try{
				if(false==zsCrmService.isEnableZSMember(tenancyId, storeId))
				{
					logger.info("未启用众赏会员");
					throw SystemException.getInstance(PosErrorCode.NOT_ENABLE_ZHONGSHANG_CUSTOMER_ERROR);
				}

				if (false == zsCrmService.isSetZSMemberParam(tenancyId, storeId))
				{
					logger.info("未配置众赏会员AppId或SecretKey!");
					resultData = Data.get(tenancyId, storeId, Constant.CODE_PARAM_FAILURE);
					resultData.setMsg("未配置众赏会员AppId或SecretKey!");
					return resultData;
				}

				List<JSONObject> billJsonList = customerDao.queryNotSyncByBillNo(billNum);
				if (billJsonList == null || billJsonList.size() == 0)
				{
					resultData = Data.get(tenancyId, storeId, Constant.CODE_PARAM_FAILURE);
					resultData.setMsg("账单已经上传众赏!");
					return resultData;
				}
				JSONObject billJson = billJsonList.get(0);
				resultData = execSyncMethod(tenancyId, storeId,billJson);

			}catch(Exception e){
				logger.error("账单同步至众赏会员系统失败，账单号："+billNum+",异常信息："+e.getMessage());
			}
			return resultData;
		}
	}

	/*
	 * POS调用众赏CRMII系统同步订单信息（通过定时任务执行）
	 */
	@Override
	public void syncOrderByTask() {
		try{
			String tenancyId = com.tzx.framework.common.constant.Constant.getSystemMap().get("tenent_id");
			int storeId = Integer.valueOf(com.tzx.framework.common.constant.Constant.getSystemMap().get("store_id"));
			if (false == zsCrmService.isEnableZSMember(tenancyId, storeId))
			{
				logger.info("未启用众赏会员");
				return;
			}

			if (false == zsCrmService.isSetZSMemberParam(tenancyId, storeId))
			{
				logger.info("未配置众赏会员AppId或SecretKey!");
				return;
			}
			// 获取最近两天的营业日期
			Date date = customerDao.getReportDate(tenancyId,storeId);
			if(date == null){
				logger.error("没有获取到当前营业日期，与众赏CRM系统同步账单失败！");
				return;
			}
			String reportDate =DateUtil.format(date);
			String beforeReportDate = customerDao.getBeforeReportDate(tenancyId,storeId,reportDate);
			if(beforeReportDate == null) beforeReportDate = "";

			// 同步pos_bill账单表数据
			List<JSONObject> billJsonList = customerDao.queryNotSyncFromBill(tenancyId,storeId,"1",reportDate,beforeReportDate);
			posBillDataSync(tenancyId,storeId,billJsonList);
			billJsonList = customerDao.queryNotSyncFromBill(tenancyId,storeId,"2",reportDate,beforeReportDate);
			posBillDataSync(tenancyId,storeId,billJsonList);

            //同步外卖取消订单
            List<JSONObject> refundOrders=customerDao.query4Json(tenancyId,"SELECT\n" +
                    "	bill_num,\n" +
                    "	'4' oper_type,- 100 shift_id,\n" +
                    "	report_date business_date,\n" +
                    "	'外卖' operator_id,\n" +
                    "	chanel,\n" +
                    "	third_order_code bill_code \n" +
                    "FROM\n" +
                    "	cc_order_list \n" +
                    "WHERE\n" +
                    "	order_state = '08' \n" +
                    "	AND store_id = "+storeId+" \n" +
                    "	AND single_time > '2021-10-01' \n" +
                    "	AND ( bip_code IS NULL OR bip_code <> '1' ) AND length(bill_num) >0;");

            if(!CollectionUtils.isEmpty(refundOrders)){
                for(JSONObject refundOrderJson:refundOrders){
                    IData<?> data = new DataReturnOrder();
                    logger.info("众赏取消外卖单:"+refundOrderJson);
                    Data responseData = ZSRequestUtil.sendPostRequest(REFUND_ORDER, data.getParams(tenancyId, storeId, refundOrderJson, customerDao));
                    if (Constant.CODE_SUCCESS==responseData.getCode()
                           ||  PosErrorCode.ZS_UPLOAD_ORDER_ALREADY_EXISTS_ERROR.getNumber()==responseData.getCode()) {
                        customerDao.execute(tenancyId,"update cc_order_list set bip_code='1' where bill_num='"+refundOrderJson.optString("bill_code")+"'");
                    }
                }
            }

		}catch(Exception e){
			logger.error("POS调用众赏CRMII系统同步账单信息服务接口异常，异常信息："+ExceptionMessage.getExceptionMessage(e));
		}
	}

	/**
	 * @Description 多个账单按顺序逐个处理，并加同步锁。
	 * @param tenancyId : 商户号
	 * @param storeId   : 门店号
	 * @param billJsonList        : 账单数据列表
	 * dataQueryMemberCard : 数据同步对象
	 * billTag             : 账单表标识（1：POS_bill    2：pos_bill2）
	 * @return
	 * @exception Exception
	 * <AUTHOR> email:<EMAIL>
	 * @version 1.0  2018-12-27
	 * @see
	 */
	private boolean posBillDataSync(String tenancyId,int storeId,List<JSONObject> billJsonList){
		boolean bo = true;     // 需同步的账单列表中，如果其中一条同步失败，方法即返回失败
		for(JSONObject billJson : billJsonList){
			// 账单编号
			Data resultData  = execSyncMethod(tenancyId, storeId, billJson);
			if (null!= resultData && Constant.CODE_SUCCESS == resultData.getCode())
			{
				bo = bo && true;
			}
			else
			{
				bo = false;
			}
		}
		return bo;
	}

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param billJson
	 * @return
	 */
	private Data execSyncMethod(String tenancyId, int storeId, JSONObject billJson)
	{
		Data resultData = null;

		String syncTag = billJson.optString("sync_tag_zs");
		String billTag = billJson.optString("bill_tag");// 账单表标识（1：pos_bill ;// 2:pos_bill2）
		String billNum = billJson.optString("bill_num");
		String billProperty = billJson.optString("bill_property");
		Double billAmount = ParamUtil.getDoubleValueByObject(billJson, "bill_amount");

		// 判断当前账单是否已经同步成功
		if (ZsCrmConstant.SYNC_TAG_ZS_SUCCESS.equals(syncTag))
		{
			resultData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
			return resultData;
		}
/*		else if (ZsCrmConstant.SYNC_TAG_ZS_FAILURE.equals(syncTag))
		{
			return resultData;
		}*/

		// 根据账单号加同步锁，防止对同一账单的并发操作问题。
		synchronized (("zs_sync_" + billNum).intern())
		{
			if (null == billJson || 0d == billAmount.doubleValue())
			{
				logger.info("零账单不上传!");
				return resultData;
			}

			try
			{
				IData<?> data = new DataSyncOrder();
				String param = data.getParams(tenancyId, storeId, billJson, customerDao);

				resultData = ZSRequestUtil.sendPostRequest(SYNC_BILL_DATA, param);
			}
			catch (Exception e)
			{
				logger.error(e.getMessage(), e);
			}

			try
			{
				if(SysDictionary.BILL_PROPERTY_CLOSED.equals(billProperty))
				{
					if (Constant.CODE_SUCCESS == resultData.getCode()||PosErrorCode.ZS_UPLOAD_ORDER_ALREADY_EXISTS_ERROR.getNumber()==resultData.getCode())
					{
						resultData.setCode(Constant.CODE_SUCCESS);
						customerDao.updatePosBillSyncTag(tenancyId, billNum, billTag);
					
					}
					else if (PosErrorCode.SEND_REQUEST_ZHONGSHANG_CUSTOMER_ERROR.getNumber() == resultData.getCode())
					{
						customerDao.updatePosBillSyncTag(tenancyId, billNum, ZsCrmConstant.SYNC_TAG_ZS_FAILURE, billTag);
					}
					else
					{
						logger.info("账单同步请求异常");
					}
				}
			}
			catch (Exception e)
			{
				logger.info("修改上传状态失败"+e.getMessage());
			}
		}
		return resultData;
	}

	@Override
	public Data queryMemberCard(Data paramData) throws Exception
	{
		String tenancyId = paramData.getTenancy_id();
		Integer storeId = paramData.getStore_id();

		Data resultData = Data.get(paramData);

		List<?> paramList = paramData.getData();

		if (null == paramList || 0 == paramList.size())
		{
			throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
		}
		JSONObject paramJson = JSONObject.fromObject(paramList.get(0));
		String mobile = ParamUtil.getStringValueByObject(paramJson, "mobile");
		String billNum = ParamUtil.getStringValueByObject(paramJson, "bill_num");
		String customerKey = ParamUtil.getStringValueByObject(paramJson, "customer_key");

		IData<?> data = new DataQueryMemberCard();
		Data responseData = ZSRequestUtil.sendPostRequest(QUERY_MEMBER_CARD, data.getParams(tenancyId, storeId, paramJson, customerDao));

		if (Constant.CODE_SUCCESS == responseData.getCode())
		{
			JSONObject bodyJson = ZSRequestUtil.getResponseObject(responseData);

			List<JSONObject> cardList = new ArrayList<JSONObject>();
			List<JSONObject> actiList = new ArrayList<JSONObject>();
			Double maxFee = 0d;
			Double actiMoney = 0d;
			Double discountMoney = 0d;
			Double discountRate = 0d;
			String discountReason = "";
			Integer discountModeId = 0;
			Double balance = 0d;
			Integer creditTotal = 0;
			Double creditMoney = 0d;
			String verifyType ="";
			if (null != bodyJson && !bodyJson.isEmpty())
			{
				mobile = ParamUtil.getStringValueByObject(bodyJson, "mobile");
				balance = DoubleHelper.convertF2Y(ParamUtil.getLongValueByObject(bodyJson, "preCardMoney"));
				creditTotal = ParamUtil.getIntegerValueByObject(bodyJson, "creTotal");
				creditMoney = DoubleHelper.convertF2Y(ParamUtil.getLongValueByObject(bodyJson, "creChangeMoney"));
				maxFee = ZsCrmUtil.changeF2Y(bodyJson.optLong("maxFee"));
				actiMoney = ZsCrmUtil.changeF2Y(bodyJson.optLong("actiMoney"));
				Integer discountType = ParamUtil.getIntegerValueByObject(bodyJson, "discountType");
				verifyType = ParamUtil.getStringValueByObject(bodyJson, "verifyType");
				if (null != discountType)
				{
					if (ZsCrmConstant.DISCOUNT_TYPE_1 == discountType.intValue())
					{
						JSONObject billJson = customerDao.getPosBillByBillnum(tenancyId, storeId, billNum);
						discountModeId = billJson.optInt("discount_mode_id");
						discountMoney = billJson.optDouble("discount_amount");
						discountRate = billJson.optDouble("discount_rate");
						discountReason = billJson.optString("discount_reason_id");
					}
					else
					{
						discountModeId = SysDictionary.DISCOUNT_MODE_ZHONGSHANG;
						discountMoney = ZsCrmUtil.changeF2Y(bodyJson.optLong("discountMoney"));
						discountRate = ZsCrmUtil.changeF2Y(bodyJson.optLong("discountRate"));
						discountReason = ParamUtil.getStringValueByObject(bodyJson, "discountReason");
					}
				}

				JSONArray cardJsonList = bodyJson.optJSONArray("cardList");
				if (null != cardJsonList && 0 < cardJsonList.size())
				{
					//查询历史使用记录,设置默认
					List<JSONObject> paymentList = customerDao.getBillPaymentForZSByBillNum(tenancyId, storeId, billNum, mobile);
					List<String> hisCouponsList = new ArrayList<String>();
					for(JSONObject payment:paymentList)
					{
						hisCouponsList.add(payment.optString("number"));
					}

					for (Object cardObj : cardJsonList)
					{
						JSONObject cardReJson = JSONObject.fromObject(cardObj);
						String cardId = ParamUtil.getStringValueByObject(cardReJson, "cardId");
						String cardType = ZsCrmUtil.getCardType(cardReJson.optInt("cardType"));
						String validTime = ZsCrmUtil.getDate(cardReJson.optString("validityEndTime"));
						if (null == validTime)
						{
							validTime = "";
						}
						String isCheck = ParamUtil.getStringValueByObject(cardReJson, "isCheck", "0");
						if (0 < hisCouponsList.size())
						{
							//
							isCheck = (hisCouponsList.contains(cardId)) ? "1" : "0";
						}

						String isUseMerge = ParamUtil.getStringValueByObject(cardReJson, "isUseMerge", "1");
						String remark ="";
						if("0".equals(isUseMerge))
						{
							remark = ZsCrmConstant.IS_USEMERGE_REMARK;
						}
						JSONObject cardJson = new JSONObject();
						cardJson.put("card_id", cardId);
						cardJson.put("card_name", ParamUtil.getStringValueByObject(cardReJson, "cardName"));
						cardJson.put("card_type", cardType);
						cardJson.put("card_value", ZsCrmUtil.changeF2Y(cardReJson.optLong("cardValue")));
						cardJson.put("card_reduce_money", ZsCrmUtil.changeF2Y(cardReJson.optLong("cardReduceMoney")));
						cardJson.put("dish_id", ParamUtil.getStringValueByObject(cardReJson, "dishId", ""));
						cardJson.put("is_check", isCheck);
						cardJson.put("is_use_merge", isUseMerge);
						cardJson.put("remark", remark);
						cardJson.put("valid_time", validTime);
						//实收金额
                        cardJson.put("paid_in_amount",ZsCrmUtil.changeF2Y(cardReJson.optLong("paidInAmount")));
						cardList.add(cardJson);
					}
				}

				JSONArray actiJsonList = bodyJson.optJSONArray("actiList");
				if (null != actiJsonList && 0 < actiJsonList.size())
				{
					for (Object actiObj : actiJsonList)
					{
						JSONObject actiReJson = JSONObject.fromObject(actiObj);
						JSONObject actiJson = new JSONObject();
						actiJson.put("acti_id", ParamUtil.getStringValueByObject(actiReJson, "actiId"));
						actiJson.put("acti_name", ParamUtil.getStringValueByObject(actiReJson, "remark"));
						actiJson.put("acti_reduce_money", ZsCrmUtil.changeF2Y(actiReJson.optLong("actiReduceMoney")));
						actiJson.put("acti_type", ParamUtil.getStringValueByObject(actiReJson, "type", ""));
						actiJson.put("dish_id", ParamUtil.getStringValueByObject(actiReJson, "extData", ""));
						//实收金额
                        actiJson.put("paid_in_amount", ZsCrmUtil.changeF2Y(actiReJson.optLong("paidInAmount")));
						actiList.add(actiJson);
					}
				}
			}

			JSONObject dataJson = new JSONObject();
			dataJson.put("mobile", mobile);
			dataJson.put("customerKey", customerKey);
			dataJson.put("balance", balance);
			dataJson.put("credit_total", creditTotal);
			dataJson.put("credit_money", creditMoney);
			dataJson.put("max_fee", maxFee);
			dataJson.put("acti_money", actiMoney);
			dataJson.put("discount_money", discountMoney);
			dataJson.put("discount_rate", discountRate);
			dataJson.put("discount_reason", discountReason);
			dataJson.put("discount_mode_id", discountModeId);
			dataJson.put("verifyType", verifyType);
			dataJson.put("card_list", cardList);
			dataJson.put("acti_list", actiList);

			List<JSONObject> DataList = new ArrayList<JSONObject>();
			DataList.add(dataJson);
			resultData.setData(DataList);
		}
		else if (Constant.CODE_CONN_EXCEPTION == responseData.getCode())
		{
			resultData.setCode(Constant.CODE_CONN_EXCEPTION);
			resultData.setMsg(Constant.CODE_CONN_EXCEPTION_MSG);
			resultData.setSuccess(false);
		}
		else
		{
			resultData.setCode(Constant.CODE_AUTH_FAILURE);
			resultData.setMsg(responseData.getMsg());
			resultData.setSuccess(false);
		}
		return resultData;
	}


	@Override
	public Data queryKnockActivity(Data paramData) throws Exception {
		String tenancyId = paramData.getTenancy_id();
		Integer storeId = paramData.getStore_id();

		Data resultData = Data.get(paramData);

		List<?> paramList = paramData.getData();

		if (null == paramList || 0 == paramList.size())
		{
			throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
		}

		JSONObject paramJson = JSONObject.fromObject(paramList.get(0));
		IData<?> data = new DataQueryMemberCard();

		Data responseData = ZSRequestUtil.sendPostRequest(QUERY_MEMBER_CARD, data.getParams(tenancyId, storeId, paramJson, customerDao));
		if (Constant.CODE_SUCCESS == responseData.getCode())
		{
			JSONObject bodyJson = ZSRequestUtil.getResponseObject(responseData);

			List<JSONObject> actiList = new ArrayList<JSONObject>();
			if (null != bodyJson && !bodyJson.isEmpty())
			{
				JSONArray actiJsonList = bodyJson.optJSONArray("actiList");
				if (null != actiJsonList && 0 < actiJsonList.size())
				{
					for (Object actiObj : actiJsonList)
					{
						JSONObject actiReJson = JSONObject.fromObject(actiObj);
						JSONObject actiJson = new JSONObject();
						actiJson.put("acti_id", ParamUtil.getStringValueByObject(actiReJson, "actiId"));
						actiJson.put("acti_name", ParamUtil.getStringValueByObject(actiReJson, "remark"));
						actiJson.put("acti_reduce_money", ZsCrmUtil.changeF2Y(actiReJson.optLong("actiReduceMoney")));
						actiJson.put("acti_type", ParamUtil.getStringValueByObject(actiReJson, "type", ""));
						actiJson.put("dish_id", ParamUtil.getStringValueByObject(actiReJson, "extData", ""));
                        //实收金额
                        actiJson.put("paid_in_amount", ZsCrmUtil.changeF2Y(actiReJson.optLong("paidInAmount")));
						actiList.add(actiJson);
					}
				}
			}

			JSONObject dataJson = new JSONObject();
			dataJson.put("acti_list", actiList);

			List<JSONObject> DataList = new ArrayList<JSONObject>();
			DataList.add(dataJson);

			resultData.setData(DataList);
		}
		else if (Constant.CODE_CONN_EXCEPTION == responseData.getCode())
		{
			resultData.setCode(Constant.CODE_CONN_EXCEPTION);
			resultData.setMsg(Constant.CODE_CONN_EXCEPTION_MSG);
			resultData.setSuccess(false);
		}
		else
		{
			resultData.setCode(Constant.CODE_AUTH_FAILURE);
			resultData.setMsg(responseData.getMsg());
			resultData.setSuccess(false);
		}
		return resultData;
	}

	@Override
	public Data lockCard(String tenancyId, Integer storeId, JSONObject paramJson) throws Exception
	{
		IData<?> data = new DataLockCard();
		Data responseData = ZSRequestUtil.sendPostRequest(LOCK_CARD, data.getParams(tenancyId, storeId, paramJson, customerDao));
		Data resultData = Data.get();
		if(Constant.CODE_CONN_EXCEPTION ==responseData.getCode())
		{
			resultData.setCode(Constant.CODE_CONN_EXCEPTION);
			resultData.setMsg(Constant.CODE_CONN_EXCEPTION_MSG);
			resultData.setSuccess(false);
		}
		else if (Constant.CODE_SUCCESS != responseData.getCode())
		{
			resultData.setCode(Constant.CODE_AUTH_FAILURE);
			resultData.setMsg(responseData.getMsg());
			resultData.setSuccess(false);
		}
		return resultData;
	}

	@Override
	public Data refundOrder(String tenancyId, Integer storeId, JSONObject paramJson) throws Exception
	{
		String billNum = ParamUtil.getStringValueByObject(paramJson, "bill_num");
		Date reportDate = ParamUtil.getDateValueByObject(paramJson, "business_date");
		Integer operatorId = ParamUtil.getIntegerValueByObject(paramJson, "operator_id");
		Integer shiftId = ParamUtil.getIntegerValueByObject(paramJson, "shift_id");
		String chanel = ParamUtil.getStringValueByObject(paramJson, "chanel");
		String billCode = ParamUtil.getStringValueByObject(paramJson, "bill_code");
		this.insertCrmCardTradingListForCancelConsume(tenancyId, storeId, billNum, reportDate, shiftId, operatorId, chanel,billCode);

		IData<?> data = new DataReturnOrder();
		Data responseData = ZSRequestUtil.sendPostRequest(REFUND_ORDER, data.getParams(tenancyId, storeId, paramJson, customerDao));

		Data resultData = Data.get();
		/*if(Constant.CODE_CONN_EXCEPTION ==responseData.getCode())
		{
			resultData.setCode(Constant.CODE_CONN_EXCEPTION);
			resultData.setMsg(Constant.CODE_CONN_EXCEPTION_MSG);
			resultData.setSuccess(false);
            throw SystemException.getInstance(PosErrorCode.CANCEL_PAYMENT_FAIL);
		}
		else if (Constant.CODE_SUCCESS != responseData.getCode())
		{
			resultData.setCode(Constant.CODE_AUTH_FAILURE);
			resultData.setMsg(responseData.getMsg());
			resultData.setSuccess(false);
            throw SystemException.getInstance(PosErrorCode.CANCEL_PAYMENT_FAIL);
		}*/
        if (Constant.CODE_SUCCESS!=responseData.getCode()) {
            logger.error("众赏退款失败:"+responseData.getMsg());
            throw SystemException.getInstance(PosErrorCode.CANCEL_PAYMENT_FAIL);
        }

		return resultData;
	}

	@Override
	public void insertCrmCardTradingListForConsume(String tenancyId, int storeId, CrmCardTradingListEntity cardTrading) throws Exception
	{
		customerDao.insertCrmCardTradingList(tenancyId, storeId, cardTrading);
	}

	public void insertCrmCardTradingListForCancelConsume(String tenancyId, int storeId, String billNum, Date repotrDate, Integer shiftId, Integer operatorId, String chanel,String billCode)throws Exception
	{
		if(CommonUtil.isNullOrEmpty(billCode))
		{
			return;
		}

		List<CrmCardTradingListEntity> cardTradingList = customerDao.getCrmCardTradingListForConsumeNotCancel(tenancyId, storeId, billNum,billCode);
		if (null != cardTradingList && 0 < cardTradingList.size())
		{
			String operator = customerDao.getEmpNameById(String.valueOf(operatorId), tenancyId, storeId);

			Timestamp currentTime = DateUtil.currentTimestamp();
			CrmCardTradingListEntity cardTrading = cardTradingList.get(0);

			cardTrading.setBusiness_date(repotrDate);
			cardTrading.setShift_id(shiftId);
			cardTrading.setChanel(chanel);
			cardTrading.setBill_code(billNum + "_" + String.valueOf(currentTime.getTime()));
			cardTrading.setBill_code_original(cardTrading.getBill_code());
			cardTrading.setOperat_type(SysDictionary.OPERAT_TYPE_FXF);
			cardTrading.setMain_trading(-cardTrading.getMain_trading());
			cardTrading.setReward_trading(0d);
			Double balance = DoubleHelper.padd(cardTrading.getTotal_balance(), cardTrading.getMain_trading());
			cardTrading.setTotal_balance(balance);
			cardTrading.setReward_balance(0d);
			cardTrading.setMain_balance(balance);
			cardTrading.setInvoice_balance(0d);
			cardTrading.setIs_invoice("0");
			cardTrading.setOperator_id(operatorId);
			cardTrading.setOperator(operator);
			cardTrading.setOperate_time(currentTime);
			cardTrading.setLast_updatetime(currentTime);
			cardTrading.setStore_updatetime(currentTime);

			customerDao.insertCrmCardTradingList(tenancyId, storeId, cardTrading);
		}
	}
}
