package com.tzx.base.po.springjdbc.dao.imp;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import net.sf.json.JSONObject;

import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.stereotype.Repository;

import com.tzx.base.po.springjdbc.dao.ProcessMessageDao;
import com.tzx.framework.common.util.dao.impl.GenericDaoImpl;

/**
 * 
 * <AUTHOR> 2015年8月3日-上午10:08:00
 */
@Repository(ProcessMessageDao.NAME)
public class ProcessMessageDaoImp extends GenericDaoImpl implements ProcessMessageDao
{
	@Override
	public int[] batchInsert(String[] sql) throws Exception
	{
		int[] aa=null;
		try
		{
			aa=this.jdbcTemplate.batchUpdate(sql);
		}catch (Exception e){
			//e.printStackTrace();
			throw e;
		}
		return aa;
	}

	@Override
	@Deprecated
	public int[] batchUpdateVersion(final String tenancyId, final int storeId, final List<JSONObject> param) throws Exception
	{
		String delteSql = "delete from pos_data_version where tenancy_id=? and store_id=? and para_code like 'BASIC_VERSION_%'";

		this.jdbcTemplate.update(delteSql, tenancyId, storeId);

		String sql = "insert into pos_data_version(tenancy_id,store_id,system_name,model_name,para_name,para_code,para_value,para_defaut,para_type,valid_state) values(?,?,'POS','数据同步版本',?,?,?,'1','初始化','1')";

		BatchPreparedStatementSetter pss = new BatchPreparedStatementSetter()
		{
			@Override
			public void setValues(PreparedStatement arg0, int arg1) throws SQLException
			{
				JSONObject json = param.get(arg1);
				arg0.setString(1, tenancyId);
				arg0.setInt(2, storeId);
				arg0.setString(3, json.optString("para_code").replace("BASIC_VERSION_", ""));
				arg0.setString(4, json.optString("para_code"));
				arg0.setString(5, json.optString("para_value"));
			}

			@Override
			public int getBatchSize()
			{
				return param.size();
			}
		};
		return this.jdbcTemplate.batchUpdate(sql, pss);
	}

	@Override
	public String getSysParameter(String tenancyId, int storeId, String para) throws Exception
	{
		String sql = "select trim(para_value) as para_value from sys_parameter where para_code = ? and tenancy_id = ? and store_id = ?";

		SqlRowSet rs = jdbcTemplate.queryForRowSet(sql, new Object[]
		{ para, tenancyId, storeId });

		if (rs.next())
		{
			return rs.getString("para_value");
		}
		return "";
	}

	@Override
	public List<JSONObject> getItemPhoto(String tenancyId) throws Exception
	{
		List<JSONObject> retJson = new ArrayList<JSONObject>();
		String sql = "select photo1,photo2,photo3,photo4,photo5,photo6 from hq_item_info where (photo1 is not null or photo2 is not null or photo3 is not null or photo4 is not null or photo5 is not null or photo6 is not null) and tenancy_id=?";
		SqlRowSet rs = jdbcTemplate.queryForRowSet(sql, new Object[]
		{ tenancyId });

		String[] columns = rs.getMetaData().getColumnNames();
		while (rs.next())
		{
			JSONObject itemJson = new JSONObject();
			for (String column : columns)
			{
				itemJson.element(column.toLowerCase(), rs.getString(column));
			}
			retJson.add(itemJson);
		}
		return retJson;
	}

	@Override
	public void updateEmployeeForStoreId(String tenancyId, int storeId) throws Exception
	{
		StringBuilder sql = new StringBuilder("update employee set store_id=? ");
		this.jdbcTemplate.update(sql.toString(), storeId);
	}

	@Override
	public void updateUserForStoreId(String tenancyId, int storeId) throws Exception
	{
		StringBuilder sql = new StringBuilder("update user_authority set store_id=? ");
		this.jdbcTemplate.update(sql.toString(), storeId);
	}

	@Override
	public List<JSONObject> getViceScreenPhoto(String tenancyId,int storeid) throws Exception
	{
		// TODO Auto-generated method stub
		List<JSONObject> retJson = new ArrayList<JSONObject>();
		String sql = "select img_addr from boh_img_padpc where img_addr is not null and tenancy_id =? and organ_id=?";
		SqlRowSet rs = jdbcTemplate.queryForRowSet(sql, new Object[]
		{ tenancyId,storeid });

		String[] columns = rs.getMetaData().getColumnNames();
		while (rs.next())
		{
			JSONObject itemJson = new JSONObject();
			for (String column : columns)
			{
				itemJson.element(column.toLowerCase(), rs.getString(column));
			}
			retJson.add(itemJson);
		}
		return retJson;
	}

	@Override
	public void insertSysParameter(String tenancyId, int storeId, List<Map<String, Object>> parameterList) throws Exception
	{
		// TODO Auto-generated method stub
		String sql = new String("select setval('sys_parameter_id_seq', (select max(id) from sys_parameter)+1)");
		this.jdbcTemplate.queryForRowSet(sql);

		List<Object[]> batchArgs = new ArrayList<Object[]>();
		if(null!=parameterList && parameterList.size()>0)
		{
			for(Map<String, Object> param:parameterList)
			{
				batchArgs.add(new Object[]{tenancyId,storeId,param.get("system_name"),param.get("model_name"),param.get("para_name"),param.get("para_code"),param.get("para_value"),param.get("para_defaut"),param.get("para_type"),param.get("valid_state"),param.get("values_name"),param.get("para_remark")});
			}
		}
		
		if(batchArgs.size()>0)
		{
			sql =new String("INSERT INTO sys_parameter(tenancy_id,store_id,system_name,model_name,para_name,para_code,para_value,para_defaut,para_type,valid_state,values_name,para_remark) VALUES (?,?,?,?,?,?,?,?,?,?,?,?)");
			this.jdbcTemplate.batchUpdate(sql, batchArgs);
		}
	}

	/**
	  * @Description: 查询本地DB是否存在对应表的版本号
	  * @Title:findTableNameId
	  * @param:@param tableName
	  * @param:@return
	  * @return: int
	  * @author: zhouquan
	  * @version: 1.0
	  * @Date: 2018年4月11日
	*/
	@Override
	public List<JSONObject> findTableNameId(String tableName) {
		List<JSONObject> list = null;
		try {
			String sql = "select id,table_name,table_version from down_table_version where table_name = '"+tableName+"'";
			list = this.query4Json(null, sql);
		} catch (Exception e) {
			// TODO: handle exception
			//e.printStackTrace();
		}
		
		return list;
	}

	/**
	  * @Description: 将下发的表和表的版本号存入本地DB
	  * @Title:insertTableNameAndVersion
	  * @param:@param tableName
	  * @param:@param tableVersion
	  * @return: void
	  * @author: zhouquan
	  * @version: 1.0
	  * @Date: 2018年4月11日
	*/
	@Override
	public String insertTableNameAndVersion(String tenancyId ,int storeId, String tableName,  String tableVersion,String time,String recordCount) {
		String sql = "";
		if(recordCount == null || "".equals(recordCount)){
			sql = "insert into down_table_version(tenancy_id,store_id,type_name,table_name,table_version,createtime,updatetime,state) values ('"+tenancyId+"',"+storeId+",'基础资料下发','"+tableName+"','"+tableVersion+"','"+time+"','"+time+"',1)";
		}else{
			sql = "insert into down_table_version(tenancy_id,store_id,type_name,table_name,table_version,createtime,updatetime,state,table_id) values ('"+tenancyId+"',"+storeId+",'基础资料下发','"+tableName+"','"+tableVersion+"','"+time+"','"+time+"',1,"+recordCount+")";
		}		
	    return sql;
		//this.jdbcTemplate.execute(sql);
	}

	/**
	  * @Description: 在本地DB修改下发的表和表的版本号
	  * @Title:updateTableNameAndVersion
	  * @param:@param tableName
	  * @param:@param tableVersion
	  * @return: void
	  * @author: zhouquan
	  * @version: 1.0
	  * @Date: 2018年4月11日
	*/
	@Override
	public String updateTableNameAndVersion(String tenancyId ,int storeId, String tableName,  String tableVersion,String time,String recordCount) {
		String sql = "";
		if(recordCount == null || "".equals(recordCount)){
			sql = "update down_table_version set table_name = '"+tableName+"' , table_version = '"+tableVersion+"' , createtime = '"+time+"' , updatetime = '"+time+"' where table_name = '"+tableName+"'";
		}else{
			sql = "update down_table_version set table_id = "+recordCount+", table_name = '"+tableName+"' , table_version = '"+tableVersion+"' , createtime = '"+time+"' , updatetime = '"+time+"' where table_name = '"+tableName+"'";
		}
		return sql;
		//this.jdbcTemplate.update(sql);
	}

	@Override
	public String ifExistSequenceOrTable(String sequenceName) throws Exception {
		String sql = "select count(*) as num from pg_class where relname = ? ";
		SqlRowSet rs = jdbcTemplate.queryForRowSet(sql, new Object[]{ sequenceName });
		if (rs.next())
		{
			return rs.getString("num");
		}
		return "0";
	}

	@Override
	public String initSeqValue(String sql) throws Exception {
		SqlRowSet rs = jdbcTemplate.queryForRowSet(sql);
		if (rs.next())
		{
			return rs.getString("num");
		}
		return "0";
	}
}
