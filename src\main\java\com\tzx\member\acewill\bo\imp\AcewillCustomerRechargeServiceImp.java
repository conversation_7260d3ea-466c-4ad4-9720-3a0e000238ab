package com.tzx.member.acewill.bo.imp;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.tzx.framework.common.constant.Oper;
import com.tzx.framework.common.constant.Type;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.CrmErrorCode;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.CommonUtil;
import com.tzx.framework.common.util.DateUtil;
import com.tzx.framework.common.util.DoubleHelper;
import com.tzx.member.acewill.bo.AcewillCustomerRechargeService;
import com.tzx.member.acewill.common.constant.ErrorCodeConstant;
import com.tzx.member.acewill.common.constant.RequestUrlConstant;
import com.tzx.member.acewill.common.constant.WLifeCrmConstant;
import com.tzx.member.acewill.common.util.AcewillUtil;
import com.tzx.member.common.constant.PaymentWayEnum;
import com.tzx.member.common.entity.CrmCardPaymentListEntity;
import com.tzx.member.common.entity.CrmCardTradingListEntity;
import com.tzx.member.common.entity.PosCustomerOperateListEntity;
import com.tzx.member.crm.bo.CustomerCardRechargeService;
import com.tzx.pos.base.Constant;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.util.JsonToDataUtil;
import com.tzx.pos.base.util.ParamUtil;
import com.tzx.thirdpay.bo.ThirdPaymentService;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

@Service(AcewillCustomerRechargeService.NAME)
public class AcewillCustomerRechargeServiceImp extends AcewillCustomerServiceImp implements AcewillCustomerRechargeService
{
	@Resource(name = ThirdPaymentService.NAME)
	private ThirdPaymentService	thirdPaymentService;
	
	@Resource(name = CustomerCardRechargeService.NAME)
	private CustomerCardRechargeService	cardRechargeService;

	public Data findAcewillChargeUser(Data requestData) throws Exception {
		String tenancyId = requestData.getTenancy_id();
		Integer storeId = requestData.getStore_id();
		Data resData = requestData.clone();
		JSONObject jsonO = JSONObject.fromObject(requestData.getData().get(0));
		//String cno = this.getCarCode(requestData);
		String cno = jsonO.optString("card_code");

		if (CommonUtil.hv(cno)) {
			// 组织接口参数
			JSONObject jsonParam = new JSONObject();
			jsonParam.put("cno", cno);
			jsonParam.put("begin_date", "1900-01-01");
			jsonParam.put("end_date",  DateUtil.getNowDateYYDDMM());
			//jsonParam.put("shop_id", getShopId(tenancyId, storeId));  //是否本地消费

			// 请求acewill接口
			List<JSONObject> data = new ArrayList<JSONObject>();
			data.add(jsonParam);
			Data paramData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
			paramData.setData(data);
			paramData.setType(requestData.getType());
			paramData.setT(requestData.getT());
			paramData.setOper(requestData.getOper());

			List<JSONObject> dataList = new ArrayList<JSONObject>();
			try {
				//指定会员储值记录列表
				Data resultData = this.commonPost(paramData, RequestUrlConstant.FIND_ACEWILL_CHARGE_USER);
				JSONObject jsonObj = null;
				if(Constant.CODE_SUCCESS==resultData.getCode()){
					logger.info("微生活会员储值记录接口 返回数据 ==="+JsonToDataUtil.DataToJson(resultData));

					JSONObject dataObj = JSONObject.fromObject(resultData.getData().get(0));
					JSONArray jsonArray  = JSONArray.fromObject(dataObj.getJSONArray("data"));

					//                    Map<String,JSONObject> tradingMap = this.getCrmCardTradingListByCardcode(tenancyId, storeId, cno);

					for(int i = 0;i<jsonArray.size();i++){
						JSONObject resultJson = JSONObject.fromObject(jsonArray.get(i));
						String sid = ParamUtil.getStringValueByObject(resultJson, "sid");
						String thirdBillCode = ParamUtil.getStringValueForNullByObject(resultJson, "biz_id");

						jsonObj = new JSONObject();
						jsonObj.put("charge_id", ParamUtil.getStringValueForNullByObject(resultJson, "charge_id")); // 储值流水
						jsonObj.put("card_code", ParamUtil.getStringValueForNullByObject(resultJson, "cno")); // 会员卡号

						jsonObj.put("org_id",sid); // 储值门店id
						jsonObj.put("org_name", this.getAcewillShopNameById(tenancyId, storeId, sid)); // 储值门店名称

						jsonObj.put("charge_total", DoubleHelper.convertF2Y(ParamUtil.getLongValueByObject(resultJson, "total_fee"))); // 充值金额
						jsonObj.put("money", DoubleHelper.convertF2Y(ParamUtil.getLongValueByObject(resultJson, "fee"))); // 充值金额
						jsonObj.put("gift", DoubleHelper.convertF2Y(ParamUtil.getLongValueByObject(resultJson, "award_fee"))); // 充值金额
						jsonObj.put("award_coupons", resultJson.get("award_coupons")); // 赠送券

						jsonObj.put("type", AcewillUtil.getChargeType(ParamUtil.getIntegerValueByObject(resultJson, "type"))); // 交易类型

						jsonObj.put("pay_time", ParamUtil.getStringValueForNullByObject(resultJson, "pay_time")); // 交易时间

						Integer pay_type = ParamUtil.getIntegerValueByObject(resultJson, "pay_type");

						//                        //微生活支付方式  转换成  tzx支付方式
						//						String paymentClass = PaymentWayEnum.findValue(String.valueOf(pay_type));
						//						Integer paymentId = 0;
						//						if (null != tradingMap && tradingMap.containsKey(thirdBillCode))
						//						{
						//							// 查询本地充值记录,优先获取本地记录付款方式
						//							paymentId = ParamUtil.getIntegerValueByObject(tradingMap.get(thirdBillCode), "payment_id");
						//						}
						//						else if (SysDictionary.PAYMENT_CLASS_CASH.equals(paymentClass))
						//						{
						//							JSONObject paymentWayJson = customerDao.getPaymentWayForStandard(tenancyId, storeId);
						//							if (null != paymentWayJson && !paymentWayJson.isEmpty())
						//							{
						//								paymentId = ParamUtil.getIntegerValueByObject(paymentWayJson, "payment_id");
						//							}
						//						}
						//
						//						if (null == paymentId || 0 == paymentId.intValue())
						//						{
						//							JSONObject paymentWayJson = customerDao.getPaymentWayByPaymentClass(tenancyId, storeId, paymentClass);
						//							if (null != paymentWayJson && !paymentWayJson.isEmpty())
						//							{
						//								paymentId = ParamUtil.getIntegerValueByObject(paymentWayJson, "payment_id");
						//							}
						//						}

						jsonObj.put("charge_type", pay_type); // 交易方式
						jsonObj.put("charge_type_name", AcewillUtil.getPayType(pay_type)); // 交易方式名称
						jsonObj.put("has_receipt",ParamUtil.getIntegerValueByObject(resultJson, "has_receipt")); // 是否开票  1:开票 2:未开票 0:未开票
						String cashier_id = ParamUtil.getStringValueByObject(resultJson, "cashier_id");
						jsonObj.put("cashier_id",cashier_id); // 收银员id
						String cashier_name =this.getAcewillCashierNameById(tenancyId, storeId, cashier_id);
						jsonObj.put("cashier_name",cashier_name); // 收银员名称
						jsonObj.put("remark",ParamUtil.getStringValueForNullByObject(resultJson, "remark")); // 备注
						jsonObj.put("grade",ParamUtil.getIntegerValueByObject(resultJson, "grade")); // 用户等级id
						jsonObj.put("grade_name",ParamUtil.getStringValueForNullByObject(resultJson, "grade_name")); // 用户等级名称
						jsonObj.put("biz_id",thirdBillCode);
						dataList.add(jsonObj);
					}
					resData.setData(dataList);
				}


			} catch (Exception e) {
				//e.printStackTrace();
				logger.info("获取会员储值记录列表请求失败" + e.getMessage());
				resData = Data.get();
				resData.setMsg("获取会员储值记录列表请求失败");
				resData.setCode(Constant.CODE_INNER_EXCEPTION);
				resData.setSuccess(false);
			}
		} else {
			logger.info("根据输入手机号、卡号查找会员，会员卡号为空");
			resData = Data.get();
			resData.setMsg("获取会员卡号为空");
			resData.setCode(Constant.CODE_INNER_EXCEPTION);
			resData.setSuccess(false);
		}
		return resData;
	}
	@Override
	public Data previewAcewillCharge(Data requestData) throws Exception {

		String tenancyId = requestData.getTenancy_id();
		Integer storeId = requestData.getStore_id();
		JSONObject jsonObject = JSONObject.fromObject(requestData.getData().get(0));

		String optNum = ParamUtil.getStringValueByObject(jsonObject, "opt_num");
		String posNum = ParamUtil.getStringValueByObject(jsonObject, "pos_num");
		Integer shiftId = ParamUtil.getIntegerValueByObject(jsonObject, "shift_id");
		String reportDate = ParamUtil.getStringValueByObject(jsonObject, "report_date");
		Double real_money = ParamUtil.getDoubleValueByObject(jsonObject, "real_money");
		Double reward_money = ParamUtil.getDoubleValueByObject(jsonObject, "reward_money");
		String memberName = ParamUtil.getStringValueByObject(jsonObject, "name");
		String cardCode = ParamUtil.getStringValueByObject(jsonObject, "card_code");

		String ts = String.valueOf(DateUtil.currentTimestamp().getTime());
		Data resData = requestData.clone();
		// 组织接口参数
		JSONObject jsonParam = new JSONObject();
		jsonParam.put("cno", cardCode);//卡号
		jsonParam.put("shop_id", getShopId(tenancyId, storeId)); //门店id
		jsonParam.put("cashier_id", -1); //默认
		jsonParam.put("money", DoubleHelper.convertY2F(real_money));//储值实收金额(单位:分)

		int payment_Id = jsonObject.optInt("charge_type"); // tzx支付方式
		JSONObject paymentWayJson = customerDao.getPaymentWayByID(tenancyId, storeId, payment_Id);
		String paymentClass = ParamUtil.getStringValueByObject(paymentWayJson, "payment_class");
		// tzx支付方式 转换成 微生活支付方式
		String charge_type = PaymentWayEnum.findLabel(paymentClass);
		jsonParam.put("charge_type", charge_type);
		
		if(jsonObject.containsKey("remark") && CommonUtil.hv(jsonObject.containsKey("remark"))) {
			jsonParam.put("remark", jsonObject.optString("remark"));
		}
		if(jsonObject.containsKey("is_diy") && CommonUtil.hv(jsonObject.containsKey("is_diy"))){
			jsonParam.put("is_diy", false);
		}
		if(CommonUtil.hv(reward_money)){
			jsonParam.put("reward_money", reward_money);//储值赠送金额(单位:分)
		}
		if(jsonObject.containsKey("salesman") && CommonUtil.hv(jsonObject.containsKey("salesman"))){
			jsonParam.put("recommenderecode", jsonObject.optString("salesman"));
		}
		String billCode = this.createThirdBillCode(tenancyId, storeId, jsonObject);
		jsonParam.put("biz_id",billCode);


		// 请求acewill接口
		List<JSONObject> data = new ArrayList<JSONObject>();
		data.add(jsonParam);
		Data paramData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
		paramData.setData(data);
		paramData.setType(requestData.getType());
		paramData.setT(requestData.getT());
		paramData.setOper(requestData.getOper());
		try {
			Data resultData = this.commonPost(paramData, RequestUrlConstant.PREVIEW_ACEWILL_CHARGE_URL);
			resData.setCode(resultData.getCode());
			resData.setMsg(resultData.getMsg());
			resData.setSuccess(resultData.isSuccess());
			if(Constant.CODE_SUCCESS == resultData.getCode()){
				//储值预览是否记录交易记录
				JSONObject resultJson = JSONObject.fromObject(resultData.getData().get(0));
				resultJson.put("charge_total",DoubleHelper.convertF2Y(ParamUtil.getLongValueByObject(resultJson,"charge_total")));//储值金额
				resultJson.put("money",DoubleHelper.convertF2Y(ParamUtil.getLongValueByObject(resultJson,"money")));//实付金额
				resultJson.put("gift",DoubleHelper.convertF2Y(ParamUtil.getLongValueByObject(resultJson,"gift")));//赠送金额
				resultJson.put("third_bill_code_timestamp",ts);//交易时间
				resultJson.put("third_bill_code",billCode); //交易单号
				resultJson.put("name",memberName);
				resultJson.put("payment_class",paymentClass);
				resultJson.put("paymeng_id",payment_Id);
				List<JSONObject> list = new ArrayList<>();
				list.add(resultJson);
				resData.setData(list);
			}
			else if (ErrorCodeConstant.SHOP_NOT_CHARGE_RULE_CODE == resultData.getCode() || ErrorCodeConstant.BUSINESS_NOT_CHARGE_RULE_CODE == resultData.getCode())
			{
				SystemException se = SystemException.getInstance(CrmErrorCode.GRADE_NOT_CONFIG_CHARGE_RULE_ERROR);
				resData.setCode(se.getErrorCode().getNumber());
				resData.setMsg(se.getErrorMsg());
			}
		} catch (Exception e) {
			logger.info("储值预览请求失败" + e.getLocalizedMessage());
			resData = Data.get();
			resData.setMsg("储值预览请求失败");
			resData.setCode(Constant.CODE_INNER_EXCEPTION);
			resData.setSuccess(false);
		}

		//保存储值预览日志
		String optName = customerDao.getEmpNameById(optNum, tenancyId, storeId);
		StringBuffer newstate = new  StringBuffer();
		newstate.append("实收金额:").append(real_money).append(";");
		newstate.append("赠送金额:").append(reward_money).append(";");
		newstate.append("充值金额:").append((real_money+reward_money)).append(";");
		if(Constant.CODE_SUCCESS == resData.getCode()){
			JSONObject resulJson = JSONObject.fromObject(resData.getData().get(0));
			String  award_credit = resulJson.optString("award_credit");
			String  expired = resulJson.optString("expired");
			newstate.append("赠送积分:").append(award_credit).append(";");
			newstate.append("有效期:").append(expired).append(";");

			if(resulJson.containsKey("award_coupons")){
				Object object = resulJson.get("award_coupons");
				newstate.append("赠送券:").append(object).append(";");
			}
		}
		customerDao.savePosLog(tenancyId, storeId, posNum, optNum, optName, shiftId, DateUtil.parseDate(reportDate), Constant.TITLE, "微生活预储值", "储值业务号:"+billCode, newstate.toString());

		return resData;
	}


	@Override
	public String addCustomerOperateListForRecharge(String tenantId, Integer storeId, String OperatType, JSONObject paramJson) throws Exception {
		String reportDateStr = ParamUtil.getStringValueByObject(paramJson, "report_date", false, null);
		Integer paymentId = ParamUtil.getIntegerValueByObject(paramJson, "charge_type", false, null); //储值方式1:现金 2:银行卡 5:微信 4:支付宝
		Double tradeAmount = ParamUtil.getDoubleValueByObject(paramJson, "money", false, null);
		String deal_id = ParamUtil.getStringValueByObject(paramJson, "deal_id");
		String third_bill_code = ParamUtil.getStringValueByObject(paramJson, "third_bill_code");
		String third_bill_code_timestamp = ParamUtil.getStringValueByObject(paramJson, "third_bill_code_timestamp");

		Date reportDate = DateUtil.parseDate(reportDateStr);

		String isInvoice = ParamUtil.getStringValueByObject(paramJson, "is_invoice");//是否开发票
		if (CommonUtil.isNullOrEmpty(isInvoice))
		{
			isInvoice = "0";
		}

		Timestamp currentTime = DateUtil.currentTimestamp();

		String paymentClass = null;
		if(null !=paymentId &&paymentId>0)
		{
			JSONObject paymentWayJson = customerDao.getPaymentWayByID(tenantId, storeId, paymentId);
			paymentClass = ParamUtil.getStringValueByObject(paymentWayJson, "payment_class");
		}

		PosCustomerOperateListEntity  cardRecharge = customerDao.queryPosCustomerOperateListByBillcode(tenantId, storeId, null, null, null, null, null, null, third_bill_code, null, OperatType);

		if(cardRecharge==null){
			cardRecharge = new PosCustomerOperateListEntity();
			cardRecharge.setTenancy_id(tenantId);
			cardRecharge.setStore_id(storeId);
			cardRecharge.setBusiness_date(reportDate);
			cardRecharge.setOperator_id(ParamUtil.getIntegerValueByObject(paramJson, "opt_num"));
			cardRecharge.setPos_num(ParamUtil.getStringValueByObject(paramJson, "pos_num"));
			cardRecharge.setShift_id(ParamUtil.getIntegerValueByObject(paramJson, "shift_id"));
			cardRecharge.setOperate_time(currentTime);
			cardRecharge.setChanel(ParamUtil.getStringValueByObject(paramJson, "chanel"));
			cardRecharge.setService_type(SysDictionary.SERVICE_TYPE_RECHARGE);
			cardRecharge.setOperat_type(OperatType);
			//cardRecharge.setCustomer_id();
			//cardRecharge.setCustomer_code();
			cardRecharge.setMobil(ParamUtil.getStringValueByObject(paramJson, "mobil"));//手机号
			cardRecharge.setCustomer_name(ParamUtil.getStringValueByObject(paramJson, "name"));//会员姓名
			//cardRecharge.setCard_class_id();
			//cardRecharge.setCard_id();
			cardRecharge.setCard_code(ParamUtil.getStringValueByObject(paramJson, "card_code"));//会员考好
			cardRecharge.setThird_code(null);
			cardRecharge.setThird_bill_code(third_bill_code);//储值业务号
			cardRecharge.setThird_bill_code_timestamp(third_bill_code_timestamp);//储值业务号时间戳
			cardRecharge.setBill_code(deal_id); //微生活 返回 储值订单流水号
			cardRecharge.setTrade_amount(tradeAmount);//储值实收金额
			cardRecharge.setTrade_credit(ParamUtil.getDoubleValueByObject(paramJson, "award_credit"));//赠送积分
			cardRecharge.setDeposit(0d);//押金
			cardRecharge.setSales_price(0d); //售卡金额
			cardRecharge.setSales_person(ParamUtil.getStringValueByObject(paramJson, "salesman"));
			cardRecharge.setBill_code_original(ParamUtil.getStringValueByObject(paramJson, "bill_code_original"));
			cardRecharge.setPayment_id(paymentId);
			cardRecharge.setPayment_class(paymentClass);
			cardRecharge.setIs_invoice(isInvoice);
			cardRecharge.setPayment_state(SysDictionary.THIRD_PAY_STATUS_PAYING);
			cardRecharge.setOperate_state(SysDictionary.CUSTOMER_OPERATE_STATE_WAIT);
			cardRecharge.setCancel_state(SysDictionary.CUSTOMER_OPERATE_CANCEL_STATE_N);
			cardRecharge.setRequest_status(SysDictionary.REQUEST_STATUS_ING);
			cardRecharge.setLast_query_time(currentTime);
			cardRecharge.setQuery_count(0);
			cardRecharge.setAction_type(Type.CUSTOMER_CARD_RECHARGE.name());

			JSONObject extJson = new JSONObject();
			extJson.put("charge_total", ParamUtil.getIntegerValueByObject(paramJson, "charge_total"));//本次储值金
			extJson.put("money", ParamUtil.getIntegerValueByObject(paramJson, "money"));//实收金额
			extJson.put("gift", ParamUtil.getIntegerValueByObject(paramJson, "gift")); //赠送金额
			extJson.put("award_credit", ParamUtil.getIntegerValueByObject(paramJson, "award_credit"));//赠送积分
			extJson.put("expired", ParamUtil.getStringValueByObject(paramJson, "expired")); //储值金额有效期
			//	            extJson.put("deal_id", ParamUtil.getStringValueByObject(paramJson, "deal_id"));//储值订单流水号
			extJson.put("award_coupons", paramJson.get("award_coupons"));//储值订单流水号
			//	            extJson.put("biz_id", third_bill_code);//储值业务号
			extJson.put("salesman_name", ParamUtil.getStringValueByObject(paramJson,"salesman_name"));//销售人员名称
			cardRecharge.setExtend_param(extJson.toString());
			customerDao.insertPosCustomerOperateList(tenantId, storeId, cardRecharge);
		}
		return third_bill_code;
	}



	@Override
	public Data commitAcewillCharge(String tenancyId,Integer storeId,String thirdBillCode, JSONObject jsonObject) throws Exception {

		synchronized (thirdBillCode.intern())
		{
			Timestamp currentTime = DateUtil.currentTimestamp();

			//查询会员操作记录
			PosCustomerOperateListEntity  customerOperate = customerDao.queryPosCustomerOperateListByBillcode(tenancyId, storeId, null, null, null, null, null, null, thirdBillCode, null, SysDictionary.OPERAT_TYPE_CZ);
			if (null == customerOperate)
			{
				Data responseData = Data.get();
				responseData.setCode(Constant.CODE_INNER_EXCEPTION);
				responseData.setMsg("充值记录不存在");
				return responseData;
			}
			
			String is_invoice = customerOperate.getIs_invoice();//是否开发票
			String posNum =customerOperate.getPos_num();
			Integer optNum = customerOperate.getOperator_id();
			Date reportDate = customerOperate.getBusiness_date();
			if(SysDictionary.CUSTOMER_OPERATE_CANCEL_STATE_Y.equals(customerOperate.getCancel_state()))
			{
				Data responseData = Data.get();
				responseData.setCode(Constant.CODE_INNER_EXCEPTION);
				responseData.setMsg("充值已取消");
				return responseData;
			}

			Data resData = new Data();
			// 组织接口参数
			JSONObject jsonParam = new JSONObject();
			jsonParam.put("biz_id", thirdBillCode);
			if (jsonObject.containsKey("is_diy") && CommonUtil.hv(jsonObject.optString("is_diy"))) {//是否自定义
				jsonParam.put("is_diy", 0);
			}

			// 请求acewill接口
			List<JSONObject> data = new ArrayList<JSONObject>();
			data.add(jsonParam);
			Data paramData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
			paramData.setData(data);
			resData = this.commonPost(paramData, RequestUrlConstant.COMMIT_ACEWILL_CHARGE_URL);

			String paymentState = SysDictionary.THIRD_PAY_STATUS_SUCCESS;
			String operateState = SysDictionary.REQUEST_STATUS_ING;
			String requestStatus = SysDictionary.REQUEST_STATUS_ING;
			Integer requestCode = null;
			String requestMsg = null;
			Timestamp finishTime = null;
			JSONObject resultObj = new JSONObject();
			JSONObject extendParamJson = null;
			String comitDealId = null;

			if(CommonUtil.hv(customerOperate.getExtend_param()))
			{
				extendParamJson = JSONObject.fromObject(customerOperate.getExtend_param());
			}
			String operator = customerDao.getEmpNameById(String.valueOf(customerOperate.getOperator_id()), tenancyId, storeId);

			if (resData.isSuccess()) //调用微生储值提交成功
			{

				operateState = SysDictionary.CUSTOMER_OPERATE_STATE_SUCCESS;
				requestStatus = SysDictionary.REQUEST_STATUS_COMPLETE;
				finishTime = currentTime;
				Double invoiceAmount = 0d;
				Double trade_amount = customerOperate.getTrade_amount();
				int  payment_id = customerOperate.getPayment_id();
				if ("1".equals(is_invoice)) //是否开发票
				{
					JSONObject paymentWayJson = customerDao.getPaymentWayByID(tenancyId, storeId, payment_id);

					if ("1".equals(paymentWayJson.optString("if_invoicing")))
					{
						invoiceAmount = customerOperate.getTrade_amount();//实收金额
					}

					resultObj.put("paymenttypename",paymentWayJson.optString("payment_name"));
				}

				resultObj = JSONObject.fromObject(resData.getData().get(0));
				comitDealId = ParamUtil.getStringValueByObject(resultObj, "deal_id");

				Double chargeTotal = ParamUtil.getDoubleValueByObject(extendParamJson, "charge_total");
				Double money = ParamUtil.getDoubleValueByObject(extendParamJson, "money");
				Double gift = ParamUtil.getDoubleValueByObject(extendParamJson, "gift");
				Double awardCredit = ParamUtil.getDoubleValueByObject(extendParamJson, "award_credit");

				//新增会员卡交易记录
				CrmCardTradingListEntity cardTradingList = new CrmCardTradingListEntity();
				cardTradingList.setThird_bill_code(thirdBillCode); //微生活 储值交易id
				cardTradingList.setBill_code(comitDealId);  //储值业务号，收银方保证唯一，提交储值需要biz_id
				cardTradingList.setCard_id(null);
				cardTradingList.setCard_code(customerOperate.getCard_code());
				cardTradingList.setCard_class_id(null);
				cardTradingList.setName(customerOperate.getCustomer_name());
				cardTradingList.setMobil(customerOperate.getMobil());
				cardTradingList.setBusiness_date(reportDate);
				cardTradingList.setShift_id(customerOperate.getShift_id());
				cardTradingList.setChanel(customerOperate.getChanel());
				cardTradingList.setOperat_type(customerOperate.getOperat_type());
				cardTradingList.setBill_money(customerOperate.getTrade_amount());//实收金额
				cardTradingList.setMain_trading(money); //实收
				cardTradingList.setReward_trading(gift);//赠送金额
				cardTradingList.setRevoked_trading(0d);
				cardTradingList.setMain_original(0d);
				cardTradingList.setReward_original(0d);
				cardTradingList.setTotal_balance(null);
				cardTradingList.setMain_balance(null);
				cardTradingList.setDeposit(customerOperate.getDeposit());
				cardTradingList.setActivity_id(null);
				cardTradingList.setCustomer_id(null);
				cardTradingList.setSalesman(customerOperate.getSales_person());
				cardTradingList.setOperate_time(currentTime);
				cardTradingList.setLast_updatetime(currentTime);
				cardTradingList.setStore_updatetime(currentTime);
				cardTradingList.setOperator_id(optNum);

				cardTradingList.setOperator(operator);
				cardTradingList.setInvoice_balance(invoiceAmount);
				cardTradingList.setIs_invoice(is_invoice);
				cardTradingList.setPayment_state(paymentState);
				cardTradingList.setRecharge_state(operateState);
				cardTradingList.setRequest_status(requestStatus);
				cardTradingList.setBill_code_original(null);
				cardTradingList.setBatch_num(null);
				cardTradingList.setCommission_saler_money(0d);
				cardTradingList.setCommission_store_money(0d);
				cardTradingList.setPay_type(customerOperate.getPayment_class());
				cardTradingList.setPosNum(posNum);
				customerDao.insertCrmCardTradingList(tenancyId, storeId, cardTradingList);


				//新增会员储值支付记录
				CrmCardPaymentListEntity cardPaymentList = new CrmCardPaymentListEntity();
				cardPaymentList.setBill_code(comitDealId);
				cardPaymentList.setThird_bill_code(thirdBillCode);
				cardPaymentList.setCard_id(null);
				cardPaymentList.setPayment_id(customerOperate.getPayment_id());
				cardPaymentList.setPay_money(trade_amount);
				cardPaymentList.setLocal_currency(trade_amount);
				cardPaymentList.setPay_no(null);
				cardPaymentList.setRexchange_rate(100d);
				cardPaymentList.setStore_updatetime(currentTime);
				cardPaymentList.setLast_updatetime(currentTime);
				customerDao.insertCrmCardPaymentList(tenancyId, storeId, cardPaymentList);


				//返回信息（打印小票）
				resultObj.put("third_bill_code",thirdBillCode);
				resultObj.put("bill_code",thirdBillCode);
				resultObj.put("card_code",customerOperate.getCard_code());
				resultObj.put("card_class_name", "");
				resultObj.put("name",customerOperate.getCustomer_name());
				resultObj.put("mobil",customerOperate.getMobil());
				resultObj.put("real_money",customerOperate.getTrade_amount());
				resultObj.put("income",customerOperate.getTrade_amount());
				resultObj.put("payment_id",payment_id);
				resultObj.put("charge_total",chargeTotal);//实收+赠送
				resultObj.put("gift",gift);//赠送金额
				resultObj.put("award_credit",awardCredit);//赠送金额
				resultObj.put("award_coupons",extendParamJson.get("award_coupons"));//赠送券
				resultObj.put("expired",extendParamJson.getString("expired"));//有效期
				resultObj.put("salesman_name",ParamUtil.getStringValueByObject(extendParamJson,"salesman_name"));//销售人员名称
				//会员打印使用
				resultObj.put("main_trading", money); //主账户交易金额
				resultObj.put("reward_trading", gift);//赠送交易金额
				resultObj.put("main_balance", 0d);
				resultObj.put("reward_balance", gift);// 赠送账户余额无法获取  模板修改 赠送金额
				resultObj.put("reward_credit", awardCredit); //赠送积分
				resultObj.put("useful_credit", awardCredit);//可用积分
				resultObj.put("level_name", ""); //等级
				resultObj.put("operator", operator);
				resultObj.put("channel", customerOperate.getChanel());
				resultObj.put("updatetime", DateUtil.format(currentTime));

			}
			else if (Constant.CODE_CONN_EXCEPTION == resData.getCode())
			{
				operateState = SysDictionary.CUSTOMER_OPERATE_STATE_PROCESS;
				requestStatus = SysDictionary.REQUEST_STATUS_FAILURE;
				requestCode = resData.getCode();
				requestMsg = resData.getMsg();
			}
			else
			{
				operateState = SysDictionary.CUSTOMER_OPERATE_STATE_FAIL;
				requestStatus = SysDictionary.REQUEST_STATUS_COMPLETE;
				requestCode = resData.getCode();
				requestMsg = resData.getMsg();
				finishTime = currentTime;
			}

			customerDao.updatePosCustomerOperateList(tenancyId, storeId, thirdBillCode, paymentState, operateState, requestStatus, requestCode, requestMsg, finishTime, SysDictionary.OPERAT_TYPE_CZ, comitDealId, (null != extendParamJson ? extendParamJson.toString() : null));


			//保存提交日志
			StringBuffer newstate = new  StringBuffer();
			newstate.append("实收金额:").append(extendParamJson.getString("money")).append(";");
			newstate.append("赠送金额:").append(extendParamJson.getString("gift")).append(";");
			newstate.append("充值金额:").append((extendParamJson.getString("charge_total"))).append(";");
			String  award_credit = extendParamJson.getString("award_credit");
			String  expired = extendParamJson.getString("expired");
			newstate.append("赠送积分:").append(award_credit).append(";");
			newstate.append("有效期:").append(expired).append(";");
			newstate.append("赠送券:").append(extendParamJson.getString("award_coupons")).append(";");
			customerDao.savePosLog(tenancyId, storeId, posNum, optNum.toString(), operator, customerOperate.getShift_id(),reportDate, Constant.TITLE, "微生活储值提交", "储值业务号:"+thirdBillCode, "储值业务号:"+thirdBillCode+" payment_state："+paymentState+" operate_state:"+operateState+" 储值信息："+newstate.toString());

			resultObj.put("operate_state", operateState);
			resultObj.put("payment_state", paymentState);
			resultObj.put("payment_id", customerOperate.getPayment_id());
			//resultObj.put("is_invoice", "0");
			resultObj.put("report_date", DateUtil.formatDate(reportDate));
			resultObj.put("pos_num", posNum);
			List<JSONObject> resultList = new ArrayList<JSONObject>();
			resultList.add(resultObj);
			resData.setData(resultList);
			resData.setType(Type.ACEWILL_CUSTOMER);
			resData.setOper(Oper.add);

			return resData;
		}
	}



	@Override
	public Data precreatePaymentForRecharge(String tenantId, Integer storeId,  JSONObject paramJson, String path) throws Exception {

		String thirdBillCode = paramJson.optString("third_bill_code");//储值订单流水号

		//获取会员操作记录
		PosCustomerOperateListEntity  customerOperate = customerDao.queryPosCustomerOperateListByBillcode(tenantId, storeId, null, null, null, null, null, null, thirdBillCode, null, SysDictionary.OPERAT_TYPE_CZ);
		Double settleAmount = customerOperate.getTrade_amount();//实收金额
		Date report_date  = customerOperate.getBusiness_date();
		Integer shift_id = customerOperate.getShift_id();
		String chanel = customerOperate.getChanel();
		String pos_num = customerOperate.getPos_num();
		Integer opt_num = customerOperate.getOperator_id();
		Integer payment_id = customerOperate.getPayment_id();


		JSONObject thirdParamJson = new JSONObject();
		thirdParamJson.put("order_no", thirdBillCode);
		thirdParamJson.put("order_num", thirdBillCode);
		thirdParamJson.put("bill_num", thirdBillCode);
		thirdParamJson.put("service_type", SysDictionary.SERVICE_TYPE_RECHARGE);
		thirdParamJson.put("report_date", DateUtil.formatDate(report_date));
		thirdParamJson.put("shift_id", shift_id.toString());
		thirdParamJson.put("channel", chanel);
		thirdParamJson.put("pos_num", pos_num);
		thirdParamJson.put("opt_num", opt_num);
		thirdParamJson.put("settle_amount", settleAmount);
		thirdParamJson.put("total_amount", settleAmount);
		thirdParamJson.put("payment_id", payment_id.toString());
		thirdParamJson.put("subject", "会员卡充值");
		thirdParamJson.put("currency_name", ParamUtil.getStringValueByObject(paramJson, "currency_name"));
		thirdParamJson.put("body", ParamUtil.getStringValueByObject(paramJson, "body"));
		thirdParamJson.put("client_ip", ParamUtil.getStringValueByObject(paramJson, "client_ip"));
		thirdParamJson.put("description", ParamUtil.getStringValueByObject(paramJson, "description"));
		thirdParamJson.put("extra", ParamUtil.getStringValueByObject(paramJson, "extra"));
		thirdParamJson.put("oper_type", Type.CUSTOMER_CARD_RECHARGE.name());

		Data resultData = thirdPaymentService.precreatePayment(tenantId, storeId, thirdParamJson, path);

		JSONObject resultJson = null;
		String paymentState = null;
		String operateState = null;
		String requestStatus = null;
		Integer requestCode = null;
		String requestMsg = null;
		Timestamp finishTime = null;
		if (Constant.CODE_SUCCESS == resultData.getCode())
		{
			// 获取二维码成功
			paymentState = SysDictionary.THIRD_PAY_STATUS_PAYING;
			operateState = SysDictionary.CUSTOMER_OPERATE_STATE_WAIT;
			requestStatus = SysDictionary.REQUEST_STATUS_COMPLETE;

			resultJson = JSONObject.fromObject(resultData.getData().get(0));
			resultJson.put("third_bill_code", thirdBillCode);
			resultJson.put("payment_state", paymentState);
			resultJson.put("operate_state", operateState);
		}
		else if (Constant.CODE_CONN_EXCEPTION == resultData.getCode())
		{
			// 网络异常,获取二维码网络异常后,已自动取消交易
			paymentState = SysDictionary.THIRD_PAY_STATUS_NOOK;
			operateState = SysDictionary.CUSTOMER_OPERATE_STATE_FAIL;
			requestStatus = SysDictionary.REQUEST_STATUS_FAILURE;
			requestCode = resultData.getCode();
			requestMsg = resultData.getMsg();
			if(JSONObject.fromObject(resultData.getData().get(0)).containsKey("failure_code")){
				requestMsg = JSONObject.fromObject(resultData.getData().get(0)).getString("failure_msg");
			}

			finishTime = DateUtil.currentTimestamp();

			resultJson = new JSONObject();
			resultJson.put("third_bill_code", thirdBillCode);
			resultJson.put("payment_state", paymentState);
			resultJson.put("operate_state", operateState);
		}
		else
		{
			// 获取二维码失败
			paymentState = SysDictionary.THIRD_PAY_STATUS_FAIL;
			operateState = SysDictionary.CUSTOMER_OPERATE_STATE_FAIL;
			requestStatus = SysDictionary.REQUEST_STATUS_COMPLETE;
			requestCode = resultData.getCode();
			requestMsg = resultData.getMsg();
			finishTime = DateUtil.currentTimestamp();

			resultJson = new JSONObject();
			resultJson.put("third_bill_code", thirdBillCode);
			resultJson.put("payment_state", paymentState);
			resultJson.put("operate_state", operateState);
		}

		// 修改会员操作状态
		customerDao.updatePosCustomerOperateList(tenantId, storeId, thirdBillCode, paymentState, operateState, requestStatus, requestCode, requestMsg, finishTime, SysDictionary.OPERAT_TYPE_CZ);

		List<JSONObject> resultList = new ArrayList<JSONObject>();
		resultList.add(resultJson);

		resultData.setType(Type.ACEWILL_CUSTOMER);
		resultData.setOper(Oper.precreate);
		resultData.setData(resultList);

		String optName = customerDao.getEmpNameById(opt_num.toString(), tenantId, storeId);
		JSONObject extendParamJson = JSONObject.fromObject(customerOperate.getExtend_param());
		StringBuffer newstate = new  StringBuffer();
		newstate.append("实收金额:").append(extendParamJson.getString("money")).append(";");
		newstate.append("赠送金额:").append(extendParamJson.getString("gift")).append(";");
		newstate.append("充值金额:").append((extendParamJson.getString("charge_total"))).append(";");
		String  award_credit = extendParamJson.getString("award_credit");
		String  expired = extendParamJson.getString("expired");
		newstate.append("赠送积分:").append(award_credit).append(";");
		newstate.append("有效期:").append(expired).append(";");
		newstate.append("赠送券:").append(extendParamJson.getString("award_coupons")).append(";");
		customerDao.savePosLog(tenantId, storeId, pos_num, opt_num.toString(), optName, customerOperate.getShift_id(),report_date, Constant.TITLE, "微生活储值获取二维码", "储值业务号:"+thirdBillCode, "储值业务号:"+thirdBillCode+" payment_state："+paymentState+" operate_state:"+operateState+" 储值信息："+newstate.toString());
		return resultData;
	}

	@Override
	public Data barcodePaymentForRecharge(String tenantId, Integer storeId, JSONObject paramJson) throws Exception{

		String thirdBillCode = paramJson.optString("bill_code");//储值订单流水号

		//查询会员操作记录
		PosCustomerOperateListEntity  customerOperate = customerDao.queryPosCustomerOperateListByBillcode(tenantId, storeId, null, null, null, null, null, null, thirdBillCode, null, SysDictionary.OPERAT_TYPE_CZ);

		Double settleAmount = customerOperate.getTrade_amount();//实收金额
		Date report_date  = customerOperate.getBusiness_date();
		Integer shift_id = customerOperate.getShift_id();
		String chanel = customerOperate.getChanel();
		String pos_num = customerOperate.getPos_num();
		Integer opt_num = customerOperate.getOperator_id();
		Integer payment_id = customerOperate.getPayment_id();

		JSONObject thirdParamJson = new JSONObject();
		thirdParamJson.put("order_no", thirdBillCode);
		thirdParamJson.put("order_num", thirdBillCode);
		thirdParamJson.put("bill_num", thirdBillCode);
		thirdParamJson.put("service_type", SysDictionary.SERVICE_TYPE_RECHARGE);
		thirdParamJson.put("report_date", DateUtil.formatDate(report_date));
		thirdParamJson.put("shift_id", shift_id);
		thirdParamJson.put("pos_num", pos_num.toString());
		thirdParamJson.put("opt_num", opt_num);
		thirdParamJson.put("channel", chanel);
		thirdParamJson.put("payment_id", payment_id);
		thirdParamJson.put("total_amount", settleAmount);
		thirdParamJson.put("settle_amount", settleAmount);
		thirdParamJson.put("currency_name", ParamUtil.getStringValueByObject(paramJson, "currency_name"));
		thirdParamJson.put("client_ip", ParamUtil.getStringValueByObject(paramJson, "client_ip"));
		thirdParamJson.put("body", ParamUtil.getStringValueByObject(paramJson, "body"));
		thirdParamJson.put("description", ParamUtil.getStringValueByObject(paramJson, "description"));
		thirdParamJson.put("extra", ParamUtil.getStringValueByObject(paramJson, "extra"));
		thirdParamJson.put("credential", ParamUtil.getStringValueByObject(paramJson, "credential"));
		thirdParamJson.put("oper_type", Type.CUSTOMER_CARD_RECHARGE.name());
		thirdParamJson.put("subject", "会员卡充值");
		logger.info("扫码支付参数  "+thirdParamJson);

		Data resultData = thirdPaymentService.barcodePayment(tenantId, storeId, thirdParamJson);

		JSONObject resultJson = null;
		String paymentState = null;
		String operateState = null;
		String requestStatus = null;
		Integer requestCode = null;
		String requestMsg = null;
		Timestamp finishTime = null;
		if (Constant.CODE_SUCCESS == resultData.getCode())
		{
			// 支付成功
			resultJson = JSONObject.fromObject(resultData.getData().get(0));

			if (SysDictionary.THIRD_PAY_STATUS_SUCCESS.equals(resultJson.optString("payment_state")))
			{
				paymentState = SysDictionary.THIRD_PAY_STATUS_SUCCESS;
				operateState = SysDictionary.CUSTOMER_OPERATE_STATE_PROCESS;
				requestStatus = SysDictionary.REQUEST_STATUS_COMPLETE;
			}
			else if(SysDictionary.THIRD_PAY_STATUS_FAIL.equals(resultJson.optString("payment_state")))
			{
				paymentState = SysDictionary.THIRD_PAY_STATUS_FAIL;
				operateState = SysDictionary.CUSTOMER_OPERATE_STATE_FAIL;
				requestStatus = SysDictionary.REQUEST_STATUS_COMPLETE;
				requestMsg = resultJson.optString("failure_msg");
				requestCode = PosErrorCode.THIRD_PAYMENT_FAILURE.getNumber();

				resultData.setCode(requestCode);
				resultData.setMsg(requestMsg);
			}
			else
			{
				paymentState = SysDictionary.THIRD_PAY_STATUS_PAYING;
				operateState = SysDictionary.CUSTOMER_OPERATE_STATE_WAIT;
				requestStatus = SysDictionary.REQUEST_STATUS_COMPLETE;
				requestMsg = resultJson.optString("failure_msg");
			}

			resultJson.put("bill_code", thirdBillCode);
			resultJson.put("third_bill_code", thirdBillCode);
			resultJson.put("payment_state", paymentState);
			resultJson.put("operate_state", operateState);
		}
		else if (Constant.CODE_CONN_EXCEPTION == resultData.getCode())
		{
			// 网络异常,支付网络异常后,已自动取消交易
			paymentState = SysDictionary.THIRD_PAY_STATUS_NOOK;
			operateState = SysDictionary.CUSTOMER_OPERATE_STATE_WAIT;
			requestStatus = SysDictionary.REQUEST_STATUS_FAILURE;
			requestCode = resultData.getCode();
			requestMsg = resultData.getMsg();

			resultJson = new JSONObject();
			resultJson.put("bill_code", thirdBillCode);
			resultJson.put("third_bill_code", thirdBillCode);
			resultJson.put("payment_state", paymentState);
			resultJson.put("operate_state", operateState);
		}
		else
		{
			// 支付失败
			paymentState = SysDictionary.THIRD_PAY_STATUS_FAIL;
			operateState = SysDictionary.CUSTOMER_OPERATE_STATE_FAIL;
			requestStatus = SysDictionary.REQUEST_STATUS_COMPLETE;
			requestCode = resultData.getCode();
			requestMsg = resultData.getMsg();
			finishTime = DateUtil.currentTimestamp();

			resultJson = new JSONObject();
			resultJson.put("bill_code", thirdBillCode);
			resultJson.put("third_bill_code", thirdBillCode);
			resultJson.put("payment_state", paymentState);
			resultJson.put("operate_state", operateState);
		}

		// 修改会员操作状态
		customerDao.updatePosCustomerOperateList(tenantId, storeId, thirdBillCode, paymentState, operateState, requestStatus, requestCode, requestMsg, finishTime, SysDictionary.OPERAT_TYPE_CZ);

		//保存日志
		String optName = customerDao.getEmpNameById(opt_num.toString(), tenantId, storeId);
		JSONObject extendParamJson = JSONObject.fromObject(customerOperate.getExtend_param());
		StringBuffer newstate = new  StringBuffer();
		newstate.append("实收金额:").append(extendParamJson.getString("money")).append(";");
		newstate.append("赠送金额:").append(extendParamJson.getString("gift")).append(";");
		newstate.append("充值金额:").append((extendParamJson.getString("charge_total"))).append(";");
		String  award_credit = extendParamJson.getString("award_credit");
		String  expired = extendParamJson.getString("expired");
		newstate.append("赠送积分:").append(award_credit).append(";");
		newstate.append("有效期:").append(expired).append(";");
		newstate.append("赠送券:").append(extendParamJson.getString("award_coupons")).append(";");
		customerDao.savePosLog(tenantId, storeId, pos_num, opt_num.toString(), optName, customerOperate.getShift_id(),report_date, Constant.TITLE, "微生活储值提扫码支付", "储值业务号:"+thirdBillCode, "储值业务号:"+thirdBillCode+" payment_state："+paymentState+" operate_state:"+operateState+" 储值信息："+newstate.toString());

		List<JSONObject> resultList = new ArrayList<JSONObject>();
		resultList.add(resultJson);
		resultData.setType(Type.ACEWILL_CUSTOMER);
		resultData.setOper(Oper.barcode);
		resultData.setData(resultList);
		return resultData;
	}

	@Override
	public JSONObject queryCustomerCardRechargeForDetails(String tenantId, Integer storeId, PosCustomerOperateListEntity cardRecharge) throws Exception {
		String billCode = cardRecharge.getThird_bill_code();

		StringBuffer detailsSql = new StringBuffer();
		detailsSql.append(" select pcol.card_code, pcol.bill_code, coalesce(pcol.customer_name,cctl.name) as name, coalesce(pcol.mobil,cctl.mobil) as mobil, " +
				" pcol.operate_state, cctl.chanel, ccpl.payment_id,ccpl.last_updatetime as updatetime,pcol.operator_id,cctl.operator,cctl.shift_id,ccpl.pay_no,pcol.extend_param ");
		detailsSql.append(" from pos_customer_operate_list pcol left join crm_card_trading_list cctl on pcol.third_bill_code = cctl.third_bill_code and pcol.tenancy_id = cctl.tenancy_id and pcol.store_id = cctl.store_id ");
		detailsSql.append(" left join crm_card_payment_list ccpl on pcol.bill_code = ccpl.bill_code and pcol.tenancy_id = ccpl.tenancy_id ");
		detailsSql.append(" where pcol.third_bill_code = ? and pcol.operate_state = '" + SysDictionary.CUSTOMER_OPERATE_STATE_SUCCESS + "' and pcol.tenancy_id = ? and pcol.store_id = ? ");

		List<JSONObject> resultList = customerDao.query4Json(tenantId, detailsSql.toString(), new Object[]{ billCode, tenantId, storeId });


		JSONObject resultJson = new JSONObject();
		if (resultList.size() > 0)
		{
			resultJson = resultList.get(0);
			JSONObject extendParamJson = ParamUtil.getJSONObject(resultJson, "extend_param");
			resultJson.remove("extend_param");

			resultJson.put("charge_total", ParamUtil.getDoubleValueByObject(extendParamJson, "charge_total"));
			resultJson.put("money", ParamUtil.getDoubleValueByObject(extendParamJson, "money"));
			resultJson.put("gift", extendParamJson.optString("gift"));
			resultJson.put("award_credit", extendParamJson.optString("award_credit"));
			resultJson.put("deal_id", extendParamJson.optString("deal_id"));
			resultJson.put("award_coupons", extendParamJson.get("award_coupons"));
			resultJson.put("expired", extendParamJson.optString("expired"));
		}
		return resultJson;
	}
	
	@Override
	public PosCustomerOperateListEntity queryCustomerCardRecharge(String tenantId, Integer storeId, JSONObject paramJson) throws Exception
	{
		return customerDao.queryPosCustomerOperateListByBillcode(tenantId, storeId, paramJson);
	}
	
	@Override
	public Data findCustomerCardChargeDetails(String tenantId, Integer storeId,PosCustomerOperateListEntity customer,JSONObject printJson) throws Exception
	{
		// 调用微生活撤销接口
		JSONObject jsonParam = new JSONObject();
		jsonParam.put("biz_id", customer.getThird_bill_code());

		// 请求acewill接口
		List<JSONObject> data = new ArrayList<JSONObject>();
		data.add(jsonParam);
		Data parData = Data.get(tenantId, storeId, Constant.CODE_SUCCESS);
		parData.setData(data);

		Data responseData = this.commonPost(parData, RequestUrlConstant.QUERY_ACEWILL_CHARGE_URL);
		if (Constant.CODE_SUCCESS == responseData.getCode())
		{
			JSONObject resultJson = null;
			if (null != responseData.getData() && 0 < responseData.getData().size())
			{
				resultJson = JSONObject.fromObject(responseData.getData().get(0));
			}
			Integer status = ParamUtil.getIntegerValueByObject(resultJson, "status");
			
			String paymentState = SysDictionary.THIRD_PAY_STATUS_SUCCESS;
            String operateState = SysDictionary.REQUEST_STATUS_ING;
            String requestStatus = SysDictionary.REQUEST_STATUS_ING;

			JSONObject extendParamJson = null;
			if (CommonUtil.hv(customer.getExtend_param()))
			{
				extendParamJson = JSONObject.fromObject(customer.getExtend_param());
			}
            
			if(1==status.intValue())
			{
                operateState = SysDictionary.CUSTOMER_OPERATE_STATE_SUCCESS;
                requestStatus = SysDictionary.REQUEST_STATUS_COMPLETE;

				String comitDealId = ParamUtil.getStringValueByObject(resultJson, "charge_id");
				Double chargeTotal = ParamUtil.getDoubleValueByObject(resultJson, "total_fee");
				Double money = ParamUtil.getDoubleValueByObject(resultJson, "fee");
				Double gift = ParamUtil.getDoubleValueByObject(resultJson, "award_fee");
				Double awardCredit = ParamUtil.getDoubleValueByObject(resultJson, "award_credit");
				JSONArray awardCoupons = ParamUtil.getJSONArray(resultJson, "award_coupons");
				
                Timestamp currentTime = DateUtil.currentTimestamp();
                
                Double invoiceAmount = 0d;
                String paymentName = "";
                if ("1".equals(customer.getIs_invoice())) //是否开发票
                {
                    JSONObject paymentWayJson = customerDao.getPaymentWayByID(tenantId, storeId, customer.getPayment_id());

                    if ("1".equals(paymentWayJson.optString("if_invoicing")))
                    {
                        invoiceAmount = customer.getTrade_amount();//实收金额
                    }
                    paymentName = paymentWayJson.optString("payment_name");
                }
                String operator = customerDao.getEmpNameById(String.valueOf(customer.getOperator_id()), tenantId, storeId);
                
                extendParamJson.put("charge_total", chargeTotal);
                extendParamJson.put("money", money);
                extendParamJson.put("gift", gift);
                extendParamJson.put("award_credit", awardCredit);
                extendParamJson.put("award_coupons", awardCoupons);
                
                //新增会员卡交易记录
                CrmCardTradingListEntity cardTradingList = new CrmCardTradingListEntity();
                cardTradingList.setThird_bill_code(customer.getThird_bill_code()); //微生活 储值交易id
                cardTradingList.setBill_code(comitDealId);  //储值业务号，收银方保证唯一，提交储值需要biz_id
                cardTradingList.setCard_id(null);
                cardTradingList.setCard_code(customer.getCard_code());
                cardTradingList.setCard_class_id(null);
                cardTradingList.setName(customer.getCustomer_name());
                cardTradingList.setMobil(customer.getMobil());
                cardTradingList.setBusiness_date(customer.getBusiness_date());
                cardTradingList.setShift_id(customer.getShift_id());
                cardTradingList.setChanel(customer.getChanel());
                cardTradingList.setOperat_type(customer.getOperat_type());
                cardTradingList.setBill_money(customer.getTrade_amount());//实收金额
                cardTradingList.setMain_trading(money); //实收
                cardTradingList.setReward_trading(gift);//赠送金额
                cardTradingList.setRevoked_trading(0d);
                cardTradingList.setMain_original(0d);
                cardTradingList.setReward_original(0d);
                cardTradingList.setTotal_balance(null);
                cardTradingList.setMain_balance(null);
                cardTradingList.setDeposit(customer.getDeposit());
                cardTradingList.setActivity_id(null);
                cardTradingList.setCustomer_id(null);
                cardTradingList.setSalesman(customer.getSales_person());
                cardTradingList.setOperate_time(currentTime);
                cardTradingList.setLast_updatetime(currentTime);
                cardTradingList.setStore_updatetime(currentTime);
                cardTradingList.setOperator_id(customer.getOperator_id());
                cardTradingList.setOperator(operator);
                cardTradingList.setInvoice_balance(invoiceAmount);
                cardTradingList.setIs_invoice(customer.getIs_invoice());
                cardTradingList.setPayment_state(paymentState);
                cardTradingList.setRecharge_state(operateState);
                cardTradingList.setRequest_status(requestStatus);
                cardTradingList.setBill_code_original(null);
                cardTradingList.setBatch_num(null);
                cardTradingList.setCommission_saler_money(0d);
                cardTradingList.setCommission_store_money(0d);
                cardTradingList.setPay_type(customer.getPayment_class());
                cardTradingList.setPosNum(customer.getPos_num());
                customerDao.insertCrmCardTradingList(tenantId, storeId, cardTradingList);

                //新增会员储值支付记录
                CrmCardPaymentListEntity cardPaymentList = new CrmCardPaymentListEntity();
                cardPaymentList.setBill_code(comitDealId);
                cardPaymentList.setThird_bill_code(customer.getThird_bill_code());
                cardPaymentList.setCard_id(null);
                cardPaymentList.setPayment_id(customer.getPayment_id());
                cardPaymentList.setPay_money(customer.getTrade_amount());
                cardPaymentList.setLocal_currency(customer.getTrade_amount());
                cardPaymentList.setPay_no(null);
                cardPaymentList.setRexchange_rate(100d);
                cardPaymentList.setStore_updatetime(currentTime);
                cardPaymentList.setLast_updatetime(currentTime);
                customerDao.insertCrmCardPaymentList(tenantId, storeId, cardPaymentList);

                //返回信息（打印小票）
                printJson.put("third_bill_code",customer.getThird_bill_code());
                printJson.put("bill_code",comitDealId);
                printJson.put("card_code",customer.getCard_code());
                printJson.put("card_class_name", "");
                printJson.put("name",customer.getCustomer_name());
                printJson.put("mobil",customer.getMobil());
                printJson.put("real_money",customer.getTrade_amount());
                printJson.put("income",customer.getTrade_amount());
                printJson.put("payment_id",customer.getPayment_id());
                printJson.put("paymenttypename",paymentName);
                printJson.put("charge_total",chargeTotal);//实收+赠送
                printJson.put("gift",gift);//赠送金额
                printJson.put("award_credit",awardCredit);//赠送金额
                printJson.put("award_coupons",awardCoupons);//赠送券
                printJson.put("expired",extendParamJson.getString("expired"));//有效期

                //会员打印使用
                printJson.put("main_trading", money); //主账户交易金额
                printJson.put("reward_trading", gift);//赠送交易金额
                printJson.put("main_balance", 0d);
                printJson.put("reward_balance", gift);// 赠送账户余额无法获取  模板修改 赠送金额
                printJson.put("reward_credit", awardCredit); //赠送积分
                printJson.put("useful_credit", awardCredit);//可用积分
                printJson.put("level_name", ""); //等级
                printJson.put("operator", operator);
                printJson.put("channel", customer.getChanel());
                printJson.put("updatetime", DateUtil.format(currentTime));
            
				customerDao.updatePosCustomerOperateList(tenantId, storeId, customer.getThird_bill_code(), paymentState, operateState, requestStatus, (null != customer.getRequest_code() ? Integer.parseInt(customer.getRequest_code()) : null), customer.getRequest_msg(), currentTime,
						SysDictionary.OPERAT_TYPE_CZ, comitDealId, (null != extendParamJson ? extendParamJson.toString() : null));
			}
		}
		return responseData;
	}
	
	@Override
	public Data cancelThirdPaymentForCardRecharge(String tenantId, Integer storeId,String thirdBillCode, PosCustomerOperateListEntity cardRecharge) throws Exception
	{
		if ((SysDictionary.PAYMENT_CLASS_WECHAT_PAY.equals(cardRecharge.getPayment_class()) || SysDictionary.PAYMENT_CLASS_ALI_PAY.equals(cardRecharge.getPayment_class()))
				&& (SysDictionary.THIRD_PAY_STATUS_SUCCESS.equals(cardRecharge.getPayment_state()) || SysDictionary.THIRD_PAY_STATUS_PAYING.equals(cardRecharge.getPayment_state()) || SysDictionary.THIRD_PAY_STATUS_NOOK.equals(cardRecharge.getPayment_state())))
		{
			JSONObject paramJson = new JSONObject();
			paramJson.put("order_no", thirdBillCode);
			paramJson.put("order_num", thirdBillCode);
			paramJson.put("settle_amount", cardRecharge.getTrade_amount());
			paramJson.put("total_amount", cardRecharge.getTrade_amount());
			paramJson.put("payment_id", cardRecharge.getPayment_id());
			paramJson.put("service_type", SysDictionary.SERVICE_TYPE_RECHARGE);
			paramJson.put("channel", cardRecharge.getChanel());
			paramJson.put("report_date", DateUtil.formatDate(cardRecharge.getBusiness_date()));
			paramJson.put("shift_id", cardRecharge.getShift_id());
			paramJson.put("pos_num", cardRecharge.getPos_num());
			paramJson.put("opt_num", cardRecharge.getOperator_id());
			paramJson.put("client_ip", "");
			paramJson.put("currency_name", "");
			paramJson.put("oper_type", Type.CUSTOMER_CARD_RECHARGE.name());
			paramJson.put("refunds_order", "");

			Data resultData = thirdPaymentService.cancelPayment(tenantId, storeId, paramJson);
			return resultData;
		}
		else
		{
			return Data.get(tenantId, storeId, Constant.CODE_SUCCESS);
		}
	}

	@Override
	public Data revokeAcewillCharge(String tenantId, Integer storeId, String thirdBillCode, Data paramData) throws Exception {
		JSONObject paramJson = JSONObject.fromObject(paramData.getData().get(0));
		String cardCode = ParamUtil.getStringValueByObject(paramJson, "card_code", false, null);
		String billCodeOriginal = ParamUtil.getStringValueByObject(paramJson, "biz_id", false, null);
		Integer operatorId = ParamUtil.getIntegerValueByObject(paramJson, "opt_num", false, null);
		String operator = customerDao.getEmpNameById(String.valueOf(operatorId), tenantId, storeId);
		Timestamp currentTime = DateUtil.currentTimestamp();

		Date reportDate = ParamUtil.getDateValueByObject(paramJson, "report_date");
		Integer shiftId = ParamUtil.getIntegerValueByObject(paramJson, "shift_id");
		String posNum = ParamUtil.getStringValueByObject(paramJson, "pos_num");
		String chanel = ParamUtil.getStringValueByObject(paramJson, "chanel");
		String name = ParamUtil.getStringValueByObject(paramJson, "name");
		String mobil = ParamUtil.getStringValueByObject(paramJson, "mobil");
		Double money = ParamUtil.getDoubleValueByObject(paramJson, "money");
		Double gift = ParamUtil.getDoubleValueByObject(paramJson, "gift");
		Double awardCredit = ParamUtil.getDoubleValueByObject(paramJson, "award_credit");
		Double chargeTotal = ParamUtil.getDoubleValueByObject(paramJson, "charge_total");
		String salesman = ParamUtil.getStringValueByObject(paramJson, "salesman");
		String chargeType = ParamUtil.getStringValueByObject(paramJson, "charge_type");
		Integer paymentId = ParamUtil.getIntegerValueByObject(paramJson, "payment_id");
		
		// 调用微生活撤销接口
		JSONObject jsonParam = new JSONObject();
		jsonParam.put("biz_id",billCodeOriginal);
		jsonParam.put("cashier_id",-1);
		// 请求acewill接口
		List<JSONObject> data = new ArrayList<JSONObject>();
		data.add(jsonParam);
		Data parData = Data.get(tenantId, storeId, Constant.CODE_SUCCESS);
		parData.setData(data);
		parData.setType(paramData.getType());
		parData.setT(paramData.getT());
		parData.setOper(paramData.getOper());

		Data responseData = this.commonPost(parData, RequestUrlConstant.CANCEL_ACEWILL_CHARGE_URL);

		String paymentState = SysDictionary.THIRD_PAY_STATUS_SUCCESS;
		String operateState = SysDictionary.REQUEST_STATUS_ING;
		String requestStatus = SysDictionary.REQUEST_STATUS_ING;
		Integer requestCode = null;
		String requestMsg = null;
		Timestamp finishTime = null;
		String paymentClass = null;
		String paymentName = null;

		JSONObject resultObj = null;
		if (responseData.isSuccess())
		{
			operateState = SysDictionary.CUSTOMER_OPERATE_STATE_SUCCESS;
			requestStatus = SysDictionary.REQUEST_STATUS_COMPLETE;
			finishTime = currentTime;

			resultObj = JSONObject.fromObject(responseData.getData().get(0));
			String billCode = ParamUtil.getStringValueByObject(resultObj, "deal_id");

			List<JSONObject> tradingList = customerDao.getCrmCardTradingListByCardcode(tenantId, storeId, cardCode, billCodeOriginal, SysDictionary.OPERAT_TYPE_CZ);
			
//			paymentId = ParamUtil.getIntegerValueByObject(paramJson, "charge_type");
			// 微生活支付方式 转换成 tzx支付方式
			paymentClass = PaymentWayEnum.findValue(chargeType);
			JSONObject paymentWayJson = null;
			if (null != paymentId && paymentId > 0)
			{
				if (null != tradingList && 0 < tradingList.size())
				{
					// 查询本地充值记录,优先获取本地记录付款方式
					paymentId = ParamUtil.getIntegerValueByObject(tradingList.get(0), "payment_id");
					paymentWayJson = customerDao.getPaymentWayByID(tenantId, storeId, paymentId);
				}
				
				if (null == paymentWayJson || paymentWayJson.isEmpty())
				{
					if (SysDictionary.PAYMENT_CLASS_CASH.equals(paymentClass))
					{
						paymentWayJson = customerDao.getPaymentWayForStandard(tenantId, storeId);
					}
					else
					{
						paymentWayJson = customerDao.getPaymentWayByPaymentClass(tenantId, storeId, paymentClass);
					}
				}

				if (null != paymentWayJson && !paymentWayJson.isEmpty())
				{
					paymentId = ParamUtil.getIntegerValueByObject(paymentWayJson, "payment_id");
					paymentClass = ParamUtil.getStringValueByObject(paymentWayJson, "payment_class");
					paymentName = ParamUtil.getStringValueByObject(paymentWayJson, "payment_name");
				}
			}
			else if(null != paymentId)
			{
				paymentWayJson = customerDao.getPaymentWayByID(tenantId, storeId, paymentId);
				if (null != paymentWayJson && !paymentWayJson.isEmpty())
				{
					paymentId = ParamUtil.getIntegerValueByObject(paymentWayJson, "payment_id");
					paymentClass = ParamUtil.getStringValueByObject(paymentWayJson, "payment_class");
					paymentName = ParamUtil.getStringValueByObject(paymentWayJson, "payment_name");
				}
			}

			if (null != tradingList && 0 < tradingList.size())
			{
				CrmCardTradingListEntity cardTradingList = new CrmCardTradingListEntity();
				cardTradingList.setThird_bill_code(thirdBillCode);
				cardTradingList.setBill_code(billCode);
				//cardTradingList.setCard_id(cardId);
				cardTradingList.setCard_code(cardCode);
				//cardTradingList.setCard_class_id(ParamUtil.getIntegerValueByObject(resultObj, "card_class_id"));
				cardTradingList.setName(name);
	
				cardTradingList.setMobil(mobil);
				cardTradingList.setBusiness_date(reportDate);
				cardTradingList.setShift_id(shiftId);
	
				cardTradingList.setChanel(chanel);
				cardTradingList.setOperat_type(SysDictionary.OPERAT_TYPE_FCZ);
	
	
				cardTradingList.setBill_money(-money);
				cardTradingList.setMain_trading(-money);
				cardTradingList.setReward_trading(-gift);
	
				//cardTradingList.setRevoked_trading(ParamUtil.getDoubleValueByObject(paramJson, "revoked_trading"));
				//cardTradingList.setMain_original(ParamUtil.getDoubleValueByObject(paramJson, "main_original"));
				//cardTradingList.setReward_original(ParamUtil.getDoubleValueByObject(paramJson, "reward_original"));
	
				//cardTradingList.setTotal_balance(ParamUtil.getDoubleValueByObject(paramJson, "total_balance"));
				//cardTradingList.setReward_balance(ParamUtil.getDoubleValueByObject(paramJson, "reward_balance"));
				//cardTradingList.setMain_balance(ParamUtil.getDoubleValueByObject(paramJson, "main_balance"));
	
				cardTradingList.setDeposit(0d);
				//cardTradingList.setActivity_id(ParamUtil.getIntegerValueByObject(paramJson, "activity_id"));
				//cardTradingList.setCustomer_id(ParamUtil.getIntegerValueByObject(paramJson, "customer_id"));
				cardTradingList.setSalesman(salesman);
				cardTradingList.setOperate_time(currentTime);
				cardTradingList.setBill_code_original(billCodeOriginal);
				cardTradingList.setLast_updatetime(currentTime);
				cardTradingList.setStore_updatetime(currentTime);
				cardTradingList.setOperator_id(operatorId);
				cardTradingList.setOperator(operator);
				cardTradingList.setInvoice_balance(0d);
				cardTradingList.setIs_invoice("0");
				cardTradingList.setPayment_state(paymentState);
				cardTradingList.setRecharge_state(operateState);
				cardTradingList.setRequest_status(requestStatus);
				cardTradingList.setBatch_num(null);
				cardTradingList.setCommission_saler_money(0d);
				cardTradingList.setCommission_store_money(0d);
				cardTradingList.setPay_type(paymentClass);
				cardTradingList.setPosNum(posNum);
	
				customerDao.insertCrmCardTradingList(tenantId, storeId, cardTradingList);
	
	
				CrmCardPaymentListEntity cardPaymentList = new CrmCardPaymentListEntity();
				cardPaymentList.setBill_code(billCode);
				cardPaymentList.setThird_bill_code(thirdBillCode);
				//cardPaymentList.setCard_id(cardId);
				cardPaymentList.setPayment_id(paymentId);
				cardPaymentList.setPay_money(-money);
				cardPaymentList.setLocal_currency(-money);
				//cardPaymentList.setPay_no(ParamUtil.getStringValueByObject(resultObj, "pay_no"));
				cardPaymentList.setRexchange_rate(100d);
				cardPaymentList.setStore_updatetime(currentTime);
				cardPaymentList.setLast_updatetime(currentTime);
				cardPaymentList.setLast_updatetime(currentTime);
	
				customerDao.insertCrmCardPaymentList(tenantId, storeId, cardPaymentList);
			}
			
			if (null == resultObj)
			{
				resultObj = new JSONObject();
			}
			resultObj.put("payment_id", paymentId);
			resultObj.put("paymenttypename", paymentName);
			resultObj.put("deal_id",billCode);
			resultObj.put("pos_num",posNum);
			resultObj.put("billCode",thirdBillCode);
			resultObj.put("bill_code",thirdBillCode);
			resultObj.put("card_code",cardCode);
			resultObj.put("name",name);
			resultObj.put("mobil",mobil);
			resultObj.put("report_date",DateUtil.formatDate(reportDate));
			resultObj.put("shift_id",shiftId);
			resultObj.put("money",money);
			resultObj.put("charge_total",chargeTotal);
			resultObj.put("income", money);
			resultObj.put("bill_money", money);
			resultObj.put("pay_money", money);


			//会员反充值打印
			resultObj.put("level_name", ParamUtil.getStringValueByObject(paramJson, "level_name"));
			resultObj.put("card_class_name", ParamUtil.getStringValueByObject(paramJson, "card_class_name"));
			resultObj.put("main_trading", money);
			resultObj.put("reward_trading", gift);

			resultObj.put("main_balance", ParamUtil.getDoubleValueByObject(paramJson, "main_balance"));
			resultObj.put("reward_balance", ParamUtil.getDoubleValueByObject(paramJson, "reward_balance"));
			resultObj.put("reward_credit",awardCredit);
			resultObj.put("useful_credit", ParamUtil.getDoubleValueByObject(paramJson, "useful_credit"));
			resultObj.put("total_main", 0d);
			resultObj.put("total_reward", 0d);
			resultObj.put("credit", 0d);
			resultObj.put("operator", operator);
			resultObj.put("channel", chanel);
			resultObj.put("updatetime", DateUtil.format(currentTime));
			resultObj.put("reward_coupon", ParamUtil.getStringValueByObject(paramJson, "award_coupon"));
			resultObj.put("url_content", "");
			resultObj.put("url_path", "");
			resultObj.put("is_invoice", "");
			resultObj.put("invoice_amount", "");

		}
		else if (Constant.CODE_CONN_EXCEPTION == responseData.getCode())
		{
			operateState = SysDictionary.CUSTOMER_OPERATE_STATE_PROCESS;
			requestStatus = SysDictionary.REQUEST_STATUS_FAILURE;
			requestCode = responseData.getCode();
			requestMsg = responseData.getMsg();
		}
		else
		{
			operateState = SysDictionary.CUSTOMER_OPERATE_STATE_FAIL;
			requestStatus = SysDictionary.REQUEST_STATUS_COMPLETE;
			requestCode = responseData.getCode();
			requestMsg = responseData.getMsg();
			finishTime = currentTime;
		}

		customerDao.updatePosCustomerOperateList(tenantId, storeId, thirdBillCode, paymentState, operateState, requestStatus, requestCode, requestMsg, finishTime, SysDictionary.OPERAT_TYPE_FCZ,paymentId,paymentClass);

		resultObj.put("operate_state", operateState);
		resultObj.put("payment_state", paymentState);
		resultObj.put("payment_id", paymentId);

		List<JSONObject> resultList = new ArrayList<JSONObject>();
		resultList.add(resultObj);
		responseData.setData(resultList);

		return responseData;
	}
	
	@Override
	public Data revokeAcewillCharge(String tenantId, Integer storeId, Date reportDate, Integer shiftId, String posNum, String optNum, String thirdBillCode, PosCustomerOperateListEntity cardRecharge) throws Exception
	{
		JSONObject extendParamJson = null;
		if (CommonUtil.hv(cardRecharge.getExtend_param()))
		{
			extendParamJson = JSONObject.fromObject(cardRecharge.getExtend_param());
		}

		JSONObject paramJson = new JSONObject();
		paramJson.put("report_date", DateUtil.formatDate(reportDate));
		paramJson.put("shift_id", shiftId);
		paramJson.put("pos_num", posNum);
		paramJson.put("opt_num", optNum);
		paramJson.put("chanel", cardRecharge.getChanel());
		paramJson.put("card_code", cardRecharge.getCard_code());
		paramJson.put("name", cardRecharge.getCustomer_name());
		paramJson.put("mobil", cardRecharge.getMobil());
		paramJson.put("biz_id", thirdBillCode);
		paramJson.put("deal_id", cardRecharge.getBill_code());
//		paramJson.put("cashier_id", "-1");
		paramJson.put("recommenderecode", cardRecharge.getSales_person());
		paramJson.put("payment_id", cardRecharge.getPayment_id());
		paramJson.put("money", cardRecharge.getTrade_amount());
		paramJson.put("gift", ParamUtil.getDoubleValueByObject(extendParamJson, "gift"));
		paramJson.put("charge_total", ParamUtil.getDoubleValueByObject(extendParamJson, "charge_total"));
		paramJson.put("award_credit", ParamUtil.getDoubleValueByObject(extendParamJson, "award_credit"));
		paramJson.put("expired", ParamUtil.getStringValueByObject(extendParamJson, "expired"));
		paramJson.put("is_invoice", cardRecharge.getIs_invoice());
//		paramJson.put("local_currency", "CNY");

		List<JSONObject> data = new ArrayList<>();
		data.add(paramJson);

		Data paramData = Data.get(tenantId, storeId, Constant.CODE_SUCCESS);
		paramData.setData(data);

		Data resultData = this.revokeAcewillCharge(tenantId, storeId, thirdBillCode, paramData);
		if(Constant.CODE_SUCCESS==resultData.getCode())
		{
			customerDao.updatePosCustomerOperateList(tenantId, storeId, thirdBillCode, "1", SysDictionary.OPERAT_TYPE_CZ);
		}
		return resultData;
	}
	
	@Override
	public void printForCustomerCardRecharge(String tenancyId, Integer storeId, String printCode, JSONObject paramJson) throws Exception
	{
		try
		{
			String posNum = ParamUtil.getStringValueByObject(paramJson, "pos_num");
			String strUrlContent = ParamUtil.getStringValueByObject(paramJson, "url_content");
			String strUrlPath = ParamUtil.getStringValueByObject(paramJson, "url_path");
			Double payMoney = ParamUtil.getDoubleValueByObject(paramJson, "income");
			String billCode = ParamUtil.getStringValueByObject(paramJson, "bill_code");
			Integer paymentId = ParamUtil.getIntegerValueByObject(paramJson, "payment_id");

			String isInvoice = "0";
			Double invoiceAmount = 0d;

			if (CommonUtil.hv(strUrlContent))
			{
				isInvoice = "1";
				invoiceAmount = payMoney;
			}
			String paymentName = "";
			if (null != paymentId && paymentId > 0)
			{
				JSONObject paymentWayJson = customerDao.getPaymentWayByID(tenancyId, storeId, paymentId);
				paymentName = ParamUtil.getStringValueByObject(paymentWayJson, "payment_name");
			}

			// // 打印入参
			JSONObject printJson = new JSONObject();
			printJson.put("print_code", printCode);
			printJson.put("mode", "1");

			printJson.put("pos_num", posNum);
			printJson.put("bill_code", billCode);
			printJson.put("income", payMoney);
			printJson.put("bill_money", payMoney);
			printJson.put("pay_money", payMoney);
			printJson.put("paymenttypename", paymentName);
			printJson.put("level_name", ParamUtil.getStringValueByObject(paramJson, "level_name"));
			printJson.put("card_class_name", ParamUtil.getStringValueByObject(paramJson, "card_class_name"));
			printJson.put("card_code", ParamUtil.getStringValueByObject(paramJson, "card_code"));
			printJson.put("name", ParamUtil.getStringValueByObject(paramJson, "name"));
			printJson.put("mobil", ParamUtil.getStringValueByObject(paramJson, "mobil"));
			printJson.put("main_trading", ParamUtil.getDoubleValueByObject(paramJson, "main_trading"));
			printJson.put("reward_trading", ParamUtil.getDoubleValueByObject(paramJson, "reward_trading"));
			printJson.put("main_balance", ParamUtil.getDoubleValueByObject(paramJson, "main_balance")); //充值前金额
			printJson.put("before_balance", ParamUtil.getDoubleValueByObject(paramJson, "before_balance"));
			printJson.put("reward_balance", ParamUtil.getDoubleValueByObject(paramJson, "reward_balance"));
			printJson.put("reward_credit", ParamUtil.getDoubleValueByObject(paramJson, "reward_credit"));
			printJson.put("useful_credit", ParamUtil.getDoubleValueByObject(paramJson, "useful_credit"));

			printJson.put("before_credit",ParamUtil.getDoubleValueByObject(paramJson, "before_credit"));//充值后前积分

			printJson.put("total_main", 0d);
			printJson.put("total_reward", 0d);
			printJson.put("credit", 0d);
			printJson.put("operator", ParamUtil.getStringValueByObject(paramJson, "operator"));
			printJson.put("channel", ParamUtil.getStringValueByObject(paramJson, "chanel"));
			printJson.put("updatetime", ParamUtil.getStringValueByObject(paramJson, "updatetime"));
			printJson.put("reward_coupon", ParamUtil.getStringValueByObject(paramJson, "reward_coupon"));
			printJson.put("url_content", strUrlContent);
			printJson.put("url_path", strUrlPath);
			printJson.put("is_invoice", isInvoice);
			printJson.put("invoice_amount", invoiceAmount);
			printJson.put("salesman_name", ParamUtil.getStringValueByObject(paramJson, "salesman_name"));
			
			if (posPrintNewService.isNONewPrint(tenancyId, storeId))
			{// 如果启用新的打印模式
				posPrintNewService.posPrintByMode(tenancyId, storeId, printCode, printJson);
			}
			else
			{
				List<JSONObject> printList = new ArrayList<>();
				printList.add(printJson);

				Data paramData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
				paramData.setData(printList);

				Data result = new Data();

				String printCountStr = customerDao.getSysParameter(tenancyId, storeId, "MemberReceiptCount");
				if (CommonUtil.isNullOrEmpty(printCountStr))
				{
					printCountStr = "0";
				}
				Integer printCount = Integer.parseInt(printCountStr);
				if (printCount <= 0)
				{
					printCount = 1;
				}

				for (int i = 0; i < printCount; i++)
				{
					posPrintService.printPosBill(paramData, result);
				}
			}
		}
		catch (Exception e)
		{
			// TODO Auto-generated catch block
			//e.printStackTrace();
		}
	}
	
	@Override
	public void printForCustomerCardRecharge(String tenancyId, Integer storeId, JSONObject paramJson) throws Exception
	{
		String isInvoice = ParamUtil.getStringValueByObject(paramJson, "is_invoice");
		if ("1".equals(isInvoice))
		{
			// 生成电子发票
			JSONObject invoiceParamJson = new JSONObject();
			invoiceParamJson.put("is_invoice", isInvoice);
			invoiceParamJson.put("bill_code", ParamUtil.getStringValueByObject(paramJson, "bill_code"));
			invoiceParamJson.put("business_date", ParamUtil.getStringValueByObject(paramJson, "report_date"));
			JSONObject invoiceJson = cardRechargeService.createElectricInvoice(tenancyId, storeId, invoiceParamJson);

			paramJson.put("url_content", invoiceJson.optString("url_content"));
			paramJson.put("url_path", invoiceJson.optString("url_path"));
		}
		// 打印小票
		this.printForCustomerCardRecharge(tenancyId, storeId, SysDictionary.PRINT_CODE_1008, paramJson);
	}

    @Override
    public Data findAcewillChargeRule(Data requestData) throws Exception {

        String tenancyId = requestData.getTenancy_id();
        Integer storeId = requestData.getStore_id();
        Data resData = new Data();
        // 组织接口参数
        JSONObject jsonParam = new JSONObject();
        jsonParam.put("shop_id", getShopId(tenancyId, storeId));

        // 请求acewill接口
        List<JSONObject> data = new ArrayList<JSONObject>();
        data.add(jsonParam);
        Data paramData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
        paramData.setData(data);
        paramData.setType(requestData.getType());
        paramData.setT(requestData.getT());
        paramData.setOper(requestData.getOper());
        try {
            //查询门店储值设置
            resData = this.commonPost(paramData, RequestUrlConstant.FIND_ACEWILL_CHARGE_RULE_URL);

            if(Constant.CODE_SUCCESS == resData.getCode()){
                JSONObject resultJson = JSONObject.fromObject(resData.getData().get(0));
                if(null!=resultJson && !resultJson.isEmpty()){
                    String expired = resultJson.optString("expired");//有效期
                    if(!WLifeCrmConstant.EXPIRED_DEFAULT.equals(expired)){
                        Date expired_date = DateUtil.parseDate(expired);
                        Date now =new Date();
                        if(expired_date.before(now)){
                            logger.info("有效的储值规则不存在");
                            resData = Data.get();
                            resData.setMsg("有效的储值规则不存在");
                            resData.setCode(Constant.CODE_INNER_EXCEPTION);
                            resData.setSuccess(false);
                        }
                    }
                }
            }

        } catch (Exception e) {
            logger.info("查询门店储值设置请求失败" + e.getLocalizedMessage());
            resData = Data.get();
            resData.setMsg("查询门店储值设置求失败");
            resData.setCode(Constant.CODE_INNER_EXCEPTION);
            resData.setSuccess(false);
        }
        return resData;
    }
    
    @Override
	public void savePosLog(String tenantId, Integer storeId, String posNum, String optNum, Integer shift_id, Date reportDate, String content, String oldstate, String newstate) throws Exception
	{
		try
		{
			String optName = customerDao.getEmpNameById(optNum, tenantId, storeId);
			customerDao.savePosLog(tenantId, storeId, posNum, optNum, optName, shift_id, reportDate, Constant.TITLE, content, oldstate, newstate);
		}
		catch (Exception e)
		{
			logger.info("保存日志失败", e);
		}
	}
}
