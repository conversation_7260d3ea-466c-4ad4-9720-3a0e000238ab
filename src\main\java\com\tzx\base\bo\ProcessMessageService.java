package com.tzx.base.bo;

import java.util.List;

import net.sf.json.JSONObject;



/**
 *
 * <AUTHOR>
 * 2015年8月3日-上午10:21:24
 */
public interface ProcessMessageService
{
	String NAME = "com.tzx.base.bo.imp.ProcessMessageServiceImp";
	
	/**
	 * @param data
	 * @return
	 */
	@Deprecated
	public boolean batchInsert(String [] data);
	
	/**更新版本
	 * @param tenancy_id
	 * @param organId
	 * @param param
	 * @return
	 * @throws Exception
	 */
	public boolean updateBasicVersion(String tenancyId,int organId) throws Exception;
	
	/** 同步基础资料到线上
	 * @param tenancyId
	 * @param storeId
	 * @throws Exception
	 */
	public void syncBasicDataToOnline(String tenancyID, int storeId, JSONObject params) throws Exception;
	
	/**
	 * @param tenancyId
	 * @param organId
	 * @return
	 * @throws Exception
	 */
	public void updateDataVersion(String tenancyId,int organId,String state) throws Exception;
	
	/** 初始化基本数据
	 * @param tenancyId
	 * @param storeId
	 * @param param
	 * @throws Exception
	 */
	public boolean initBasicData(String tenancyId,int storeId,JSONObject param) throws Exception;
	
	/**更新菜品图片
	 * @param tenancy_id
	 * @param organId
	 * @return
	 * @throws Exception
	 */
	public boolean updateProductPhoto(String tenancyId,int organId) throws Exception;
	
	/** 
	 * @param tenancyId
	 * @param storeId
	 * @param data
	 * @return
	 * @throws Exception
	 */
	public void addOrders(String tenancyId,int storeId,JSONObject data) throws Exception;
	
	/**
	 * @param tenancyId
	 * @param storeId
	 * @param data
	 * @throws Exception
	 */
	public void cancleOrders(String tenancyId,int storeId,JSONObject data) throws Exception;
	
	/** 
	 * @param tenancyId
	 * @param storeId
	 * @param data
	 * @throws Exception
	 */
	public void opinionOrders(String tenancyId,int storeId,JSONObject data) throws Exception;
	
	/** 订单完成
	 * @param tenancyId
	 * @param storeId
	 * @param data
	 * @throws Exception
	 */
	public void completeOrders(String tenancyId,int storeId,JSONObject data) throws Exception;
	
	/**加菜(后付)
	 * @param tenancyId
	 * @param storeId
	 * @param data
	 * @throws Exception
	 */
	public void updateDish(String tenancyId,int storeId,JSONObject data) throws Exception;

	/** 下载副屏图片
	 * @param tenancyId
	 * @param organId
	 * @return
	 * @throws Exception
	 */
	public boolean downloadViceScreenPhoto(String tenancyId,int organId) throws Exception;

	/**
	 * 清除业务缓存数据并更新
	 * @param tenancyId
	 * @param organId
	 * @return
	 * @throws Exception
	 */
	@Deprecated
	public boolean removeAndUpdateCache(String tenancyId,int organId) throws Exception;

	/**
	  * @Description: 查询本地DB是否存在对应表的版本号
	  * @Title:findTableNameId
	  * @param:@param tableName
	  * @param:@return
	  * @return: int
	  * @author: zhouquan
	  * @version: 1.0
	  * @Date: 2018年4月11日
	*/
	public List<JSONObject> findTableNameId(String tableName);

	/**
	  * @Description: 将下发的表和表的版本号存入本地DB
	  * @Title:insertTableNameAndVersion
	  * @param:@param tableName
	  * @param:@param tableVersion
	  * @return: void
	  * @author: zhouquan
	  * @version: 1.0
	 * @param time 
	 * @param tableVersion2 
	 * @param storeId 
	  * @Date: 2018年4月11日
	*/
	public String insertTableNameAndVersion(String tenancyId ,int storeId, String tableName,  String tableVersion,String time,String recordCount);

	/**
	  * @Description: 在本地DB修改下发的表和表的版本号
	  * @Title:updateTableNameAndVersion
	  * @param:@param tableName
	  * @param:@param tableVersion
	  * @return: void
	  * @author: zhouquan
	  * @version: 1.0
	 * @param time 
	 * @param tenancyId 
	 * @param storeId 
	  * @Date: 2018年4月11日
	*/
	public String updateTableNameAndVersion(String tenancyId ,int storeId, String tableName,  String tableVersion,String time,String recordCount);
	
	/*
	 * 查询序列是否存在
	 */
	public String ifExistSequenceOrTable(String sequenceName) throws Exception;
	
	/*
	 * 初始化序列值
	 */
	public String initSeqValue(String sql) throws Exception;

	/**
	  * @Description: 重新接收订单
	  * @Title:modifyOrder
	  * @param:@param tenancyId
	  * @param:@param storeId
	  * @param:@param data
	  * @param:@throws Exception
	  * @return: void
	  * @author: shenzhanyu
	  * @version: 1.0
	  * @Date: 2018年4月13日
	 */
	void modifyOrder(String tenancyId,int storeId,JSONObject data) throws Exception;
	
	/**
	  * @Description: 重新取消订单
	  * @Title:cancleModifyOrders
	  * @param:@param tenancyId
	  * @param:@param storeId
	  * @param:@param data
	  * @param:@throws Exception
	  * @return: void
	  * @author: shenzhanyu
	  * @version: 1.0
	  * @Date: 2018年4月15日
	 */
	void cancleModifyOrders(String tenancyId,int storeId,JSONObject data) throws Exception;
	
	/** 更改订单配送方式
	 * @param tenancyId
	 * @param storeId
	 * @param data
	 * @throws Exception
	 */
	void modifySendModeForOrders(String tenancyId,int storeId,JSONObject data) throws Exception;
	
	/**设置新美大会员参数
	 * @param tenancyId
	 * @param storeId
	 * @throws Exception
	 */
	public void setXMDCRMConfigToSysParameter(String tenancyId, int storeId) throws Exception;
	
	/** 订单管理
	 * @param tenancyId
	 * @param storeId
	 * @param data
	 * @return
	 * @throws Exception
	 */
	public String ordersManagement(String tenancyId,int storeId,JSONObject data) throws Exception;
	
	/**
	  * @Description: 外卖订单退款
	  * @Title:refundOrders
	  * @param:@param tenancyId
	  * @param:@param storeId
	  * @param:@param data
	  * @param:@throws Exception
	  * @return: void
	  * @author: shenzhanyu
	  * @version: 1.0
	  * @Date: 2018年5月21日
	 */
	public void refundOrders(String tenancyId,int storeId,JSONObject data) throws Exception;
	
	/** 外卖配送异常订单
	 * @param tenancyId
	 * @param storeId
	 * @param data
	 * @throws Exception
	 */
	void deliverExcepetionForOrders(String tenancyId,int storeId,JSONObject json) throws Exception;

	/**
	 * 部分退是否存在
	 * @param tenentid
	 * @param storeid
	 * @param bill_state
	 * @param order_code
	 * @return
	 * @throws Exception
	 */
	public  Boolean isExitsBFTCJ(String tenentid, int storeid, String bill_state, String order_code) throws Exception;
}
