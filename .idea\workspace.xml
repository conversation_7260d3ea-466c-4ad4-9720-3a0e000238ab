<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ArtifactsWorkspaceSettings">
    <artifacts-to-build>
      <artifact name="saaspos:war exploded" />
    </artifacts-to-build>
  </component>
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="d4d7c543-d2ef-45f7-83c2-52075d643a82" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/src/main/java/com/tzx/framework/common/util/MessageUtils.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/tzx/framework/common/util/MessageUtils.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/tzx/pos/service/rest/PosRest.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/tzx/pos/service/rest/PosRest.java" afterDir="false" />
    </list>
    <list id="a86d4fac-551a-4ecb-b2c2-3b9b9d4f1c54" name="config" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="GitLabMergeRequestFiltersHistory"><![CDATA[{
  "history": [
    {
      "assignee": {
        "type": "org.jetbrains.plugins.gitlab.mergerequest.ui.filters.GitLabMergeRequestsFiltersValue.MergeRequestsMemberFilterValue.MergeRequestsAssigneeFilterValue",
        "username": "fuguonan",
        "fullname": "fuguonan"
      }
    }
  ],
  "lastFilter": {
    "assignee": {
      "type": "org.jetbrains.plugins.gitlab.mergerequest.ui.filters.GitLabMergeRequestsFiltersValue.MergeRequestsMemberFilterValue.MergeRequestsAssigneeFilterValue",
      "username": "fuguonan",
      "fullname": "fuguonan"
    }
  }
}]]></component>
  <component name="GitLabMergeRequestsSettings">{
  &quot;selectedUrlAndAccountId&quot;: {
    &quot;first&quot;: &quot;https://gitlab.acewill.cn/tzx/pos/posservice/saaspos_v1.32.x_0322.git&quot;,
    &quot;second&quot;: &quot;0585a476-d2f2-4874-a725-b465bf82d5b5&quot;
  }
}</component>
  <component name="GitToolBoxStore">
    <option name="largeFiles">
      <LargeFiles>
        <option name="unlockedFilePaths">
          <set>
            <option value="$PROJECT_DIR$/src/main/java/com/tzx/pos/bo/imp/PosServiceImp.java" />
          </set>
        </option>
      </LargeFiles>
    </option>
  </component>
  <component name="LogFilters">
    <option name="FILTER_ERRORS" value="false" />
    <option name="FILTER_WARNINGS" value="false" />
    <option name="FILTER_INFO" value="true" />
    <option name="FILTER_DEBUG" value="true" />
    <option name="CUSTOM_FILTER" />
  </component>
  <component name="MavenRunner">
    <option name="skipTests" value="true" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 4
}</component>
  <component name="ProjectId" id="2uz8jz9OkuYkSz9zSPlRAsLxymJ" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Application.MessageUtils.executor": "Debug",
    "Maven.saaspos [package].executor": "Run",
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "Tomcat Server.Tomcat 8.5.100.executor": "Debug",
    "WebServerToolWindowFactoryState": "false",
    "com.codeium.enabled": "true",
    "git-widget-placeholder": "master",
    "ignore.virus.scanning.warn.message": "true",
    "last_opened_file_path": "D:/IdeaProjects/tzx/pos/posservice/saaspos_v1.32.x_0322",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "ssh.settings",
    "spring.configuration.checksum": "feb085d55805676df7500d7661547a07",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RunManager" selected="Application.MessageUtils">
    <configuration name="MessageUtils" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.tzx.framework.common.util.MessageUtils" />
      <module name="saaspos" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.tzx.framework.common.util.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="Tomcat 8.5.100" type="#com.intellij.j2ee.web.tomcat.TomcatRunConfigurationFactory" factoryName="Local" APPLICATION_SERVER_NAME="Tomcat 8.5.100" ALTERNATIVE_JRE_ENABLED="false" nameIsGenerated="true">
      <option name="UPDATING_POLICY" value="restart-server" />
      <deployment>
        <artifact name="saaspos:war exploded">
          <settings>
            <option name="CONTEXT_PATH" value="/" />
          </settings>
        </artifact>
      </deployment>
      <server-settings>
        <option name="BASE_DIRECTORY_NAME" value="c333fe85-8f27-4fdc-8a6b-3d4fbbbd196c" />
        <option name="HTTP_PORT" value="80" />
      </server-settings>
      <predefined_log_file enabled="true" id="Tomcat" />
      <predefined_log_file enabled="true" id="Tomcat Catalina" />
      <predefined_log_file id="Tomcat Manager" />
      <predefined_log_file id="Tomcat Host Manager" />
      <predefined_log_file id="Tomcat Localhost Access" />
      <RunnerSettings RunnerId="Debug">
        <option name="DEBUG_PORT" value="52997" />
      </RunnerSettings>
      <ConfigurationWrapper VM_VAR="JAVA_OPTS" RunnerId="Cover">
        <option name="USE_ENV_VARIABLES" value="true" />
        <STARTUP>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </STARTUP>
        <SHUTDOWN>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </SHUTDOWN>
      </ConfigurationWrapper>
      <ConfigurationWrapper VM_VAR="JAVA_OPTS" RunnerId="Debug">
        <option name="USE_ENV_VARIABLES" value="true" />
        <STARTUP>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </STARTUP>
        <SHUTDOWN>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </SHUTDOWN>
      </ConfigurationWrapper>
      <ConfigurationWrapper VM_VAR="JAVA_OPTS" RunnerId="Profile">
        <option name="USE_ENV_VARIABLES" value="true" />
        <STARTUP>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </STARTUP>
        <SHUTDOWN>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </SHUTDOWN>
      </ConfigurationWrapper>
      <ConfigurationWrapper VM_VAR="JAVA_OPTS" RunnerId="Run">
        <option name="USE_ENV_VARIABLES" value="true" />
        <STARTUP>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </STARTUP>
        <SHUTDOWN>
          <option name="USE_DEFAULT" value="true" />
          <option name="SCRIPT" value="" />
          <option name="VM_PARAMETERS" value="" />
          <option name="PROGRAM_PARAMETERS" value="" />
        </SHUTDOWN>
      </ConfigurationWrapper>
      <method v="2">
        <option name="Make" enabled="true" />
        <option name="BuildArtifacts" enabled="true">
          <artifact name="saaspos:war exploded" />
        </option>
      </method>
    </configuration>
    <list>
      <item itemvalue="Application.MessageUtils" />
      <item itemvalue="Tomcat Server.Tomcat 8.5.100" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="Application.MessageUtils" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.26927.53" />
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-IU-251.26927.53" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="d4d7c543-d2ef-45f7-83c2-52075d643a82" name="Changes" comment="" />
      <changelist id="a86d4fac-551a-4ecb-b2c2-3b9b9d4f1c54" name="config" comment="" />
      <created>1743240183367</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1743240183367</updated>
      <workItem from="1743240185297" duration="2335000" />
      <workItem from="1743597678045" duration="694000" />
      <workItem from="1751871027000" duration="190000" />
      <workItem from="1751881266478" duration="2874000" />
      <workItem from="1751948569193" duration="2642000" />
      <workItem from="1751955627602" duration="6787000" />
      <workItem from="1752027054782" duration="13574000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/tzx/pos/bo/imp/UploadDataServiceImp.java</url>
          <line>912</line>
          <option name="timeStamp" value="9" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/tzx/pos/ndt/imp/UploadDataExServiceImp.java</url>
          <line>231</line>
          <option name="timeStamp" value="13" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>jar://$PROJECT_DIR$/../../../../../work/mavenResp/org/apache/activemq/activemq-all/5.10.0/activemq-all-5.10.0.jar!/org/apache/activemq/ActiveMQMessageProducer.class</url>
          <line>172</line>
          <option name="timeStamp" value="19" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/tzx/framework/common/util/MessageUtils.java</url>
          <line>45</line>
          <option name="timeStamp" value="20" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
    <watches-manager>
      <configuration name="#com.intellij.j2ee.web.tomcat.TomcatRunConfigurationFactory">
        <watch expression="PosPropertyUtil.getMsg(&quot;tzxmq.password&quot;)" />
        <watch expression="PosPropertyUtil.getMsg(&quot;tzxmq.url&quot;)" />
      </configuration>
    </watches-manager>
  </component>
</project>