package com.tzx.member.yazuo.task;

import org.apache.log4j.Logger;

import com.tzx.framework.common.constant.Constant;
import com.tzx.framework.common.util.CommonUtil;
import com.tzx.framework.common.util.SpringConext;
import com.tzx.member.yazuo.bo.YazuoCardConsumeService;
import com.tzx.member.yazuo.bo.YazuoCrmService;

public class CacheConsumRuleRunnable implements Runnable
{
	private static final Logger logger = Logger.getLogger(CacheConsumRuleRunnable.class);

	@Override
	public void run()
	{
		String tenancyId = null;
		if (Constant.systemMap.containsKey("tenent_id"))
		{
			tenancyId = Constant.systemMap.get("tenent_id");
		}

		Integer storeId = Integer.valueOf(Constant.systemMap.get("store_id"));
		if (Constant.systemMap.containsKey("store_id"))
		{
			storeId = Integer.valueOf(Constant.systemMap.get("store_id"));
		}

		if (CommonUtil.isNullOrEmpty(tenancyId) || CommonUtil.isNullOrEmpty(storeId))
		{
			logger.info("参数为空");
			return;
		}

		try
		{
			YazuoCrmService yazuoCrmService = (YazuoCrmService) SpringConext.getBean(tenancyId, YazuoCrmService.NAME);
			if (yazuoCrmService.isEnableYazuoMember(tenancyId, storeId))
			{
				YazuoCardConsumeService consumeService = (YazuoCardConsumeService) SpringConext.getBean(tenancyId, YazuoCardConsumeService.NAME);
				consumeService.setMemberConsumRuleCache(tenancyId, storeId, null);
			}
		}
		catch (Exception e)
		{
			// TODO: handle exception
		}
	}

}
