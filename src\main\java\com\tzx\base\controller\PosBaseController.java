package com.tzx.base.controller;

import java.io.InputStream;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import net.sf.json.JSONObject;

import org.apache.log4j.Logger;
import org.springframework.stereotype.Controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tzx.base.bo.OrganService;
import com.tzx.base.constant.Constant;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.ExceptionMessage;
import com.tzx.framework.common.util.InputStreamUtils;


/**
 * 
 * <AUTHOR> 
 * @日期：2015年5月21日-下午6:30:46
 */
@Controller
public class PosBaseController
{
	protected static Logger logger = Logger.getLogger(PosBaseController.class);
	
	@Resource(name=OrganService.NAME)
	private OrganService organService;
	
	protected JSONObject getPostJson(HttpServletRequest request)
	{
		JSONObject json = null;
		
		try
		{
			InputStream in = request.getInputStream();

			String reStr = InputStreamUtils.InputStreamTOString(in);
			
			logger.info("param === " + reStr);
			
			if(reStr != null && reStr.trim().length() > 0)
			{
				json = JSONObject.fromObject(reStr);
			}
		}
		catch(Exception e)
		{
			logger.info("getPostJson失败，原因：" + ExceptionMessage.getExceptionMessage(e));
		}
		
		logger.info("请求数据："+json);
		
		return json;
	}
	
	protected Data getPostData(HttpServletRequest request)
	{
		ObjectMapper objectMapper = new ObjectMapper();
		
		Data param = null; 
		
		try {
			InputStream in = request.getInputStream();

			String reStr = InputStreamUtils.InputStreamTOString(in);
			
			JSONObject obj = JSONObject.fromObject(reStr);
			//不要使用 System.out.println()，如有必要，用logger.debug()替代("传入的参数：" + obj.toString());
			logger.info("传入的参数：" + obj.toString());
			param = objectMapper.convertValue(obj,Data.class);
		}catch (Exception se) {
			param = new Data();
			
			logger.info("参数解析错误，原因：" + se.getMessage());
			
			logger.info("错误显示：" + se.getCause());
			
			param.setCode(Constant.CODE_INNER_EXCEPTION);
			
			param.setMsg("参数解析错误，请检查传入的参数名称或类型！");
			
		}
		
		logger.info("post data="+ param.toString());
		
		return param;
	}
	/**
	 * 根据传入的json转换成data对象
	 * @param json
	 * @return
	 */
	public Data getRequestData(JSONObject json)
	{
		ObjectMapper objectMapper = new ObjectMapper();
		Data param = null; 
		try
		{
			param = objectMapper.readValue(json.toString(),Data.class);
		}catch (Exception se)
		{
			param = new Data();
			
			param.setCode(Constant.CODE_PARAM_FAILURE);
			
			param.setMsg("json转data类型错误，请查看传入参数");
			
			//e.printStackTrace();
			
			logger.info("Fastjson 类型解析错误" + ExceptionMessage.getExceptionMessage(se));
			
			return param;
		}
		return param;
	}
	/**
	 * 用来判断门店状态是否正常
	 * @param tenancyId 机构id
	 * @param organId 门店id
	 * @return 正常营业，关店，内部装修或暂停营业
	 */
	public JSONObject isStoreNormal(String tenancyId, Integer organId)
	{
		JSONObject obj = new JSONObject();
		try
		{
			JSONObject object = organService.getOrganOperatingStatus(tenancyId, organId);
			/**
			 * status 1:非正常营业，0:是正常营业
			 * msg 正常营业，关店，内部装修或暂停营业
			 */
			if (object.getInt("status") == 0)
			{
				obj.put("code", Constant.CODE_SUCCESS);
			}else
			{
				obj.put("code", Constant.CODE_STORE_EXCEPTION);
				obj.put("msg", object.getString("result"));
			}
		}catch(Exception se)
		{
			logger.info("查询门店状态失败：" + ExceptionMessage.getExceptionMessage(se));
			//e.printStackTrace();
			obj.put("code", Constant.CODE_INNER_EXCEPTION);
			obj.put("msg", Constant.QUERY_ORGAN_STATUS_FAILURE);
			return obj;
		}
		return obj;
	}
	
	public void basicVersion() 
	{
		
	}
}
