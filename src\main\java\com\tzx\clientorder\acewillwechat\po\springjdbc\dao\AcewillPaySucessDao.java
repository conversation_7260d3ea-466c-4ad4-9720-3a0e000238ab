package com.tzx.clientorder.acewillwechat.po.springjdbc.dao;

import java.util.List;

import com.tzx.member.common.entity.CrmCardTradingListEntity;
import com.tzx.pos.base.dao.BaseDao;
import com.tzx.clientorder.common.entity.AcewillPaymentWay;
import com.tzx.clientorder.common.entity.AcewillPosBillMember;
import com.tzx.clientorder.common.entity.AcewillPosBillPayment;
import com.tzx.clientorder.common.entity.AcewillPosBillPaymentCoupons;

import net.sf.json.JSONObject;

public interface AcewillPaySucessDao extends BaseDao{
	String	NAME	= "com.tzx.clientorder.acewillwechat.po.springjdbc.dao.imp.AcewillPaySucessDaoImpl";

	List<AcewillPaymentWay> findPaymentWay(String tenantId, Integer storeId, List<String> paymentClassList) throws Exception;

	void savePosBillMember(List<AcewillPosBillMember> posBillMemberList) throws Exception;

	void savePosBillPayment(List<AcewillPosBillPayment> posBillPaymentList) throws Exception;

	JSONObject getPosBillByBillNum(String tenantId, String billNum) throws Exception;

	void saveCrmCardTradingList(List<CrmCardTradingListEntity> list) throws Exception;
	
	void savePosBillPaymentCouponsList(List<AcewillPosBillPaymentCoupons> list) throws Exception;

}

