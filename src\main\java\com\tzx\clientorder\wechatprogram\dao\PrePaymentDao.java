package com.tzx.clientorder.wechatprogram.dao;

import java.util.List;

import com.tzx.base.entity.PaymentWay;
import com.tzx.base.entity.PosBill;
import com.tzx.base.entity.PosBillDiscount;
import com.tzx.base.entity.PosBillPayment;
import com.tzx.base.entity.PosBillPaymentCoupons;
import com.tzx.base.entity.PosBillService;
import com.tzx.member.common.entity.CrmCardTradingListEntity;

import net.sf.json.JSONObject;

public interface PrePaymentDao extends PromptGenericDao
{
	String NAME = "com.tzx.clientorder.wechatprogram.dao.impl.PrePaymentDaoImp";

	/**
	 * @param tenantId
	 * @param storeId
	 * @param paymentClassList
	 * @return
	 * @throws Exception
	 */
	public List<PaymentWay> findPaymentWay(String tenantId, Integer storeId, List<String> paymentClassList) throws Exception;

	/**
	 * @param tenentId
	 * @param itemIdList
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> findItemUnit(String tenentId, List<String> itemIdList) throws Exception;

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param duids
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getUnitNameList(String tenancyId, int storeId, List<String> duids) throws Exception;

	/**
	 * 查询套餐明细
	 * 
	 * @param tenantId
	 * @param itemIdList
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getItemComboDetails(String tenantId, List<Integer> itemIdList) throws Exception;

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param bill
	 * @throws Exception
	 */
	public void savePosBill(String tenancyId, int storeId, PosBill bill) throws Exception;

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param bill
	 * @throws Exception
	 */
	public void updatePosBillByPay(String tenancyId, int storeId, PosBill bill) throws Exception;

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @return
	 * @throws Exception
	 */
	public PosBill getPosBillBeanByBillnum(String tenancyId, int storeId, String billNum) throws Exception;

	/**
	 * @param tenantId
	 * @param storeId
	 * @param outOrderId
	 * @return
	 * @throws Exception
	 */
	public JSONObject getBillNumByOutOrderId(String tenantId, Integer storeId, String outOrderId) throws Exception;

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param serviceList
	 * @throws Exception
	 */
	public void savePosBillService(String tenancyId, int storeId, List<PosBillService> serviceList) throws Exception;

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param discountList
	 * @throws Exception
	 */
	public void savePosBillDiscount(String tenancyId, int storeId, List<PosBillDiscount> discountList) throws Exception;

	/**
	 * @param list
	 * @throws Exception
	 */
	public void savePosBillPayment(List<PosBillPayment> list) throws Exception;

	/**
	 * @param list
	 * @throws Exception
	 */
	public void savePosBillPaymentCouponsList(List<PosBillPaymentCoupons> list) throws Exception;

	/**
	 * @param list
	 * @throws Exception
	 */
	public void saveCrmCardTradingList(List<CrmCardTradingListEntity> list) throws Exception;
}
