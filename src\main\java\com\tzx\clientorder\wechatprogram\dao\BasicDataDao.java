package com.tzx.clientorder.wechatprogram.dao;

import java.util.List;

import net.sf.json.JSONObject;

/**
 * Created by qingui on 2018-06-27.
 */
public interface BasicDataDao extends PromptGenericDao
{
	String NAME = "com.tzx.clientorder.wechatprogram.dao.impl.BasicDataDaoImp";

	/**
	 * 查询门店的菜品沽清信息
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @return
	 * @throws Exception
	 */
	List<JSONObject> getSoldOutInfo(String tenancyId, int storeId) throws Exception;

	/**
	 * 查询桌台信息
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @return
	 * @throws Exception
	 */
	List<JSONObject> getTables(String tenancyId, int storeId) throws Exception;

	/**
	 * 查询菜品类别
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param chanel
	 * @return
	 * @throws Exception
	 */
	List<JSONObject> getDishKinds(String tenancyId, Integer storeId, String chanel) throws Exception;

	/**
	 * 菜品信息
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @return
	 * @throws Exception
	 */
	List<JSONObject> getItemInfos(String tenancyId, int storeId, String chanel) throws Exception;

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param comboIds
	 * @param itemIds
	 * @return
	 * @throws Exception
	 */
	List<JSONObject> getItemInfosForCombo(String tenancyId, int storeId, int[] comboIds, int[] itemIds, String chanel) throws Exception;

	/**
	 * @param tenancy_id
	 * @param store_id
	 * @param chanel
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getItemInfosForCombo(String tenancy_id, int store_id, String chanel) throws Exception;
	
	/**
	 * 查询菜品规格信息
	 * 
	 * @param tenancyId
	 * @param item_ids
	 * @param price_system
	 * @return
	 * @throws Exception
	 */
	List<JSONObject> getUnitInfos(String tenancyId, int[] itemIds, String chanel, String priceSystem) throws Exception;

	/**
	 * 查询菜品做法信息
	 * 
	 * @param tenancyId
	 * @param item_ids
	 * @return
	 * @throws Exception
	 */
	List<JSONObject> getMethodInfos(String tenancyId, int storeId, int[] itemIds, String chanel) throws Exception;

	/**
	 * 套餐
	 * 
	 * @param tenancyId
	 * @param combo_item_ids
	 * @param price_system
	 * @return
	 * @throws Exception
	 */
	List<JSONObject> getComboBaseInfo(String tenancyId, int[] comboItem_ids, String chanel, String priceSystem) throws Exception;

	/**
	 * 套餐-辅菜
	 * 
	 * @param tenancyId
	 * @param mandatoryCombIds
	 * @return
	 * @throws Exception
	 */
	List<JSONObject> getMandatoryBaseInfo(String tenancyId, int[] mandatoryCombIds) throws Exception;

	/**
	 * 套餐-辅菜-菜品组
	 * 
	 * @param tenancyId
	 * @param groupIds
	 * @return
	 * @throws Exception
	 */
	List<JSONObject> getGroupDetails(String tenancyId, int[] groupIds) throws Exception;

	/**
	 * @param tenancyId
	 * @param storeId
	 * @return
	 * @throws Exception
	 */
	public JSONObject selectOrganInfo(String tenancyId, int storeId) throws Exception;

	/**
	 * @param tenancyId
	 * @param storeId
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> selectMemo(String tenancyId, int storeId) throws Exception;

	/**
	 * @param tenancyId
	 * @param combo_item_ids
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> selectDetailsInfo(String tenancyId, int[] comboItemIds) throws Exception;

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param itemIds
	 * @param chanel
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getDishMemoList(String tenancyId, int storeId, int[] itemIds) throws Exception;
	
	/**
	 * @param tenancy_id
	 * @param itemList
	 * @return
	 * @throws Exception
	 */
	public List<JSONObject> getItemInfoList(String tenancy_id, List<Integer> itemList) throws Exception;
}
