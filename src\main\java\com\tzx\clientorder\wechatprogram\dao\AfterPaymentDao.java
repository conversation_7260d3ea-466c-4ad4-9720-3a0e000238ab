package com.tzx.clientorder.wechatprogram.dao;

import java.util.List;

import com.tzx.base.entity.PaymentWay;
import com.tzx.base.entity.PosBillMember;
import com.tzx.base.entity.PosBillPayment;
import com.tzx.base.entity.PosBillPaymentCoupons;
import com.tzx.member.common.entity.CrmCardTradingListEntity;

import net.sf.json.JSONObject;

/**
 *
 */
public interface AfterPaymentDao extends PromptGenericDao
{
	String NAME = "com.tzx.clientorder.wechatprogram.dao.impl.AfterPaymentDaoImpl";

	/**
	 * @param tenantId
	 * @param storeId
	 * @param tableNo
	 * @return
	 * @throws Exception
	 */
	JSONObject getTableStatus(String tenantId, Integer storeId, String tableNo) throws Exception;

	/**
	 * @param storeId
	 * @param tableNo
	 * @param lockPosNum
	 * @param lockOptNum
	 * @param optName
	 * @throws Exception
	 */
	void lockTableStatus(Integer storeId, String tableNo, String lockPosNum, String lockOptNum, String optName) throws Exception;

	/**
	 * 获取桌台状态
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param tableCode
	 * @return
	 * @throws Exception
	 */
	JSONObject getTableStateForBill(String tenancyId, int storeId, String tableCode) throws Exception;

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param orderNum
	 * @return
	 * @throws Exception
	 */
	JSONObject getBillInfoByOrderNum(String tenancyId, int storeId, String orderNum) throws Exception;

	/**
	 * @param tenantId
	 * @param storeId
	 * @param billNum
	 * @return
	 * @throws Exception
	 */
	JSONObject getBillInfoByBillNum(String tenantId, int storeId, String billNum) throws Exception;

	/**
	 * @param tenantId
	 * @param storeId
	 * @param tableNo
	 * @return
	 * @throws Exception
	 */
	JSONObject getBillInfoByTableCode(String tenantId, Integer storeId, String tableNo) throws Exception;

	/**
	 * 查询订单的信息
	 * 
	 * @param tenancyId
	 * @param tableCode
	 * @param storeId
	 * @return
	 * @throws Exception
	 */
	JSONObject getBillInfo(String tenancyId, int storeId, String tableCode) throws Exception;

	/**
	 * @param billNum
	 * @param sourceVal
	 * @param isOrdering
	 * @throws Exception
	 */
	void updatePosBillSource(String tenancyId,String billNum, String channel) throws Exception;

	/**
	 * 修改订单来源为微生活小程序
	 * 
	 * @param tenancyId
	 * @param billNum
	 * @throws Exception
	 */
	void updateOrderSource(String tenancyId, String billNum, String orderNum,String channel) throws Exception;

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @return
	 * @throws Exception
	 */
	JSONObject getBillMemberInfo(String tenancyId, int storeId, String billNum) throws Exception;

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @return
	 * @throws Exception
	 */
	List<JSONObject> getServiceCharge(String tenancyId, int storeId, String billNum) throws Exception;

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @return
	 * @throws Exception
	 */
	List<JSONObject> getItemList(String tenancyId, int storeId, String billNum) throws Exception;

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @return
	 * @throws Exception
	 */
	List<JSONObject> getMethodList(String tenancyId, int storeId, String billNum) throws Exception;

	/**
	 * @param TenentId
	 * @param billNUm
	 * @return
	 * @throws Exception
	 */
	Integer getLastItemSerial(String TenentId, String billNUm) throws Exception;

	/**
	 * 查询菜品规格
	 * 
	 * @param microLifeOrder
	 * @return
	 * @throws Exception
	 */
	List<JSONObject> getUnitNameList(String tenancyId, int storeId, List<String> duids) throws Exception;

	/**
	 * @param tenentId
	 * @param itemIdList
	 * @return
	 * @throws Exception
	 */
	List<JSONObject> findItemUnit(String tenentId, List<String> itemIdList) throws Exception;

	/**
	 * 查询套餐明细
	 * 
	 * @param tenantId
	 * @param itemIdList
	 * @return
	 * @throws Exception
	 */
	List<JSONObject> getItemComboDetails(String tenantId, List<Integer> itemIdList) throws Exception;

	/**
	 * @param billNum
	 * @param type
	 * @param cardCode
	 * @return
	 * @throws Exception
	 */
	JSONObject getBillMemberInfo(String billNum, String type, String cardCode) throws Exception;

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param billnum
	 * @param openId
	 * @throws Exception
	 */
	void insertOpenId(String tenancyId, Integer storeId, String billnum, String openId) throws Exception;

	/**
	 * @param billnum
	 * @param openId
	 * @throws Exception
	 */
	void updateOpenId(String billnum, String openId) throws Exception;

	/**
	 * @param tenancyId
	 * @param storeId
	 * @param tableCode
	 * @return
	 * @throws Exception
	 */
	List<JSONObject> getBillLockOpenId(String tenancyId, int storeId, String tableCode) throws Exception;

	/**
	 * @param tenantId
	 * @param storeId
	 * @param paymentClassList
	 * @return
	 * @throws Exception
	 */
	List<PaymentWay> findPaymentWay(String tenantId, Integer storeId, List<String> paymentClassList) throws Exception;

	/**
	 * @param list
	 * @throws Exception
	 */
	void savePosBillPayment(List<PosBillPayment> list) throws Exception;

	/**
	 * @param list
	 * @throws Exception
	 */
	void savePosBillMember(List<PosBillMember> list) throws Exception;

	/**
	 * @param list
	 * @throws Exception
	 */
	void saveCrmCardTradingList(List<CrmCardTradingListEntity> list) throws Exception;

	/**
	 * @param list
	 * @throws Exception
	 */
	void savePosBillPaymentCouponsList(List<PosBillPaymentCoupons> list) throws Exception;

	List<JSONObject> getBilPayment(String tenancyId, int storeId, String billNum) throws Exception;

	List<JSONObject> getBillDiscountList(String tenancyId, Integer storeId,  String billNum) throws Exception;
}
