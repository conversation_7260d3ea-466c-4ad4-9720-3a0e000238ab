package com.tzx.clientorder.wxorder.bo.imp;

import com.tzx.base.common.util.PosPropertyUtil;
import com.tzx.clientorder.acewillwechat.po.springjdbc.dao.AcewillInsertOrderDao;
import com.tzx.clientorder.wlifeprogram.dao.AfterPaymentDao;
import com.tzx.clientorder.wxorder.base.util.WxOrderUtil;
import com.tzx.clientorder.wxorder.bo.WxInsertOrderService;
import com.tzx.clientorder.wxorder.entity.WxOrder;
import com.tzx.clientorder.wxorder.entity.WxOrderItem;
import com.tzx.clientorder.wxorder.entity.WxOrderItemMethod;
import com.tzx.clientorder.wxorder.po.springjdbc.dao.WxInsertOrderDao;
import com.tzx.framework.common.constant.Oper;
import com.tzx.framework.common.constant.Type;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.HttpRestException;
import com.tzx.framework.common.exception.OtherSystemException;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.Comet4jUtil;
import com.tzx.framework.common.util.DateUtil;
import com.tzx.framework.common.util.HttpUtil;
import com.tzx.framework.common.util.CommonUtil;
import com.tzx.framework.common.util.Tools;
import com.tzx.orders.base.constant.Constant;
import com.tzx.orders.base.util.OrderUtil;
import com.tzx.orders.bo.OrdersManagementService;
import com.tzx.orders.bo.dto.OrderState;
import com.tzx.orders.bo.dto.PosBill;
import com.tzx.orders.bo.dto.PosBillItem;
import com.tzx.orders.po.springjdbc.dao.OrdersManagementDao;
import com.tzx.pos.asyn.pool.ThreadPool;
import com.tzx.pos.asyn.thread.OrderingPrintThread;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.controller.CometDataNoticeClientRunnable;
import com.tzx.pos.base.util.SendUdpToPos;
import com.tzx.pos.bo.PosDishService;
import com.tzx.pos.po.springjdbc.dao.PosBillMemberDao;
import com.tzx.pos.po.springjdbc.dao.PosDao;
import com.tzx.pos.service.servlet.PosDishServlet;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import net.sf.json.JsonConfig;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import javax.annotation.Resource;
import java.util.*;

import static com.tzx.orders.base.constant.Constant.*;

/**
 * @Description 微信后付下单/加菜
 * <AUTHOR>
 * @Date 2018-11-08 13:54
 */
@Service(WxInsertOrderService.NAME)
public class WxInsertOrderServiceImp implements WxInsertOrderService{

    private Logger logger = Logger.getLogger(WxInsertOrderServiceImp.class);

    private static final String WECHAT_ORDER_DISH = "wechat_order_dish";

    @Resource(name = AfterPaymentDao.NAME)
    private AfterPaymentDao afterPaymentDao;

    @Resource(name = AcewillInsertOrderDao.NAME)
    AcewillInsertOrderDao insertOrderDao;

    @Resource
    private PosDao posDao;

    @Resource(name = PosDishServlet.NAME)
    private PosDishServlet posDishServlet;

    @Resource(name = PosDishService.NAME)
    private PosDishService posDishService;

    @Resource(name = OrdersManagementService.NAME)
    private OrdersManagementService ordersManagementService;

    @Resource(name = OrdersManagementDao.NAME)
    private OrdersManagementDao ordersDao;

    @Resource(name = "transactionManager")
    private DataSourceTransactionManager transactionManager;

    @Resource(name = WxInsertOrderDao.NAME)
    private WxInsertOrderDao wxInsertOrderDao;

    @Resource(name = PosBillMemberDao.NAME)
    private PosBillMemberDao			memberDao;

    @Override
    public String ordersManagement(String tenancyId, int storeId, JSONObject data) throws Exception {
        String returnRes = "";
        String oper = data.getString("oper");
        if (Tools.judgeStoreStateOpen(tenancyId, storeId) == false) {
            logger.info("非营业时段不处理外卖订单消息，消息内容：" + data.toString());
            return "非营业时段不处理外卖订单消息";
        }
        JSONArray dataList = data.optJSONArray("data");

        String orderCode = UUID.randomUUID().toString();

        if (dataList != null && !dataList.isEmpty()) {

            JSONObject obj = JSONObject.fromObject(dataList.get(0));
            JSONObject orderJson = obj.optJSONObject("order_list");
            if (null != orderJson) {
                orderCode = orderJson.optString("order_code").intern();
            }
        }

        synchronized (orderCode) {
            if (Oper.add.name().equals(oper)) {
                logger.info("接收订单:" + data);
                try {
                    this.addOrders(tenancyId, storeId, data);
                    returnRes = "接收订单成功";
                    ack(data);
                } catch (Exception e) {
                    logger.error("==========接收订单失败!\n", e);
                    returnRes = "接收订单失败";
                }
            }  else {
                returnRes = "Oper无效";
            }
        }
        return returnRes;
    }



    private void ack(JSONObject getJson) {
        try {
            if (getJson.containsKey("ack_data")) {

                JSONArray data = getJson.getJSONArray("ack_data");
                getJson.put("data", data);

                getJson.discard("ack_data");
                getJson.put("oper","ack");
                String reqURL = PosPropertyUtil.getMsg("saas.url") + "/orderRest/post";
                HttpUtil.post(reqURL, getJson, 3);

            }
        } catch (HttpRestException e) {
            logger.error("order ack error",e);
        }
    }

    @Override
    public  void addOrders(String tenancyId, int storeId, JSONObject data)
            throws Exception {
        logger.info("接收订单addOrders方法 data 参数：：：： "+data);
        if(Tools.judgeStoreStateOpen(tenancyId,storeId) == false){
            logger.info("非营业时段不处理外卖订单接收消息，消息内容："+data.toString());
            return;
        }

        JSONArray dataList = data.optJSONArray("data");
        boolean isAfterChangeShift=data.optBoolean("is_after_change_shift");
        for (Object obj : dataList) {
            List<OrderState> sendList = new ArrayList<OrderState>();

            JSONObject json = JSONObject.fromObject(obj);
            JSONObject orderJson = json.optJSONObject("order_list");
            // 查询订单是否是新订单
            String orderCode = orderJson.optString("order_code").intern();
            //下单时间
            String single_time = orderJson.optString("single_time");
            //订单状态
            String currentOrderState=orderJson.optString("order_state");
            //订单渠道
            String chanel = orderJson.optString("chanel");
//            if(currentOrderState.equals("12")){
//            	currentOrderState =com.tzx.orders.base.constant.Constant.ORDER_STATE_NEW;
//            }
            //账单编号
            String billNum=orderJson.optString("bill_num");
            //业务报表数据
            JSONObject para = ordersManagementService.getPosOptState(tenancyId, storeId,isAfterChangeShift);
            para.put("order_code", orderCode);
            para.put("channel", chanel);
            para.put("single_time", single_time);
            para.put("bill_num", billNum);
            if(orderJson.containsKey("oper")){
                para.put("oper",orderJson.optString("oper"));
            }

            OrderState orderState = ordersDao.getOrderStateByOrderCode(tenancyId, storeId, orderCode);

            // 无:插入订单
            if (null == orderState) {

                String upload_tag="0";
                if(!"".equals(billNum) && !"null".equals(billNum)){
                    upload_tag ="1";
                }
                if(com.tzx.orders.base.constant.Constant.ORDER_STATE_NEW.equals(currentOrderState)){
                    currentOrderState=com.tzx.orders.base.constant.Constant.ORDER_STATE_TOSHOP;
                }
//                orderJson.put("single_time",single_time);
                orderJson.put("order_state",currentOrderState);
                orderJson.put("receive_time", DateUtil.getNowDateYYDDMMHHMMSS());
                orderJson.put("report_date",para.optString("report_date"));
                orderJson.put("upload_tag", upload_tag);
                json.put("order_list", orderJson);

                if (json.containsKey("order_item") && !json.optJSONArray("order_item").isEmpty()) {
                    List<JSONObject> items = json.optJSONArray("order_item");
                    List<JSONObject> itemArray = new ArrayList<JSONObject>();
                    for (JSONObject item : items) {
                        item.put("report_date", para.optString("report_date"));
                        item.put("upload_tag", upload_tag);
                        itemArray.add(item);
                    }
                    json.put("order_item", itemArray);
                }


                if (json.containsKey("order_repayment") && !json.optJSONArray("order_repayment").isEmpty()) {

                    List<JSONObject> payments = json.optJSONArray("order_repayment");
                    List<JSONObject> paymentArray = new ArrayList<JSONObject>();
                    for (JSONObject payment : payments) {
                        payment.put("report_date", para.optString("report_date"));
                        payment.put("upload_tag", upload_tag);
                        paymentArray.add(payment);
                    }
                    json.put("order_repayment", paymentArray);
                }

                TransactionStatus status = null;
                try {
                    status = getTransctionStatus(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
                    ordersDao.addOrderInfo(tenancyId, storeId, json);
                    transactionManager.commit(status);
                } catch (Exception e1) {
                    transactionManager.rollback(status);
                    throw e1;
                }

                orderState = OrderState.get(orderJson);
                orderState.setChanel(chanel);

                //更新总部状态
                JSONObject result = null;
                if(com.tzx.orders.base.constant.Constant.ORDER_STATE_TOSHOP.equals(currentOrderState)){
                    sendList.add(orderState);
                    result = OrderUtil.sendOrderState(tenancyId, storeId, Type.ORDER,Oper.update, sendList);
                }
                if(!"".equals(billNum) && !"null".equals(billNum)){//修改订单
                    orderState.setBill_num(billNum);
                    orderState.setSingle_time(single_time);
                    orderState.setActual_pay(orderJson.optDouble("actual_pay",0));
                    if(result!=null && result.containsKey("msg")){
                        if(result.get("msg").toString().contains("已取消订单")){
                            orderState.setOrder_state(com.tzx.orders.base.constant.Constant.ORDER_STATE_CANCEL);
                            orderState.setPayment_state(PAYMENT_STATE_REFUND);
                            orderState.setReceive_time_cancellation(DateUtil.getNowDateYYDDMMHHMMSS());
                            ordersDao.updateOrderInfo(tenancyId, storeId, JSONObject.fromObject(orderState));
                        }else{
                            //发送接收订单状态
                            OrderUtil.changeOrderState(tenancyId,storeId,orderState,ORDER_STATE_RECEIVE,NOTIFY_NONE);
                            orderState.setPayment_state(com.tzx.orders.base.constant.Constant.PAYMENT_STATE_COMPLETE);
                            //更新订单状态
                            OrderUtil.changeOrderState(tenancyId, storeId, orderState, ORDER_STATE_COMPLETE, NOTIFY_NONE);
                        }
                    }
                    //根据订单号查询账单数据 删除账单数据
//					JSONObject bill = ordersDao.getBillByOrderCode(tenancyId, storeId, orderCode);
//					if (bill != null && (!bill.isEmpty())) {
//						// 删除账单信息
//						ordersDao.deletePosBillByBillNum(tenancyId, storeId, bill);
//					}
                }else {//总部不存在账单
                    // 发送消息
                    String printTime = "10";//默认外卖打印范围时间内
                    if(PosPropertyUtil.getMsg("waimai.print.time")!=null && !"".equals(PosPropertyUtil.getMsg("waimai.print.time"))){
                        printTime = PosPropertyUtil.getMsg("waimai.print.time");
                    }
                    Long currentTime = System.currentTimeMillis();
                    Date singleTime = DateUtil.parseDateAll(single_time);
                    List<String> msgList = new ArrayList<String>();
                    msgList.add(orderCode);
                    JSONObject msg = new JSONObject();
                    msg.put("order_state", currentOrderState);
                    msg.put("order_code", msgList);
                    // msg.put("chanel", orderJson.optString("chanel"));
                    // msg.put("is_online_payment",
                    // orderJson.optString("is_online_payment"));
                    // Comet4jUtil.sendMessage2All(Comet4jUtil.CALLCENTER_ORDER,
                    // msg.toString());

                    // 响铃提醒
                    List<JSONObject> cometList = new ArrayList<JSONObject>();
                    cometList.add(msg);
                    Data cometData = new Data();
                    cometData.setTenancy_id(tenancyId);
                    cometData.setStore_id(storeId);
                    cometData.setType(Type.ORDER);
                    cometData.setOper(Oper.add);
                    cometData.setSuccess(true);
                    cometData.setData(cometList);
			    /*Comet4jUtil.sendMessage2All(Comet4jUtil.POS_PC, JSONObject
					.fromObject(cometData).toString());*/

                    //POS用到UDP
                    String port = new CometDataNoticeClientRunnable(cometData).getPort();
                    SendUdpToPos.sendMessagePos(JSONObject.fromObject(cometData).toString(), port);
                    logger.debug("----推送消息：" + JSONObject.fromObject(cometData).toString() + ", 端口号：" + port);
                    // 自动取单
                    if (com.tzx.orders.base.constant.Constant.ORDER_STATE_TOSHOP.equals(currentOrderState)
                            && "yes".equals(ordersManagementService.getConfig().optString("auto_take_order"))// 微信店内点餐且设置后付支持自动取单
                    ) {

                        TransactionStatus takeTs = getTransctionStatus(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
                        JSONObject printBuffer = null;
                        try {
                            //logger.info("订单转账单模式:" + ("1".equals(OrderUtil.getSysPara(tenancyId, storeId, "order_translate_bill_mode")) ? "直接转存(beta)" : "调用下单接口"));
                            //微信后付订单
                            if (Constant.WX02_CHANNEL.equals(para.optString("channel")) ){
                                //&& !"1".equals(order.getIs_online_payment())
                                // 查询门店业态,判断业态
                                JSONObject organ = ordersDao.getOrganInfo(tenancyId, storeId);
                                if (organ == null || organ.isEmpty()) {
                                    throw SystemException.getInstance(PosErrorCode.NOT_EXISTS_ORGAN);
                                }
                                if (!ordersDao.checkReportDate(tenancyId, storeId, para.optString("report_date"))) {
                                    throw SystemException.getInstance(PosErrorCode.CHECK_REPORT_DATE_ERROR);
                                }
                                // 获取订单信息
                                //String orderCode = para.optString("order_code").intern();
                                PosBill order = ordersDao.getOrderByOrdercode(tenancyId, storeId, orderCode);
                                if(order == null){
                                    throw SystemException.getInstance(PosErrorCode.NOT_EXISTS_BILL);
                                }
                                if (!"1".equals(order.getIs_online_payment())){
                                    order.setIsprint("Y");
                                    order.setReport_date(para.optString("report_date"));
                                    order.setShift_id(para.optString("shift_id"));
                                    order.setItem_menu_id(organ.optString("item_menu_id"));
                                    order.setOpt_num(para.optString("opt_num"));
                                    order.setWaiter_num(para.optString("waiter_num"));
                                    order.setPos_num(para.optString("pos_num"));
                                    order.setItem(ordersDao.getOrderItemByOrdercode(tenancyId, storeId, orderCode, para.optString("channel")));
                                    logger.info("***************微信后付下单：" + order.toString());
                                    String consigner_phone = order.getConsigner_phone(); // 会员手机号

                                    // 获取订单状态
                                    //OrderState orderState = ordersDao.getOrderStateByOrderCode(tenancyId, storeId, orderCode);
                                    if (null==orderState) {
                                        throw SystemException.getInstance(PosErrorCode.ORDER_NOT_EXISTS_ERROR);
                                    }
                                    //发送接收订单状态
                                    WxOrderUtil.changeOrderState(tenancyId,storeId,orderState,ORDER_STATE_RECEIVE,NOTIFY_SAAS);

                                    WxOrderUtil.hook(para.optString("channel"));
                                    //微信渠道的订单，后付有加菜(没有openId,暂存手机号)
                                    this.saveOrUpdateOrder(tenancyId, storeId, order.getTable_code(), consigner_phone, orderCode, order);
                                    //重置同步订单状态和结算精度
                                    WxOrderUtil.unhook();
                                    //通知微信拉单
                                   // WxOrderUtil.getOrder(tenancyId, order.getBill_num(), orderCode);
                                }else{
                                    //原拉单
                                    printBuffer = ordersManagementService.takeOrders(tenancyId, storeId, para);
                                }

                            }else {
                                //原拉单
                                printBuffer = ordersManagementService.takeOrders(tenancyId, storeId, para);
                            }


                            transactionManager.commit(takeTs);
                        } catch (SystemException e) {
                            logger.error("自动取单失败!", e);
                            transactionManager.rollback(takeTs);
                            if (e.getErrorCode() != PosErrorCode.POS_CASHIER_NOT_SIGNIN_ERROR && e.getErrorCode() != PosErrorCode.GREAT_THAN_SOLD_OUT_COUNT) {
                                //异常订单取单
                                printBuffer = ordersManagementService.takeExceptionOrders(tenancyId, storeId, para, null);
                            }
                        } catch (Exception e) {
                            logger.error("自动取单失败!", e);
                            transactionManager.rollback(takeTs);
                            //异常订单取单
                            printBuffer = ordersManagementService.takeExceptionOrders(tenancyId, storeId, para, null);
                        }
                        if (null != printBuffer) {
                            ordersManagementService.printOrderBuffer(tenancyId,storeId, printBuffer);
                        }
                    } else {
                        Comet4jUtil.sendMessage2All(Comet4jUtil.CALLCENTER_ORDER,msg.toString());
                    }
                }
            }else{
                logger.info("订单已存在:"+orderState.getOrder_code());
            }
        }

    }

    private TransactionStatus getTransctionStatus(int transactionDefinition) {
        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        def.setPropagationBehavior(transactionDefinition);
        TransactionStatus status = transactionManager.getTransaction(def);
        return status;
    }

    /**
     * 微信后付下单/加菜
     * @param tenancyId
     * @param storeId
     * @param tableCode
     * @param openId
     */
    @Override
    public void saveOrUpdateOrder(String tenancyId, int storeId, String tableCode, String openId, String orderCode, PosBill order) throws Exception {
        JSONObject printJson = new JSONObject();
        // 桌台状态
        JSONObject tableStatus = afterPaymentDao.getTableStatus(tenancyId, storeId, tableCode);
        String errorMsg = "";
        if (tableStatus != null) {

            boolean isLock = isLockByOther(order, tableStatus);
            if (!isLock) {
                // 未锁台
                String state = tableStatus.getString("state");
                if (SysDictionary.TABLE_STATE_FREE.equals(state)) {

                    // 未占用，开台
                    Data createOpenTableData = createOpenTableData(tenancyId, storeId, tableCode, order);
                    List<JSONObject> openTableList = posDishServlet.newestOpenTable(createOpenTableData,
                            new JSONObject());
                    if (openTableList == null || openTableList.size() == 0) {
                        throw new OtherSystemException(com.tzx.pos.base.Constant.CODE_NULL_DATASET, com.tzx.pos.base.Constant.OPEN_TABLE_FAILURE, null);
                    }

                    JSONObject posBillJson = JSONObject.fromObject(openTableList.get(0));
                    String billNum = posBillJson.optString("bill_num");
                    order.setBill_num(billNum);
                    if (StringUtils.isEmpty(billNum)) {
                        throw SystemException.getInstance(PosErrorCode.GET_BILLNUM_SERVICE_ERROR);
                    }
                    //锁单表插入记录
                    insertOrderDao.insertOpenId(tenancyId, storeId, billNum, openId);
                }else  if (SysDictionary.TABLE_STATE_BUSY.equals(state)) {
                    // 已占用，订单号
                    JSONObject posBill = insertOrderDao.getPosBill(tenancyId,
                            storeId, tableCode);

                    insertOrderDao.updateOpenId(posBill.optString("bill_num"), openId);
                    order.setBill_num(posBill.optString("bill_num"));
                }else {
                    //logger.error("storeId={},tablecode={} status error",storeId,tableCode);
                    throw SystemException.getInstance(PosErrorCode.TABLE_STATE_EXCEPTION);
                }

                List<String> itemIdList = new ArrayList<String>();

                // 拼装下单所需参数
                Data orderParam = createOrderDishData(tenancyId, storeId, tableCode, order, orderCode, state, itemIdList);

                logger.info("----------------微信后付下单拼装的下单参数：" + orderParam.toString());

                // 下单
                Data newestOrderDish = posDishService.newestOrderDish(orderParam, printJson);
                //标记微信订单
                printJson.put("order_type", "WX1401");
                
                // 若订单优惠信息存在则汇总为1条 存入 at 2019-06-18
                StringBuilder sqlBuf = new StringBuilder();
                sqlBuf.setLength(0);
    			sqlBuf.append("delete from pos_bill_discount where bill_num ='"+ order.getBill_num() +"'");
    			ordersDao.execute(tenancyId, sqlBuf.toString());
    			
        		sqlBuf.setLength(0);
        		sqlBuf.append("insert into pos_bill_discount(tenancy_id,store_id,report_date,pos_num,opt_num,bill_num,discount_type,discount_mode,discount_label,discount_amount,discount_count,payment_uid,last_updatetime,upload_tag) \n");
        		sqlBuf.append("SELECT tenancy_id,store_id,report_date,open_pos_num as pos_num,open_opt as opt_num,'" + order.getBill_num() + "' as bill_num,'"
        				+ SysDictionary.DISCOUNT_TYPE_ORDER + "' as discount_type,'" + SysDictionary.DISCOUNT_MODE_7
        				+ "' as discount_mode,'线上平台优惠' as discount_label,discount_amount,1 as discount_count,null as payment_uid,'" + DateUtil.getNowDateYYDDMMHHMMSS() + "' as last_updatetime,0 as upload_tag from pos_bill where bill_num ='"+ order.getBill_num() +"' and discount_amount > 0 ");
        		ordersDao.execute(tenancyId, sqlBuf.toString());
        		logger.info("微信后付下单,如有优惠则新增优惠..");
        		
                //更新OpenId
                if (newestOrderDish.getCode() == 0) {
                    //茶位费需求，微信第一次下单，茶位费的菜品项，修改default_state为Y
                    if (itemIdList.size() > 0){
                        wxInsertOrderDao.updateBillItem(tenancyId, order.getBill_num(), itemIdList);
                    }

                    boolean isBindMember = false; // 是否绑定会员信息
                    List<JSONObject> memberList = wxInsertOrderDao.getBillMember(tenancyId, order.getBill_num());
                    //如果该账单已经绑定了会员，则不用更新账单会员信息
                    if ((null == memberList || memberList.size() == 0) && CommonUtil.hasText(order.getConsigner_phone())){
                        if(Constant.WX02_CHANNEL.equals(order.getSource()) ){  //&& order.getDiscount_mode_id() != null && String.valueOf(SysDictionary.DISCOUNT_MODE_6).equals(order.getDiscount_mode_id())
                            isBindMember = true;
                        }
                    }

                    /**
                     * 是否绑定会员信息
                     */
                    if(isBindMember){
                        // 绑定会员折扣信息
                        JSONObject memberJson = new JSONObject();
                        memberJson.put("report_date", order.getReport_date());
                        memberJson.put("bill_num", order.getBill_num());
                        memberJson.put("mobil", order.getConsigner_phone());
                        memberJson.put("card_code", "");
                        memberJson.put("customer_code", order.getCustomer_id());

                        logger.info(">>>>>>>>>绑定会员折扣信息，会员参数："+memberJson.toString());
                        memberDao.insertPosBillMember(tenancyId, storeId, order.getBill_num(), DateUtil.parseDate(order.getReport_date()), SysDictionary.BILL_MEMBERCARD_HYJ01, null, null, order.getConsigner_phone(),DateUtil.currentTimestamp(),null,"",0d);
                    }

                    com.tzx.pos.bo.dto.PosBill posBill = (com.tzx.pos.bo.dto.PosBill) newestOrderDish.getData().get(0);
                    String billNum = posBill.getBill_num();
                    //微信后付下单，更改pos_bill表的order_num为WX+bill_num,不管是门店开台还是微信开台，source都为WX02
                    wxInsertOrderDao.updatePosBill(tenancyId, billNum);
                    //更改pos的订单管理的订单状态为已完成
                    wxInsertOrderDao.updateCCOrder(tenancyId, orderCode, billNum);

                    String flag = "0";
                    if (SysDictionary.TABLE_STATE_BUSY.equals(state)){
                        //根据cc_order表的订单编号查询明细
                        List<JSONObject> orderList = wxInsertOrderDao.getOrderItems(tenancyId,orderCode);
                        if (null != orderList && orderList.size() > 0){
                            for (int i = 0; i < orderList.size(); i++){
                                JSONObject orderObj = orderList.get(i);
                                //已开台且cc_order_item的tag是微信后付下单菜品标识，则这些菜品是茶位费，去掉茶位费
                                if (WECHAT_ORDER_DISH.equals(orderObj.optString("tag"))){
                                    flag = "1";
                                }
                            }
                        }
                    }

                    //通知微信拉单
                    try{
                        WxOrderUtil.getOrder(tenancyId, order.getBill_num(), orderCode, flag);
                    }catch (Exception e){
                       //不要使用 //e.printStackTrace();，如果有必要，用logger.error替代;
                    }

                    // 异步执行分单打印
                    Data data = new Data();
                    data.setTenancy_id(tenancyId);
                    data.setStore_id(storeId);
                    OrderingPrintThread orderingPrintThread = new OrderingPrintThread(printJson, data);
//                    ThreadPool.getPrintThreadPool().execute(orderingPrintThread);
                    orderingPrintThread.run();
                } else {
                    if (com.tzx.pos.base.Constant.CODE_PARAM_FAILURE == newestOrderDish.getCode()) {
                        throw new OtherSystemException(PosErrorCode.GREAT_THAN_SOLD_OUT_COUNT.getNumber(), newestOrderDish.getMsg(), null);
                    } else {
                        throw new OtherSystemException(newestOrderDish.getCode(), newestOrderDish.getMsg(), null);
                    }

                }
            } else {
                errorMsg = "桌台已锁单";
                logger.error(errorMsg);
                String lockOptNum = tableStatus.getString("lock_opt_num");
                String lockPosNum = tableStatus.optString("lock_pos_num");
                String name = posDao.getEmpNameById(lockOptNum, tenancyId, storeId);

                throw SystemException.getInstance(PosErrorCode.TABLE_ALREADY_LOCK_ERROR).set("{0}", name).set("{1}", lockPosNum);
            }
        } else {
            errorMsg = "落单失败:当前桌台不存在";
            logger.error(errorMsg);
            throw SystemException.getInstance(errorMsg, PosErrorCode.NOT_EXISTS_TABLE_CODE);
        }
    }

    /**
     * 拼装开台参数
     * @param tenancyId
     * @param storeId
     * @param tableCode
     * @param order
     * @return
     * @throws Exception
     */
    private Data createOpenTableData(String tenancyId, Integer storeId, String tableCode, PosBill order) throws Exception {
        Data data = new Data();
        data.setTenancy_id(tenancyId);
        data.setStore_id(storeId);
        data.setType(Type.ORDERING);
        data.setOper(Oper.add);
        List<JSONObject> list = new ArrayList<>();
        JSONObject object = new JSONObject();
        JSONObject optInfo = wxInsertOrderDao.getOptInfo(tenancyId, storeId);
        String optNum = "";
        String posNum = "";
        String shiftId = "";
        if (null != optInfo){
            optNum = optInfo.optString("opt_num");
            posNum = optInfo.optString("pos_num");
            shiftId = optInfo.optString("shift_id");
        }
        object.put("mode", 0);
        object.put("shift_id", shiftId);
        object.put("report_date", order.getReport_date());
        object.put("table_code", tableCode);
        object.put("pos_num", posNum);
        object.put("opt_num", optNum);
        object.put("waiter_num", optNum);
        object.put("item_menu_id", 0);
        object.put("sale_mode", SysDictionary.SALE_MODE_TS01);
        object.put("chanel", SysDictionary.CHANEL_WX02);
        object.put("guest", order.getGuest());
        object.put("preorderno", order.getOrder_num());
        object.put("copy_bill_num", "");
        object.put("remark", "");
        object.put("shop_real_amount", 0);
        object.put("platform_charge_amount", 0);
        object.put("settlement_type", "");
        object.put("discount_mode_id", 0);
        object.put("discountk_amount", 0);
        object.put("discount_rate", 0);
        list.add(object);
        data.setData(list);
        return data;
    }

    /**
     * 拼出来的data，作为传入boh原下单的处理入参
     * @param tenancyId
     * @param storeId
     * @return
     * @throws Exception
     */
    private Data createOrderDishData(String tenancyId, Integer storeId, String tableCode, PosBill order, String orderCode, String state, List<String> itemIdList) throws Exception {
        Data data = new Data();
        data.setTenancy_id(tenancyId);
        data.setStore_id(storeId);
        data.setType(Type.ORDERING);
        data.setOper(Oper.add);
        data.setSource("cc_order");
        List<JSONObject> orderList = new ArrayList<>();
        WxOrder wxOrder = createOrder(tenancyId, storeId, tableCode, order, orderCode, state, itemIdList);
        JSONObject orderJsonObject = JSONObject.fromObject(wxOrder, getAcewillOrderJsonConfig());
        orderList.add(orderJsonObject);
        data.setData(orderList);
        return data;
    }
    /**
     * 拼装账单信息
     * @param storeId
     * @param tableCode
     * @param order
     * @return
     * @throws Exception
     */
    private WxOrder createOrder(String tenancyId, Integer storeId, String tableCode, PosBill order, String orderCode, String state, List<String> itemIdList) throws Exception {

        WxOrder wxOrder = new WxOrder();
        // 账单号
        wxOrder.setBill_num(order.getBill_num());
        // 整单备注
//        wxOrder.setBill_taste(order.getRemark());
        // 服务员号
        //wxOrder.setWaiter_num(order.getWaiter_num());
        // 是否厨打
        wxOrder.setIsprint(order.getIsprint());
        //渠道
        wxOrder.setChanel(order.getSource());
        // 0:下单 1:
        wxOrder.setMode(0);
        //桌号
        wxOrder.setTable_code(tableCode);
        //班次
        //wxOrder.setShift_id(order.getShift_id());
        // 操作员编号
        //wxOrder.setOpt_num(order.getOpt_num());
        // 收款机编号
        //wxOrder.setPos_num(order.getPos_num());
        JSONObject optInfo = wxInsertOrderDao.getOptInfo(tenancyId, storeId);
        String optNum = "";
        String posNum = "";
        String shiftId = "";
        if (null != optInfo){
            optNum = optInfo.optString("opt_num");
            posNum = optInfo.optString("pos_num");
            shiftId = optInfo.optString("shift_id");
            wxOrder.setWaiter_num(optNum);
            wxOrder.setPos_num(posNum);
            wxOrder.setShift_id(shiftId);
            wxOrder.setOpt_num(optNum);
        }
        // 备注
        wxOrder.setRemark(order.getRemark());

        // 报表日期
        wxOrder.setReport_date(order.getReport_date());
        // 销售模式
        // acewillOrder.setSale_mode(sale_mode);

        List<WxOrderItem> orderItems = new ArrayList<WxOrderItem>();
        List<PosBillItem> billItemList = order.getItem();
        Iterator<PosBillItem> it = billItemList.iterator();

        List<String> list = new ArrayList<String>();
        if (SysDictionary.TABLE_STATE_BUSY.equals(state)){
            //根据cc_order表的订单编号查询明细
            List<JSONObject> orderList = wxInsertOrderDao.getOrderItems(tenancyId,orderCode);
            if (null != orderList && orderList.size() > 0){
                for (int i = 0; i < orderList.size(); i++){
                    JSONObject orderObj = orderList.get(i);
                    //已开台且cc_order_item的tag是微信后付下单菜品标识，则这些菜品是茶位费，去掉茶位费
                    if (WECHAT_ORDER_DISH.equals(orderObj.optString("tag"))){
                        list.add(orderObj.optString("item_id"));
                    }
                }
            }
        }else {
            //根据cc_order表的订单编号查询明细
            List<JSONObject> orderList = wxInsertOrderDao.getOrderItems(tenancyId,orderCode);
            if (null != orderList && orderList.size() > 0){
                for (int i = 0; i < orderList.size(); i++){
                    JSONObject orderObj = orderList.get(i);
                    //未开台且cc_order_item的tag是微信后付下单菜品标识，则这些菜品是茶位费，茶位费打标记
                    if (WECHAT_ORDER_DISH.equals(orderObj.optString("tag"))){
                        itemIdList.add(orderObj.optString("item_id"));
                    }
                }
            }
        }

        while (it.hasNext()){

            PosBillItem posBillItem = it.next();
            //判断是否茶位费对应的pos必点菜
            if (null != list && list.size() > 0 && list.contains(posBillItem.getItem_id())){
                list.remove(posBillItem.getItem_id());
                continue;
            }

            WxOrderItem wxOrderItem = new WxOrderItem();
            wxOrderItem.setAssist_item_id(posBillItem.getAssist_item_id());
            wxOrderItem.setAssist_money(posBillItem.getAssist_money());
            wxOrderItem.setAssist_num(posBillItem.getAssist_num());
            wxOrderItem.setDetails_id(posBillItem.getDetails_id());
            //wxOrderItem.setItem_amount(0.00);
            //wxOrderItem.setItem_class_id("");
            wxOrderItem.setItem_count(posBillItem.getItem_count());
            wxOrderItem.setItem_id(posBillItem.getItem_id());
            wxOrderItem.setItem_name(posBillItem.getItem_name());
            wxOrderItem.setItem_num(posBillItem.getItem_num());
            wxOrderItem.setItem_price(Double.parseDouble(posBillItem.getItem_price()));
            wxOrderItem.setItem_property(posBillItem.getItem_property());
            //wxOrderItem.setItem_class_id(posBillItem.getitem_);
            wxOrderItem.setItem_remark(posBillItem.getItem_remark());
            wxOrderItem.setItem_serial(Integer.parseInt(posBillItem.getItem_serial()));
            wxOrderItem.setItem_taste(posBillItem.getItem_taste());
            wxOrderItem.setItem_unit_name(posBillItem.getItem_unit_name());
            wxOrderItem.setMethod((List<WxOrderItemMethod>) posBillItem.getMethod());
            wxOrderItem.setSale_mode(posBillItem.getSale_mode());
            wxOrderItem.setSeat_num(posBillItem.getSeat_num());
            wxOrderItem.setSetmeal_id(posBillItem.getSetmeal_id());
            wxOrderItem.setSetmeal_rwid(posBillItem.getSetmeal_rwid());
            wxOrderItem.setUnit_id(posBillItem.getUnit_id());
            wxOrderItem.setWaitcall_tag(posBillItem.getWaitcall_tag());

            orderItems.add(wxOrderItem);
        }
        wxOrder.setItem(orderItems);

        return wxOrder;
    }
    private JsonConfig getAcewillOrderJsonConfig() {
        Map<String, Class<?>> classMap = new HashMap<>();
        classMap.put("item", WxOrderItem.class);
        classMap.put("method", WxOrderItemMethod.class);
        JsonConfig jsonConfig = new JsonConfig();
        jsonConfig.setClassMap(classMap);
        jsonConfig.setRootClass(WxOrder.class);
        return jsonConfig;
    }

    private  boolean isLockByOther(PosBill order,JSONObject tableStatus){
        boolean flag = false;
        // 查询桌台状态
        try {
            String state = tableStatus.optString("state");
            String lockOptNum = tableStatus.optString("lock_opt_num");
            String mobile = order.getConsigner_phone();
            if(SysDictionary.TABLE_STATE_BUSY.equals(state)&& CommonUtil.hasText(lockOptNum)){
                if (CommonUtil.hasText(mobile) && !mobile.equals(lockOptNum)) {
                    flag = true;
                }
            }

        } catch (Exception e) {
           //不要使用 //e.printStackTrace();，如果有必要，用logger.error替代;
        }
        return  flag;
    }
}
