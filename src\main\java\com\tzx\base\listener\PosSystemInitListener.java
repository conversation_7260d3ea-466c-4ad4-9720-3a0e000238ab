package com.tzx.base.listener;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.UnsupportedEncodingException;
import java.net.InetAddress;
import java.net.URLDecoder;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import javax.servlet.ServletContext;

import com.tzx.orders.base.controller.AdvanceOrderRunnable;
import com.tzx.orders.base.controller.OrderAssistRunnable;
import com.tzx.pos.base.controller.*;
import com.tzx.pos.version.UpgradeConsole;
import com.tzx.yd.thread.JudgeEnableYdTask;

import org.apache.commons.io.FileUtils;
import org.apache.log4j.Logger;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.postgresql.ds.PGPoolingDataSource;
import org.springframework.stereotype.Component;
import org.springframework.web.context.ServletContextAware;
import com.tzx.base.controller.CacheDataInitRunnable;
import com.tzx.clientorder.wlifeprogram.task.runable.SyncTimePriceRunable;
import com.tzx.framework.common.constant.Constant;
import com.tzx.framework.common.print.JedgePrintJobRunnable;
import com.tzx.framework.common.util.io.PropertiesUtil;
import com.tzx.framework.common.util.CommonUtil;
import com.tzx.framework.common.util.Tools;
import com.tzx.member.yazuo.task.CacheConsumRuleRunnable;
import com.tzx.member.zs.thread.JudgeEnableZsTask;
import com.tzx.pos.base.print.PrintThread;

/**
 * <AUTHOR> 启动tomcat服务加载信息
 */

@Component("initPosConfigListener")
public class PosSystemInitListener implements ServletContextAware
{
	private ServletContext	sc;

    private Logger tip = Logger.getLogger("tip");
	private Logger				logger	= Logger.getLogger(PosSystemInitListener.class);
	
	public void setServletContext(ServletContext servletContext)
	{
		this.sc = servletContext;
		// sc = sce.getServletContext();
		
		String fileSeparator = System.getProperties().getProperty("file.separator");
		String filePath = sc.getRealPath("/") + "config" + fileSeparator + "systemParam.properties";
		String versionConfigPath= sc.getRealPath("/") + "config" + fileSeparator + "version.properties";
		// //不要使用 System.out.println()，如有必要，用logger.debug()替代("xxxxxxxxxfilePathxxxxxxxx"+filePath);
		// resource = ResourceBundle.getBundle("systemParam");
		Map<String, String> propsMap = null;
		try
		{			
			propsMap = PropertiesUtil.setPropertiesFileToMap(filePath);
			sc.setAttribute("propsMap", propsMap);
			Constant.setSystemMap(propsMap, sc);
			Constant.setSystemMap(PropertiesUtil.setPropertiesFileToMap(versionConfigPath),sc);
			if(propsMap.containsKey("identification") && !"".equals(propsMap.get("identification")))
			{
				Constant.IDENTIFICATION = propsMap.get("identification");
			}
			if(propsMap.containsKey("taskresturl") && !"".equals(propsMap.get("taskresturl")))
			{
				Constant.TASKRESTURL = propsMap.get("taskresturl");
			}
			// Constant.setSystemMap(propsMap);
			
			Constant.CONTEXT_PATH = sc.getRealPath("/");
		}
		catch (FileNotFoundException e)
		{
			//e.printStackTrace();
		}

//		System.setProperty("contextPath", sc.getRealPath("/"));

		// //不要使用 System.out.println()，如有必要，用logger.debug()替代("a:"+arg0.getApplicationContext().getDisplayName());

		// set datasource type
		// MultiDatasourceContextHelper.setDatasource(Constant.getSystemMap().get("datasourcetype"));

		if (Constant.getSystemMap().get("sotreorhq").equalsIgnoreCase("boh"))
		{
//			if (Constant.getSystemMap().get("ifstart").equalsIgnoreCase("true"))
//			{
//				try
//				{
//					Thread.sleep(5000);
//				}
//				catch (InterruptedException e)
//				{
//					//e.printStackTrace();
//				}
//				//去掉mq consumer的监听，通过配置文件实现
//				PosReceiverMessageThread rmt = new PosReceiverMessageThread(sc);
//				//不要使用 System.out.println()，如有必要，用logger.debug()替代("...");
//				rmt.start();
//
//
//			}

			String port = "";	// 端口号
			String path = sc.getContextPath(); // 项目名称
			String webPathConst ="";

			String serverPath = System.getenv().get("CATALINA_HOME") + "/conf/server.xml";

			try
			{
				String xml = FileUtils.readFileToString(new File(serverPath));
				Document dom = DocumentHelper.parseText(xml);

				@SuppressWarnings("unchecked")
				List<Element> elementList = dom.getRootElement().selectNodes("/Server/Service/Connector");

				for(Element element : elementList)
				{
					if("HTTP/1.1".equals(element.attributeValue("protocol")))
					{
						port = element.attributeValue("port");
						break;
					}
				}

				/*@SuppressWarnings("unchecked")
				List<Element> elementList2 = dom.getRootElement().selectNodes("/Server/Service/Engine/Host/Context");

				for(Element element : elementList2)
				{
					path = element.attributeValue("path");
					if(Tools.hv(path))
					{
						break;
					}
				}*/
				// 获取本地服务器端IP地址，显示单据二维码使用
				InetAddress address = InetAddress.getLocalHost();
				String ip = address.getHostAddress();
				webPathConst = "http://" + ip + ":" + port + path + "/";
			}
			catch (Exception e)
			{
				logger.info("电子发票二维码生成IP地址错误：" + e);
			}

			Constant.getSystemMap().put("WEB_PATH", webPathConst);
			logger.info("电子发票二维码生成IP地址：" + webPathConst);

			// 用来启动打印服务
			logger.info("启动打印服务......");
			//ThreadManager.newInstance();
			PrintThread printTread = new PrintThread();
		    printTread.start();

			//这块以后用来启动定时刷新厨打任务
//			Timer timer = new Timer();
//			timer.schedule(new TimerPrintThread(), 35000, 5000);

			//启动门店数据自动上传服务
			logger.info("启动门店数据自动上传服务......");
			int hqdataPeriod = CommonUtil.ti(Constant.getSystemMap().get("hqdata_minite_period"));
			Executors.newScheduledThreadPool(1).scheduleWithFixedDelay(new AutoUploadDataRunnable(), 1, hqdataPeriod, TimeUnit.MINUTES);
			
			// 秒付数据自动上传服务
//			Executors.newSingleThreadScheduledExecutor().schedule(new JudgePosQuickPassTask(), 1, TimeUnit.SECONDS);
//			logger.info("秒付数据自动上传服务......");
//			Executors.newScheduledThreadPool(1).scheduleWithFixedDelay(new PosQuickPassTask(), 1, hqdataPeriod, TimeUnit.MINUTES);

			//更新本地订单表服务
//			logger.info("启动更新本地订单表服务......");
//			Executors.newScheduledThreadPool(1).scheduleWithFixedDelay(new AutoUpdateOrderDataRunnable(), 1, hqdataPeriod-9, TimeUnit.MINUTES);


			//启动门店状态自动上传服务
			logger.info("启动门店版本、状态自动上传和打印异常提醒服务......");
			int storeStatusPeriod = CommonUtil.ti(Constant.getSystemMap().get("storestatus_minite_period"));
			Executors.newScheduledThreadPool(1).scheduleWithFixedDelay(new AutoUploadStateRunnable(), 1, storeStatusPeriod, TimeUnit.MINUTES);
			
			// 打烊自动提醒服务	
//			Executors.newSingleThreadScheduledExecutor().schedule(new JudgeAutoNotifyDayEndRunnable(), 1, TimeUnit.SECONDS);
			logger.info("启动打烊自动提醒......");
			int notifyDayendPeriod = 1;
			Executors.newScheduledThreadPool(1).scheduleWithFixedDelay(new AutoNotifyDayEndRunnable(), 1, notifyDayendPeriod, TimeUnit.MINUTES);
		
			
//			if ("true".equalsIgnoreCase(Constant.getSystemMap().get("is_delivery")))
//			{
////				int hqdataPeriod = Scm.ti(Constant.getSystemMap().get("hqdata_minite_period"));
//				Executors.newScheduledThreadPool(1).scheduleAtFixedRate(new OrderAutoUploadRunnable(), 1, hqdataPeriod, TimeUnit.MINUTES);
//			}

			//厨打任务
			Executors.newSingleThreadScheduledExecutor().schedule(new JedgePrintJobRunnable(), 1, TimeUnit.SECONDS);
//			logger.info("启动厨打任务......");
//			Executors.newScheduledThreadPool(1).scheduleWithFixedDelay(new PrintJobRunnable(), 70, 1, TimeUnit.SECONDS);

			//第三方支付状态查询
			logger.info("启动第三方支付状态查询......");
			Executors.newScheduledThreadPool(1).scheduleWithFixedDelay(new ThirdPaymentOrderRunnable(), 70, 1, TimeUnit.SECONDS);

			//秒付账单状态查询
//			Executors.newSingleThreadScheduledExecutor().schedule(new JudgePosBillStatsTask(), 1, TimeUnit.SECONDS);
//			logger.info("秒付账单状态查询......");
//			Executors.newScheduledThreadPool(1).scheduleWithFixedDelay(new PosBillStatsTask(), 70, 10, TimeUnit.SECONDS);

            //升级更新组件
			logger.info("启用升级更新组件......");
            Executors.newSingleThreadScheduledExecutor().schedule(new UpgradeConsole(servletContext.getRealPath("/")),10,TimeUnit.SECONDS);

            // 初始化sys_parameter表系统参数、更新pos_data_version表中BASIC_DATA_SYS_PARAMETER的版本
            logger.info("启用系统参数更新......");
            Executors.newSingleThreadScheduledExecutor().schedule(new InitSysParameterRunnable(), 1, TimeUnit.SECONDS);

			// 缓存业务数据
			logger.info("启用缓存业务数据更新......");
			Executors.newSingleThreadScheduledExecutor().schedule(new CacheDataInitRunnable(), 1, TimeUnit.SECONDS);

			//会员操作状态查询
			logger.info("启动会员操作状态查询......");
			Executors.newScheduledThreadPool(1).scheduleWithFixedDelay(new PosCustomerOperateRunnable(), 70, 1, TimeUnit.SECONDS);

            // 加载厨打打印序号
            logger.info("加载厨打打印序号......");
            Executors.newScheduledThreadPool(1).scheduleWithFixedDelay(new LoadPrintSerialNumberRunnable(), 1, 10, TimeUnit.SECONDS);

            //请求下发基础资料(注册成功时)
            Executors.newScheduledThreadPool(1).schedule(new SynchronizeBaseDataRunnable(),10, TimeUnit.SECONDS);

            logger.info("门店新安装业务数据更新......");
			Executors.newSingleThreadScheduledExecutor().schedule(new StoreInstallDataUpdateRunnable(), 10, TimeUnit.SECONDS);

			//启动拉单辅助
            logger.info("启动门店拉单辅助......");
            Executors.newSingleThreadScheduledExecutor().schedule(new OrderAssistRunnable(), 1, TimeUnit.SECONDS);

            // 启动门店结账，但（账单处理方）是[云端处理]的账单
            Executors.newSingleThreadScheduledExecutor().schedule(new JudgePosBillToCloudBillRunnable(), 1, TimeUnit.SECONDS);
//            logger.info("启动门店结账，但（账单处理方）是[云端处理]的账单");
//            Executors.newScheduledThreadPool(1).scheduleWithFixedDelay(new PosBillToCloudBillRunnable(), 70, 2, TimeUnit.SECONDS);

            // 门店定时解锁云账单
            Executors.newSingleThreadScheduledExecutor().schedule(new JudgePosCloudLockTask(), 1, TimeUnit.SECONDS);
//          logger.info("门店定时解锁云账单");
//			Executors.newScheduledThreadPool(1).scheduleWithFixedDelay(new PosCloudLockTask(), 3, 3, TimeUnit.SECONDS);

			// 启动美团券对账验证
			logger.info("启动美团券对账验证");
		    Executors.newSingleThreadScheduledExecutor().scheduleWithFixedDelay(new ThirdCouponsRunnable(), 10,10, TimeUnit.SECONDS);
			
			// 自动查询规定时间内配送异常的外卖单
			logger.info("查询一定时间内配送异常的外卖单");
			Executors.newScheduledThreadPool(1).scheduleWithFixedDelay(new AutoWmOrderRunnable(), 1,1 ,TimeUnit.MINUTES);
            
			// 外卖预订单提醒
			logger.info("外卖预订单提醒");
			Executors.newSingleThreadScheduledExecutor().scheduleWithFixedDelay(new AdvanceOrderRunnable(), 1,1 ,TimeUnit.MINUTES);

			logger.info("微生活小程序时段特价同步......");
			Executors.newScheduledThreadPool(1).scheduleWithFixedDelay(new SyncTimePriceRunable(), 1, 10, TimeUnit.MINUTES);

			// 判断是否启动众赏会员系统
			logger.info("启动众赏会员账单上传同步......");
			Executors.newSingleThreadScheduledExecutor().schedule(new JudgeEnableZsTask(), 1, TimeUnit.SECONDS);	
			
			// 判断是否启用易定系统
			logger.info("判断是否启用易订系统对接......");
			Executors.newSingleThreadScheduledExecutor().schedule(new JudgeEnableYdTask(), 1, TimeUnit.SECONDS);
						
			// 第三方支付状态查询
			logger.info("雅座会员消费规则缓存......");
			Executors.newScheduledThreadPool(1).scheduleWithFixedDelay(new CacheConsumRuleRunnable(), 60, 60, TimeUnit.SECONDS);

			//启动门店自动日结服务
			logger.info("门店打烊自动日结......");
			Executors.newScheduledThreadPool(1).scheduleWithFixedDelay(new AutoDailyCountRunnable(), 1, 5, TimeUnit.MINUTES);

			// 清除基础资料下发产生的历史文件
			String contextPath = "";
            try {
//            	contextPath = URLDecoder.decode(System.getProperty("contextPath"),"utf-8");
            	contextPath = URLDecoder.decode(Constant.CONTEXT_PATH,"utf-8");
			} catch (UnsupportedEncodingException e) {
				logger.error(e.getMessage());
			}
			String baseFilePath = contextPath + "file";
			File file = new File(baseFilePath);
			if(file.exists() && file.isDirectory()){
				Tools.deleteDir(file);
			}	
			
		}
			
	}	
	
}
