package com.tzx.clientorder.acewillwechat.po.springjdbc.dao.imp;

import com.tzx.framework.common.util.dao.impl.GenericDaoImpl;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.clientorder.acewillwechat.po.springjdbc.dao.OrderDao;
import net.sf.json.JSONObject;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by qin-gui on 2018/3/10.
 */
@Repository(OrderDao.NAME)
public class OrderDaoImp extends GenericDaoImpl implements OrderDao {

    @Override
    public JSONObject getWLifeMember(String tenancyId, String tableCode, int storeId) throws Exception {
        StringBuffer sql = new StringBuffer();
        //查询未结账的订单，同一门店的同一桌台。未结账(OPEN为未结账)的订单只有一个
        sql.append(" select pbm.customer_code as openid,pbm.customer_name as name,pbm.mobil as mobile,pbm.card_code as cno,pbm.remark as grade,pbm.consume_before_credit as credit,pbm.consume_before_main_balance as balance")
            .append(" from pos_bill_member pbm")
            .append(" left join pos_bill pb on pbm.bill_num = pb.bill_num")
            .append(" where pb.store_id = "+ storeId +" and pb.table_code = '"+ tableCode +"' and pb.bill_property='OPEN' ");
        List<JSONObject> list = this.query4Json(tenancyId, sql.toString());
        if (null != list && list.size()>0 )
            return list.get(0);
        return null;
    }

    @Override
    public JSONObject getBillInfo(String tenancyId, String tableCode, int storeId) throws Exception {
        StringBuffer sql = new StringBuffer();
        sql.append(" select pb.bill_num as oid,pb.remark as text,pb.bill_amount as total,pb.payment_amount as cost,pb.payment_amount as discountable,pb.payment_amount as membercouponsprice,pb.guest as people,pb.service_amount as mealfee, ")
            .append(" case when t.lock_pos_num is null or t.lock_pos_num = '' then '1' else '3' end as is_locked,pb.order_num as out_order_id,")
            .append(" pb.discount_mode_id,hdc.discount_case_name,pb.discount_rate,pb.discount_amount,o.org_full_name as shop_name")
            .append(" from pos_bill pb left join pos_tablestate t on pb.store_id = t.store_id and pb.fictitious_table = t.table_code")
            .append(" left join hq_discount_case hdc on pb.discount_case_id = hdc.id ")
            .append(" left join organ o on pb.store_id = o.id ")
            .append(" where pb.store_id = "+ storeId +" and pb.fictitious_table = '"+ tableCode +"' and pb.bill_property = 'OPEN'");
        List<JSONObject> list = this.query4Json(tenancyId, sql.toString());
        if (null != list && list.size()>0 )
            return list.get(0);
        return null;
    }

    @Override
    public List<JSONObject> getSetmeal(String tenancyId, String billNum) throws Exception {
        StringBuffer sql = new StringBuffer();
        String itemProperty = SysDictionary.ITEM_PROPERTY_SETMEAL;
        sql.append(" select pbi.id,pbi.item_id as did,pbi.item_num as dishsno,pbi.item_name as name,pbi.item_unit_id as duid,item_count as number,")
            .append(" pbi.item_price as price,pbi.item_price as orgprice,case when pbi.discount_mode_id = 6 then pbi.third_price else pbi.item_price end as memberprice,")
            .append(" hii.photo1 as dishimg,'2' as type,case when pbi.item_remark='FS02' then 1 else 0 end as bgift,")
            .append(" case when hii.is_modifyquantity = 'Y' then 1 else 0 end as is_weigh,case when pbi.discount_mode_id = 6 then 1 else 0 end as bmemberprice,0 as bargainprice,")
            .append(" pbi.method_money+pbi.assist_money as aprice,hii.item_class as pkid,pbi.item_taste as remark,pbi.real_amount as realprice")
            .append(" from pos_bill_item pbi")
            .append(" left join hq_item_info hii on pbi.item_id = hii.id")
            .append(" where pbi.item_property = '"+ itemProperty +"' and pbi.bill_num = '"+ billNum +"' and (pbi.item_remark is null or (pbi.item_remark  <> 'TC01' and pbi.item_remark  <> 'CJ05')) ");
        List<JSONObject> list = this.query4Json(tenancyId, sql.toString());
        return list;
    }

    @Override
    public List<JSONObject> getSetmealDish(String tenancyId, String billNum, String type, int stemealCount, String setmealId) throws Exception {
        String is_itemgroup = "";
        //明细
        String itemProperty = SysDictionary.ITEM_PROPERTY_MEALLIST;
        String column = "";
        //如果是套餐主菜菜品的查询
        if ("main".equals(type)){
            is_itemgroup = "N";
        }else {
            //套餐必选菜
            is_itemgroup = "Y";
            column = "(pbi.method_money+pbi.assist_money)/"+ stemealCount +" as aprice,null as rpdid,";
        }
        StringBuffer sql = new StringBuffer();
        sql.append(" select bi.bill_num,pbi.item_id as id,pbi.item_num as dishsno,pbi.item_name as name,pbi.item_unit_id as duid,")
            .append(column)
            .append("pbi.item_count/"+ stemealCount +" as number")
            //.append(" 1 as selnum,cd.combo_num as maxnum, 0 as soldout")
            .append(" from pos_bill_item pbi ")
            .append(" left join pos_bill_item bi on pbi.setmeal_id = bi.item_id and pbi.setmeal_rwid = bi.item_serial")
            .append(" left join hq_item_combo_details cd on pbi.assist_item_id=cd.id")
            .append(" where pbi.item_property = '"+ itemProperty +"' and cd.is_itemgroup = '"+ is_itemgroup +"' and pbi.bill_num = '"+ billNum +"' and (pbi.item_remark is null or (pbi.item_remark  <> 'TC01' and pbi.item_remark  <> 'CJ05'))")
            .append(" and cd.iitem_id = '"+ setmealId +"' and bi.bill_num = '"+ billNum +"'");
        List<JSONObject> list = this.query4Json(tenancyId, sql.toString());
        return list;
    }

    @Override
    public List<JSONObject> getNormalitems(String tenancyId, String billNum) throws Exception {
        StringBuffer sql = new StringBuffer();
        String itemProperty = SysDictionary.ITEM_PROPERTY_SINGLE;
        sql.append(" select pbi.id,pbi.item_id as did,pbi.item_num as dishsno,pbi.item_name as name,pbi.item_unit_id as duid,pbi.item_unit_name as dishunit,pbi.item_count as number,")
                .append(" pbi.item_price as price,pbi.item_price as orgprice,case when pbi.discount_mode_id = 6 then pbi.third_price else pbi.item_price end as memberprice,")
                .append(" hii.photo1 as dishimg,'1' as type,case when pbi.item_remark='FS02' then 1 else 0 end as bgift,")
                .append(" case when hii.is_modifyquantity = 'Y' then 1 else 0 end as is_weigh,case when pbi.discount_mode_id = 6 then 1 else 0 end as bmemberprice,0 as bargainprice,")
                .append(" pbi.method_money+pbi.assist_money as aprice,hii.item_class as pkid,pbi.item_taste as remark,pbi.real_amount as realprice")
                .append(" from pos_bill_item pbi")
                .append(" left join pos_bill pb on pbi.bill_num = pb.bill_num ")
                .append(" left join hq_item_info hii on pbi.item_id = hii.id")
                .append(" where pbi.item_property = '"+ itemProperty +"' and pb.bill_num = '"+ billNum +"' and (pbi.item_remark is null or (pbi.item_remark  <> 'TC01' and pbi.item_remark  <> 'CJ05')) ");
        List<JSONObject> list = this.query4Json(tenancyId, sql.toString());
        return list;
    }

    @Override
    public List<JSONObject> getCooks(String tenancyId, int id) throws Exception {
        StringBuffer sql = new StringBuffer();
        sql.append(" select pzi.zfkw_id as cid,pzi.zfkw_name as cook from pos_bill_item pbi")
            .append(" right join pos_zfkw_item pzi on pbi.item_id = pzi.item_id and pbi.store_id = pzi.store_id")
            .append(" and pbi.bill_num = pzi.bill_num and pbi.rwid = pzi.rwid")
            .append(" where pbi.id = " + id );
        List<JSONObject> list = this.query4Json(tenancyId, sql.toString());
        return list;
    }

    @Override
    public List<JSONObject> getParam(String tenancyId) throws Exception {
        StringBuffer sql = new StringBuffer();
        sql.append(" select para_code,para_value from sys_parameter where model_name = '微生活点餐平台对接' and valid_state = '1'");
        List<JSONObject> list = this.query4Json(tenancyId, sql.toString());
        return list;
    }

    @Override
    public JSONObject getBillOrder(String tenancyId, String tableCode, int storeId) throws Exception {
        StringBuffer sql = new StringBuffer();
        sql.append(" select pb.bill_num as oid,pb.remark as text,pb.bill_amount as total,pb.payment_amount as cost,pb.payment_amount as discountable,pb.payment_amount as membercouponsprice,pb.guest as people,pb.service_amount as mealfee, ")
                .append(" case when t.lock_pos_num is null or t.lock_pos_num = '' then '1' else '3' end as is_locked,pb.order_num as out_order_id,")
                .append(" pb.discount_mode_id,hdc.discount_case_name,pb.discount_rate,pb.discount_amount,o.org_full_name as shop_name")
                .append(" from pos_bill pb left join pos_tablestate t on pb.store_id = t.store_id and pb.fictitious_table = t.table_code")
                .append(" left join hq_discount_case hdc on pb.discount_case_id = hdc.id ")
                .append(" left join organ o on pb.store_id = o.id ")
                .append(" where pb.store_id = "+ storeId +" and pb.fictitious_table = '"+ tableCode +"' and pb.bill_property = '"+ SysDictionary.BILL_PROPERTY_CLOSED +"' order by pb.id desc limit 1");
        List<JSONObject> list = this.query4Json(tenancyId, sql.toString());
        if (null != list && list.size()>0 )
            return list.get(0);
        return null;
    }

    public List<JSONObject> serviceCharge(String tenancyId, String billNum,String tableCode, int storeId) throws Exception {
        StringBuffer sql = new StringBuffer();
        sql.append("SELECT hsft.name as title, pbs.service_total as money from pos_bill_service pbs LEFT JOIN hq_service_fee_type hsft on pbs.service_id = hsft.id where pbs.tenancy_id = '");
        sql.append(tenancyId).append("' and pbs.store_id = ").append(storeId).append(" and pbs.service_id <> 0 and pbs.bill_num = '").append(billNum).append("' and pbs.table_code='").append(tableCode).append("'");
        List<JSONObject> list = this.query4Json(tenancyId, sql.toString());
        return list;
    }
}
