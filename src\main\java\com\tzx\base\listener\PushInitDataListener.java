/** 
 * @(#)CodeFormat.java    1.0   2017-10-08
 * 
 * Copyright 北京普照天星科技有限公司. All rights reserved. 
 * PROPRIETARY/CONFIDENTIAL. Use is subject to license terms. 
 */
package com.tzx.base.listener;

import javax.servlet.ServletContextEvent;
import javax.servlet.ServletContextListener;

import org.apache.log4j.Logger;
import com.tzx.base.scoket.WebSocketConnectionThread;

/**
 * servlet容器监听类:监听推送平台的连接
 * 
 * <AUTHOR> email:<EMAIL>
 * @version 1.0 2017-10-09
 * @see
 * @since JDK7.0
 * @update
 */
public class PushInitDataListener implements ServletContextListener{
	
	private static final Logger	logger	= Logger.getLogger(PushInitDataListener.class);

	@Override
	public void contextInitialized(ServletContextEvent sce) {
    	logger.info("启动与消息推送平台的连接");
		Thread t = new Thread(new WebSocketConnectionThread(),"webSocketConnectionThread");
		t.start();		
	}

	@Override
	public void contextDestroyed(ServletContextEvent sce) {
		
	}

}
