package com.tzx.clientorder.wechatprogram.common.entity.program;

public class ConsumeCouponsEntity extends MemberCouponsEntity
{
	private Double	coupon_amount;
	private Double  actual_amount;
	private Double  income_amount;
	private Double	buyer_pay_amount;
	private Double	receipt_amount;
	private String	item_id;
	private Double	item_count;
	private Double	item_price;


	public Double getCoupon_amount()
	{
		if (null == coupon_amount || coupon_amount.isNaN())
		{
			coupon_amount = 0d;
		}
		return coupon_amount;
	}

	public void setCoupon_amount(Double coupon_amount)
	{
		this.coupon_amount = coupon_amount;
	}

	public Double getBuyer_pay_amount()
	{
		if (null == buyer_pay_amount || buyer_pay_amount.isNaN())
		{
			buyer_pay_amount = 0d;
		}
		return buyer_pay_amount;
	}

	public void setBuyer_pay_amount(Double buyer_pay_amount)
	{
		this.buyer_pay_amount = buyer_pay_amount;
	}

	public Double getReceipt_amount()
	{
		if (null == receipt_amount || receipt_amount.isNaN())
		{
			receipt_amount = 0d;
		}
		return receipt_amount;
	}

	public void setReceipt_amount(Double receipt_amount)
	{
		this.receipt_amount = receipt_amount;
	}

	public String getItem_id()
	{
		return item_id;
	}

	public void setItem_id(String item_id)
	{
		this.item_id = item_id;
	}

	public Double getItem_count()
	{
		return item_count;
	}

	public void setItem_count(Double item_count)
	{
		this.item_count = item_count;
	}

	public Double getItem_price()
	{
		return item_price;
	}

	public void setItem_price(Double item_price)
	{
		this.item_price = item_price;
	}

	public Double getActual_amount() {
		return actual_amount;
	}

	public void setActual_amount(Double actual_amount) {
		this.actual_amount = actual_amount;
	}

	public Double getIncome_amount() {
		return income_amount;
	}

	public void setIncome_amount(Double income_amount) {
		this.income_amount = income_amount;
	}
}
