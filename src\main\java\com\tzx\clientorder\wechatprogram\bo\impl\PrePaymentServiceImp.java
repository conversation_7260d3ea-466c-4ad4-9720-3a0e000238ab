package com.tzx.clientorder.wechatprogram.bo.impl;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import com.google.gson.Gson;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import com.tzx.base.bo.PosCodeService;
import com.tzx.base.cache.util.CacheTableUtil;
import com.tzx.base.entity.PaymentWay;
import com.tzx.base.entity.PosBill;
import com.tzx.base.entity.PosBillDiscount;
import com.tzx.base.entity.PosBillMember;
import com.tzx.base.entity.PosBillPayment;
import com.tzx.base.entity.PosBillPaymentCoupons;
import com.tzx.base.entity.PosBillService;
import com.tzx.clientorder.wechatprogram.bo.PrePaymentService;
import com.tzx.clientorder.wechatprogram.bo.dto.prepay.PreOrderInfo;
import com.tzx.clientorder.wechatprogram.bo.dto.prepay.PrePayOrder;
import com.tzx.clientorder.wechatprogram.common.constant.PromptConstant;
import com.tzx.clientorder.wechatprogram.common.entity.pos.PosOrder;
import com.tzx.clientorder.wechatprogram.common.entity.pos.PosOrderAssist;
import com.tzx.clientorder.wechatprogram.common.entity.pos.PosOrderItem;
import com.tzx.clientorder.wechatprogram.common.entity.pos.PosOrderItemMethod;
import com.tzx.clientorder.wechatprogram.common.entity.program.ConsumeCouponsEntity;
import com.tzx.clientorder.wechatprogram.common.entity.program.OrderCookEntity;
import com.tzx.clientorder.wechatprogram.common.entity.program.OrderDiscountInfoEntity;
import com.tzx.clientorder.wechatprogram.common.entity.program.OrderDishEntity;
import com.tzx.clientorder.wechatprogram.common.entity.program.OrderInfoEntity;
import com.tzx.clientorder.wechatprogram.common.entity.program.OrderMealfeeInfoEntity;
import com.tzx.clientorder.wechatprogram.common.entity.program.OrderSetmealEntity;
import com.tzx.clientorder.wechatprogram.common.entity.program.OrderSetmealItemEntity;
import com.tzx.clientorder.wechatprogram.common.entity.program.PayInfoEntity;
import com.tzx.clientorder.wechatprogram.common.entity.program.PayMemberEntity;
import com.tzx.clientorder.wechatprogram.common.util.PromptUtil;
import com.tzx.clientorder.wechatprogram.dao.PrePaymentDao;
import com.tzx.framework.common.constant.Code;
import com.tzx.framework.common.constant.Oper;
import com.tzx.framework.common.constant.Type;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.ExceptionMessage;
import com.tzx.framework.common.exception.OtherSystemException;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.exception.WxErrorCode;
import com.tzx.framework.common.util.CommonUtil;
import com.tzx.framework.common.util.DateUtil;
import com.tzx.framework.common.util.DoubleHelper;
import com.tzx.framework.common.util.Md5Utils;
import com.tzx.framework.common.util.UUIDUtil;
import com.tzx.member.common.entity.CrmCardTradingListEntity;
import com.tzx.pos.base.Constant;
import com.tzx.pos.base.constant.FunctionCode;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.bo.PosDishService;
import com.tzx.pos.bo.PosPrintNewService;
import com.tzx.pos.bo.PosPrintService;
import com.tzx.pos.bo.payment.PosPaymentService;
import com.tzx.pos.po.springjdbc.dao.PosBillMemberDao;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import net.sf.json.JsonConfig;

@Service(PrePaymentService.NAME)
public class PrePaymentServiceImp implements PrePaymentService
{
	private static final Logger	logger	= Logger.getLogger(PrePaymentService.class);

	private static final String	MSG				= "msg";
	private static final String	DATA			= "data";

	private static final int	DEFAULT_SCALE	= 4;

	@Resource(name = PosDishService.NAME)
	private PosDishService		posDishService;

	@Resource(name = PosPaymentService.NAME)
	private PosPaymentService	posPaymentService;

	@Resource(name = PosCodeService.NAME)
	PosCodeService				codeService;

	@Resource(name = PosPrintNewService.NAME)
	private PosPrintNewService	posPrintNewService;

	@Resource(name = PosPrintService.NAME)
	private PosPrintService		posPrintService;

	@Resource(name = PrePaymentDao.NAME)
	private PrePaymentDao		paymentDao;

	@Resource(name = PosBillMemberDao.NAME)
	private PosBillMemberDao	memberDao;

	@Override
	public JSONObject preOrderToBill(String tenancyId, Integer storeId, JSONObject param, String orderChannel) throws Exception
	{
		if (false == PromptConstant.USABLE_CHANNEL.contains(orderChannel))
		{
			throw SystemException.getInstance(WxErrorCode.CHANNEL_NOT_SUPPORT_ERROR);
		}

		// 校验参数
		this.checkParamJson(param);

		PreOrderInfo preOrderBean = this.getPreOrderInfo(param);

		JSONObject billJson = paymentDao.getBillNumByOutOrderId(tenancyId, storeId, preOrderBean.getOrdernum());
		if (null != billJson && !billJson.isEmpty())
		{
			throw SystemException.getInstance(WxErrorCode.ORDER_ALREADY_EXISTS_ERROR).set("{0}", preOrderBean.getOrdernum());
		}

		if(null!=preOrderBean.getMember_info()){
            this.setMemberParam(preOrderBean, orderChannel);
        }

		//
		PosOrderAssist orderAssist = createOrderAssist(tenancyId, storeId);

		// 开台
		String billNum = openTable(tenancyId, storeId, preOrderBean.getOrder_info(), orderAssist, orderChannel);
		orderAssist.setBill_num(billNum);

		// 下单
		JSONObject orderPrintObj = createOrderDishData(tenancyId, storeId, preOrderBean.getOrder_info(), orderAssist);

		// 结账
		JSONObject billPrintObj = this.closedBill(tenancyId, storeId, preOrderBean.getMember_info(), preOrderBean.getPay_info(), orderAssist, preOrderBean.getOrder_info().getInvoice_title(), orderChannel);

		// 打印
		this.orderPrint(tenancyId, storeId, orderPrintObj, billPrintObj);

		paymentDao.savePosLog(tenancyId, storeId, orderAssist.getPos_num(), orderAssist.getOpt_num(), null, orderAssist.getShift_id(), DateUtil.parseDate(orderAssist.getReport_date()), "小程序点餐", "先付下单", "订单编号:" + preOrderBean.getOrdernum(), "账单编号:" + billNum);
		return buildSuccessData(preOrderBean.getOrdernum(), billNum);
	}

	private void checkParamJson(JSONObject param) throws Exception
	{
		String outOrderId = param.optString("ordernum");
		JSONObject orderJson = param.getJSONObject("order_info"); // 订单信息
		JSONArray payInfoJson = param.getJSONArray("pay_info"); // 支付信息

		if (!CommonUtil.hasText(outOrderId))
		{
			throw SystemException.getInstance(WxErrorCode.PARAM_NOT_NULL_ERROR).set("{0}", "ordernum");
		}

		if (null == orderJson || orderJson.isEmpty())
		{
			throw SystemException.getInstance(WxErrorCode.PARAM_NOT_NULL_ERROR).set("{0}", "order_info");
		}
		
		if (false == orderJson.containsKey("normalitems") && false == orderJson.containsKey("setmeal"))
		{
			throw SystemException.getInstance(WxErrorCode.PARAM_NOT_NULL_ERROR).set("{0}", "normalitems,setmeal");
		}

		if (null == payInfoJson || payInfoJson.isEmpty() || 0 == payInfoJson.size())
		{
			throw SystemException.getInstance(WxErrorCode.PARAM_NOT_NULL_ERROR).set("{0}", "pay_info");
		}
	}

	private PreOrderInfo getPreOrderInfo(JSONObject param) throws Exception
	{
		PreOrderInfo preOrderBean = (PreOrderInfo) JSONObject.toBean(param, getOrderInfoJsonConfig());
//        PreOrderInfo preOrderBean =new Gson().fromJson(param.toString(), PreOrderInfo.class);;


		// 实付金额=应付金额-优惠金额
		Double paymentAmount = DoubleHelper.psub(preOrderBean.getOrder_info().getTotal_amount(), preOrderBean.getOrder_info().getDiscount_money());
		if (preOrderBean.getOrder_info().getCost_amount().doubleValue() != paymentAmount.doubleValue())
		{
			throw SystemException.getInstance(WxErrorCode.PARAM_VERIFY_ERROR).set("{0}", "订单实付金额不等于应付金额-优惠金额");
		}
		
		return preOrderBean;
	}
	
	private JsonConfig getOrderInfoJsonConfig()
	{
		Map<String, Class<?>> classMap = new HashMap<>();
		classMap.put("order_info", PrePayOrder.class);
		classMap.put("discount_info", OrderDiscountInfoEntity.class);
		classMap.put("mealfee_info", OrderMealfeeInfoEntity.class);
		classMap.put("normalitems", OrderDishEntity.class);
		classMap.put("setmeal", OrderSetmealEntity.class);
		classMap.put("cooks", OrderCookEntity.class);
		classMap.put("maindish", OrderSetmealItemEntity.class);
		classMap.put("mandatory", OrderSetmealItemEntity.class);
		classMap.put("member_info", PayMemberEntity.class);
		classMap.put("pay_info", PayInfoEntity.class);
		classMap.put("coupons_info", ConsumeCouponsEntity.class);

		return PromptUtil.getJsonConfig(PreOrderInfo.class, classMap);
	}

	/**
	 * 计算消费前余额,消费前积分余额
	 * 
	 * @param preOrderBean
	 * @throws Exception
	 */
	private void setMemberParam(PreOrderInfo preOrderBean, String orderChannel) throws Exception
	{
		Double consumeAmount = 0d;
		Double consumeCredit = 0d;
		for (PayInfoEntity payInfo : preOrderBean.getPay_info())
		{
			if (PromptConstant.PAY_SOURCE_CREDIT_LIST.contains(payInfo.getSource()))
			{
				consumeCredit = payInfo.getAmount();
			}
			else if (PromptConstant.PAY_SOURCE_BALANCE_LIST.contains(payInfo.getSource()))
			{
				consumeAmount = payInfo.getAmount();
			}
		}

		Double beforeCredit = DoubleHelper.padd(DoubleHelper.psub(preOrderBean.getMember_info().getCredit(), preOrderBean.getMember_info().getReceive_credit()), consumeCredit);
		Double beforeBalance = DoubleHelper.padd(preOrderBean.getMember_info().getBalance(), consumeAmount);

		preOrderBean.getMember_info().setBeforeBalance(beforeBalance);
		preOrderBean.getMember_info().setBeforeCredit(beforeCredit);

		// 会员类型
		String customerType = null;
		if (SysDictionary.CHANEL_QIMAI.equals(orderChannel))
		{
			customerType = SysDictionary.CUSTOMER_TYPE_QIMAI;
		}
		else
		{
			customerType = CacheTableUtil.getSysParameter("CustomerType");
		}
		preOrderBean.getMember_info().setCustomerType(customerType);

		if (null == preOrderBean.getOrder_info().getMember())
		{
			preOrderBean.getOrder_info().setMember(preOrderBean.getMember_info());
		}
	}

	/**
	 * 获取报表日期,签到班次,签到人,签到机台
	 * 
	 * @param tenantId
	 * @param storeId
	 * @return
	 * @throws Exception
	 */
	private PosOrderAssist createOrderAssist(String tenantId, Integer storeId) throws Exception
	{
		PosOrderAssist orderAssist = new PosOrderAssist();
		Date reportDate = paymentDao.getReportDate(tenantId, storeId);
		orderAssist.setReport_date(DateUtil.format(reportDate));
		List<JSONObject> optStateInfoList = posPaymentService.getOptStateInfo(tenantId, storeId, orderAssist.getReport_date());
		if (optStateInfoList == null || optStateInfoList.size() == 0)
		{
			throw new OtherSystemException(Constant.CODE_NULL_DATASET, Constant.CHECK_POS_LOGIN_FAILURE, null);
		}
		JSONObject optStateInfo = optStateInfoList.get(0);
		orderAssist.setPos_num(optStateInfo.getString("pos_num"));
		orderAssist.setOpt_num(optStateInfo.getString("opt_num"));
		int shiftId = paymentDao.getShiftId(tenantId, storeId, reportDate, orderAssist.getOpt_num(), orderAssist.getPos_num());
		orderAssist.setShift_id(shiftId);
		return orderAssist;
	}

	/**
	 * 开台
	 * 
	 * @param tenantId
	 * @param storeId
	 * @param orderInfo
	 * @param orderAssist
	 * @param orderChannel
	 * @return
	 * @throws Exception
	 */
	private String openTable(String tenantId, Integer storeId, PrePayOrder orderInfo, PosOrderAssist orderAssist, String orderChannel) throws Exception
	{
		Integer shiftId = orderAssist.getShift_id();
		Date reportDate = DateUtil.parseDate(orderAssist.getReport_date());
		String optNum = orderAssist.getOpt_num();
		String posNum = orderAssist.getPos_num();
		String waiterNum = orderAssist.getOpt_num();

		String preOrderNo = orderInfo.getOrdernum();
		String tableCode = orderInfo.getTableno();
		String chanel = SysDictionary.CHANEL_WX02;
		String saleMode = SysDictionary.SALE_MODE_TS01;
		Integer itemMenuId = 0;

		Timestamp time = DateUtil.currentTimestamp();

		// POS账单编号
		String billNum = "";
		// POS流水单号
		String serialNum = "";
		// 根据小程序out_order_id 与pos_bill 中order_num 查询账单号---小程序先付跳单问题
		JSONObject jsonObject = paymentDao.getBillNumByOutOrderId(tenantId, storeId, preOrderNo);
		if (jsonObject != null)
		{
			billNum = jsonObject.optString("bill_num");
			serialNum = jsonObject.optString("serial_num");
		}
		else
		{
			try
			{
				JSONObject object = new JSONObject();
				object.put("store_id", storeId);
				object.put("busi_date", reportDate);
				billNum = codeService.getCode(tenantId, Code.POS_BILL_CODE, object);// 调用统一接口来实现
				object.put("pos_num", posNum);
				serialNum = codeService.getCode(tenantId, Code.POS_RECORD_CODE, object); // 调用统一接口来实现
			}
			catch (Exception e)
			{
				logger.info("开台：" + ExceptionMessage.getExceptionMessage(e));
				throw new SystemException(PosErrorCode.GET_BILLNUM_SERVICE_ERROR);
			}
		}

		// 服务费
		Integer serviceId = 0;
		Double serviceAmount = 0d;
		List<PosBillService> serviceList = new ArrayList<PosBillService>();
		if (null != orderInfo.getMealfee() && 0d < orderInfo.getMealfee())
		{
			serviceAmount = orderInfo.getMealfee();
			// 服务费明细
			if (null != orderInfo.getMealfee_info() && 0 < orderInfo.getMealfee_info().size())
			{
				PosBillService serviceBean = null;
				Double totalServiceAmount = 0d;
				for (OrderMealfeeInfoEntity mealfeeInfo : orderInfo.getMealfee_info())
				{
					serviceBean = new PosBillService(tenantId, storeId, billNum, tableCode, serviceId, SysDictionary.SERVICE_FEE_TYPE_JB01, SysDictionary.SERVICE_MODE_GD01, mealfeeInfo.getMoney(), 1d);
					serviceBean.setService_amount(mealfeeInfo.getMoney());
					serviceBean.setService_total(mealfeeInfo.getMoney());
					serviceList.add(serviceBean);

					totalServiceAmount = DoubleHelper.padd(totalServiceAmount, mealfeeInfo.getMoney());
				}

				if (serviceAmount != totalServiceAmount)
				{
					serviceAmount = totalServiceAmount.doubleValue();
				}
			}
			else
			{
				PosBillService serviceBean = new PosBillService(tenantId, storeId, billNum, tableCode, serviceId, SysDictionary.SERVICE_FEE_TYPE_JB01, SysDictionary.SERVICE_MODE_GD01, serviceAmount, 1d);
				serviceBean.setService_amount(serviceAmount);
				serviceBean.setService_total(serviceAmount);
				serviceList.add(serviceBean);
			}
		}

		// 折扣
		int discountModeId = 0;
		double discountkAmount = 0d;
		double discountrAmount = 0d;
		int discountRate = 100;
		List<PosBillDiscount> discountList = new ArrayList<PosBillDiscount>();
		List<PosBillMember> memberList = new ArrayList<PosBillMember>();
		List<PosBillPaymentCoupons> discountCouponList = new ArrayList<PosBillPaymentCoupons>();
		if (null != orderInfo.getDiscount_money() && 0d < orderInfo.getDiscount_money())
		{
			discountModeId = SysDictionary.DISCOUNT_MODE_7;
			discountkAmount = orderInfo.getDiscount_money();
			// 优惠明细
			if (null != orderInfo.getDiscount_info() && 0 < orderInfo.getDiscount_info().size())
			{
				PosBillDiscount discountBean = null;
				Double totalDiscountkAmount = 0d;
				for (OrderDiscountInfoEntity discountInfo : orderInfo.getDiscount_info())
				{
					discountBean = new PosBillDiscount(tenantId, storeId, reportDate, posNum, optNum, billNum, PromptUtil.getDiscountType(discountInfo.getType()), String.valueOf(discountModeId), discountInfo.getTitle(), discountInfo.getMoney(), 1d, time);
					discountList.add(discountBean);

					totalDiscountkAmount = DoubleHelper.padd(totalDiscountkAmount, discountInfo.getMoney());

					if (PromptConstant.DISCOUNT_TYPE_COUPON == discountInfo.getType() && CommonUtil.hv(discountInfo.getCoupons_info()))
					{
						String discountUid = UUIDUtil.generateGUID();
						discountBean.setPayment_uid(discountUid);
						
						discountCouponList.addAll(this.createPosBillPaymentCoupons(tenantId, storeId, billNum, reportDate, chanel, discountUid, discountInfo.getCoupons_info(),SysDictionary.COUPON_TYPE_DISCOUNT));
					}
				}

				if (discountkAmount != totalDiscountkAmount)
				{
					discountkAmount = totalDiscountkAmount.doubleValue();
				}
			}
			else
			{
				String title = "线上优惠";
				PosBillDiscount discountBean = new PosBillDiscount(tenantId, storeId, reportDate, posNum, optNum, billNum, SysDictionary.DISCOUNT_TYPE_ORDER, String.valueOf(discountModeId), title, discountkAmount, 1d, time);
				discountList.add(discountBean);
			}

			String remark = null;
			String customerCode = null;
			PosBillMember memberBean = new PosBillMember(tenantId, storeId, billNum, reportDate, SysDictionary.BILL_MEMBERCARD_CLIENTORDER, discountkAmount, 0d, orderInfo.getMember().getCno(), orderInfo.getMember().getMobile(), time, remark, customerCode, orderInfo.getMember().getName(),
					orderInfo.getMember().getBeforeCredit(), orderInfo.getMember().getCredit(), orderInfo.getMember().getBeforeBalance(), 0d, orderInfo.getMember().getBalance(), 0d);
			memberBean.setCustomer_type(orderInfo.getMember().getCustomerType());
			memberList.add(memberBean);
		}

		PosBill billBean = new PosBill();
		billBean.setTenancy_id(tenantId);
		billBean.setStore_id(storeId);
		billBean.setBill_num(billNum);
		billBean.setBatch_num(null);
		billBean.setSerial_num(serialNum);
		billBean.setReport_date(reportDate);
		billBean.setTable_code(tableCode);
		billBean.setFictitious_table(tableCode);
		billBean.setGuest(orderInfo.getPeople());
		billBean.setOpentable_time(time);
		billBean.setOpen_pos_num(posNum);
		billBean.setWaiter_num(waiterNum);
		billBean.setOpen_opt(optNum);
		billBean.setShift_id(shiftId);
		billBean.setItem_menu_id(itemMenuId);
		billBean.setService_id(serviceId);
		billBean.setService_amount(serviceAmount);
		billBean.setOrder_num(preOrderNo);
		billBean.setSubtotal(0d);
		billBean.setBill_amount(0d);
		billBean.setPayment_amount(0d);
		billBean.setDifference(0d);
		billBean.setDiscountk_amount(discountkAmount);
		billBean.setDiscountr_amount(discountrAmount);
		billBean.setMaling_amount(0d);
		billBean.setSingle_discount_amount(0d);
		billBean.setDiscount_amount(0d);
		billBean.setFree_amount(0d);
		billBean.setGivi_amount(0d);
		billBean.setMore_coupon(0d);
		billBean.setAverage_amount(0d);
		billBean.setShop_real_amount(0d);
		billBean.setTotal_fees(0d);
		billBean.setPlatform_charge_amount(0d);
		billBean.setDiscount_num(optNum);
		billBean.setDiscount_rate(discountRate);
		billBean.setDiscount_mode_id(discountModeId);
		billBean.setSale_mode(saleMode);
		billBean.setSource(chanel);
		billBean.setOrder_source(orderChannel);
		billBean.setBill_property(SysDictionary.BILL_PROPERTY_OPEN);
		billBean.setPayment_state(SysDictionary.PAYMENT_STATE_PAY_COMPLETE);

		paymentDao.savePosBill(tenantId, storeId, billBean);

		if (0 < serviceList.size())
		{
			paymentDao.savePosBillService(tenantId, storeId, serviceList);
		}

		if (0 < discountList.size())
		{
			paymentDao.savePosBillDiscount(tenantId, storeId, discountList);
		}

		if (0 < memberList.size())
		{
			memberDao.batchInsertPosBillMember(tenantId, storeId, memberList);
		}
		
		if (null != discountCouponList && 0 < discountCouponList.size())
		{
			paymentDao.savePosBillPaymentCouponsList(discountCouponList);
		}

		return billNum;
	}

	/**
	 * 下单
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param orderInfo
	 * @param orderAssist
	 * @return
	 * @throws Exception
	 */
	private JSONObject createOrderDishData(String tenancyId, Integer storeId, PrePayOrder orderInfo, PosOrderAssist orderAssist) throws Exception
	{
		// 组织下单参数
		PosOrder order = createOrder(tenancyId, storeId, orderInfo, orderAssist);
		JSONObject orderJsonObject = JSONObject.fromObject(order, getAcewillOrderJsonConfig());

		List<JSONObject> orderList = new ArrayList<>();
		orderList.add(orderJsonObject);

		Data data = new Data();
		data.setTenancy_id(tenancyId);
		data.setStore_id(storeId);
		data.setType(Type.ORDERING);
		data.setOper(Oper.add);
		data.setData(orderList);

		JSONObject orderPrintObj = new JSONObject();
		Data newestOrderDish = posDishService.newestOrderDish(data, orderPrintObj);
		if (Constant.CODE_SUCCESS != newestOrderDish.getCode())
		{
			logger.info("小程序下单失败:" + newestOrderDish.getMsg());
		}
		return orderPrintObj;
	}

	private JsonConfig getAcewillOrderJsonConfig()
	{
		Map<String, Class<?>> classMap = new HashMap<>();
		classMap.put("item", PosOrderItem.class);
		classMap.put("method", PosOrderItemMethod.class);

		return PromptUtil.getJsonConfig(PosOrder.class, classMap);
	}

	/**
	 * 组织下单菜品参数
	 * 
	 * @param tenantId
	 * @param storeId
	 * @param microLifeOrderInfo
	 * @param orderAssist
	 * @return
	 * @throws Exception
	 */
	private PosOrder createOrder(String tenantId, Integer storeId, PrePayOrder microLifeOrderInfo, PosOrderAssist orderAssist) throws Exception
	{
		PosOrder acewillOrder = new PosOrder();
		// 账单号
		acewillOrder.setBill_num(orderAssist.getBill_num());
		// 桌号
		acewillOrder.setTable_code(microLifeOrderInfo.getTableno());
		// 是否厨打
		acewillOrder.setIsprint("Y");
		// 0:下单 1:
		acewillOrder.setMode(0);
		// 报表日期
		acewillOrder.setReport_date(orderAssist.getReport_date());
		// 销售模式
		// acewillOrder.setSale_mode(sale_mode);
		// 班次id
		acewillOrder.setShift_id(orderAssist.getShift_id());
		// 收款机编号
		acewillOrder.setPos_num(orderAssist.getPos_num());
		// 操作员编号
		acewillOrder.setOpt_num(orderAssist.getOpt_num());
		// 服务员号
		acewillOrder.setWaiter_num(null);
		// 整单备注
		acewillOrder.setBill_taste(PromptUtil.formatOrderMemo(microLifeOrderInfo.getOrdermemo()));

		// 菜品明细
		List<PosOrderItem> orderItems = new ArrayList<PosOrderItem>();
		acewillOrder.setItem(orderItems);
		// 点餐序号
		Integer item_serial = 0;

		// 获取套餐档案
		Map<String, Map<String, JSONObject>> itemComboMap = this.getComboDetailMap(tenantId, microLifeOrderInfo.getSetmeal());

		// 套餐主项默认规格id不需要设置
		getSetmealUnitMap(tenantId, microLifeOrderInfo.getSetmeal());

		// 查询规格
		Map<String, String> unitNameMap = this.getUnitNameMap(tenantId, storeId, microLifeOrderInfo);

		// 套餐明细
		List<OrderSetmealEntity> setmealList = microLifeOrderInfo.getSetmeal();
		if (setmealList != null)
		{
			for (OrderSetmealEntity setmeal : setmealList)
			{
				// 点餐序号
				item_serial++;

				// 组织套餐主项数据
				String duName = unitNameMap.get(String.valueOf(setmeal.getUnitid()));
				setmeal.setUnit_name(duName);

				orderItems.add(this.createOrderItem(setmeal, item_serial));

				// 合并套餐主菜,辅菜
				List<OrderSetmealItemEntity> setmealItemList = new ArrayList<OrderSetmealItemEntity>();
				if (null != setmeal.getMaindish())
				{
					setmealItemList.addAll(setmeal.getMaindish());
				}
				if (null != setmeal.getMandatory())
				{
					setmealItemList.addAll(setmeal.getMandatory());
				}

				// 获取套餐明细信息
				Map<String, JSONObject> itemComboDetailMap = itemComboMap.get(String.valueOf(setmeal.getDishid()));

				// 组织套餐明细数据
				List<PosOrderItem> setmealOrderItemList = new ArrayList<PosOrderItem>();
				for (OrderSetmealItemEntity item : setmealItemList)
				{
					item.setNumber(setmeal.getNumber() * item.getNumber());
					item.setMallListName();// 设置套餐明细菜品名称
					String duItemName = unitNameMap.get(String.valueOf(item.getUnitid()));
					item.setUnit_name(duItemName);
					// 获取套餐明细信息
					JSONObject itemComboDetail = this.getComboDetailByDetailsId(itemComboDetailMap, item);

					PosOrderItem setmealOrderItem = this.createOrderItem(setmeal, item, item_serial, itemComboDetail);
					setmealOrderItemList.add(setmealOrderItem);
				}

				// 套餐明细排序
				orderItems.addAll(this.sortOrderSetmealItem(setmealOrderItemList));
			}
		}

		// 单品
		List<OrderDishEntity> normalitemList = microLifeOrderInfo.getNormalitems();
		if (normalitemList != null)
		{
			for (OrderDishEntity item : normalitemList)
			{
				item_serial++;
				String duName = unitNameMap.get(String.valueOf(item.getUnitid()));
				item.setUnit_name(duName);

				PosOrderItem setmealOrderItem = this.createOrderItem(item, item_serial);
				orderItems.add(setmealOrderItem);
			}
		}

		return acewillOrder;
	}

	/**
	 * 查询套餐主项默认规格
	 * 
	 * @param microLifeOrder
	 * @return
	 * @throws Exception
	 */
	private Map<String, String> getSetmealUnitMap(String tenentId, List<OrderSetmealEntity> list) throws Exception
	{
		List<String> itemIdList = new ArrayList<>();
		if (list != null)
		{
			for (OrderSetmealEntity setmeal : list)
			{
				itemIdList.add(String.valueOf(setmeal.getDishid()));
			}
		}
		// 查询默认规格
		Map<String, String> setmealUnitMap = new HashMap<String, String>();
		List<JSONObject> findItemUnit = paymentDao.findItemUnit(tenentId, itemIdList);
		if (findItemUnit != null)
		{
			for (JSONObject jsonObject : findItemUnit)
			{
				setmealUnitMap.put(jsonObject.getString("item_id"), jsonObject.getString("id"));
			}
		}

		// 设置套餐主项菜品规格
		if (list != null)
		{
			for (OrderSetmealEntity setmeal : list)
			{
				String setmealUnitId = setmealUnitMap.get(String.valueOf(setmeal.getDishid()));
				if (!StringUtils.isEmpty(setmealUnitId))
				{
					setmeal.setUnitid(Integer.valueOf(setmealUnitId));
				}
			}
		}
		return setmealUnitMap;
	}

	/**
	 * 查询规格
	 * 
	 * @param microLifeOrder
	 * @return
	 * @throws Exception
	 */
	private Map<String, String> getUnitNameMap(String tenancyId, Integer storeId, OrderInfoEntity microLifeOrderInfo) throws Exception
	{
		List<String> duids = new ArrayList<>();
		List<OrderSetmealEntity> setmealList = microLifeOrderInfo.getSetmeal();
		if (setmealList != null)
		{
			for (OrderSetmealEntity setmeal : setmealList)
			{
				if (null != setmeal.getMaindish())
				{
					for (OrderSetmealItemEntity setmealItem : setmeal.getMaindish())
					{
						duids.add(String.valueOf(setmealItem.getUnitid()));
					}
				}

				if (null != setmeal.getMandatory())
				{
					for (OrderSetmealItemEntity setmealItem : setmeal.getMandatory())
					{
						duids.add(String.valueOf(setmealItem.getUnitid()));
					}
				}

				duids.add(String.valueOf(setmeal.getUnitid()));
			}
		}

		// 单品中的规格id
		List<OrderDishEntity> normalList = microLifeOrderInfo.getNormalitems();
		if (normalList != null)
		{
			for (OrderDishEntity normal : normalList)
			{
				duids.add(String.valueOf(normal.getUnitid()));
			}
		}

		List<JSONObject> list = paymentDao.getUnitNameList(tenancyId, storeId, duids);
		Map<String, String> unitNameMap = new HashMap<>();
		if (null != list)
		{
			for (JSONObject jsonObject : list)
			{
				unitNameMap.put(jsonObject.getString("id"), jsonObject.getString("unit_name"));
			}
		}
		return unitNameMap;
	}

	/**
	 * 查询套餐明细
	 * 
	 * @param tenantId
	 * @param list
	 * @return
	 * @throws Exception
	 */
	private Map<String, Map<String, JSONObject>> getComboDetailMap(String tenantId, List<OrderSetmealEntity> list) throws Exception
	{
		List<Integer> itemIdList = new ArrayList<>();
		if (list != null)
		{
			for (OrderSetmealEntity setmeal : list)
			{
				itemIdList.add(setmeal.getDishid());
			}
		}

		// 查询套餐明细
		List<JSONObject> omboDetailsList = paymentDao.getItemComboDetails(tenantId, itemIdList);

		Map<String, Map<String, JSONObject>> comboMap = new HashMap<String, Map<String, JSONObject>>();
		if (null != omboDetailsList)
		{
			for (JSONObject comboDetail : omboDetailsList)
			{
				String iItemId = comboDetail.optString("iitem_id");// 套餐主项ID
				String isItemGroup = comboDetail.optString("is_itemgroup");
				String detailsId = comboDetail.optString("details_id");
				String itemUnitId = comboDetail.optString("item_unit_id");

				Map<String, JSONObject> comboDetailMap = null;
				if (comboMap.containsKey(iItemId))
				{
					comboDetailMap = comboMap.get(iItemId);
				}
				else
				{
					comboDetailMap = new HashMap<String, JSONObject>();
				}

				if ("Y".equals(isItemGroup))
				{
					comboDetailMap.put(detailsId, comboDetail);
				}
				else
				{
					comboDetailMap.put(detailsId + "_" + itemUnitId, comboDetail);
				}

				comboMap.put(iItemId, comboDetailMap);
			}
		}
		return comboMap;
	}

	/**
	 * 套餐明细
	 * 
	 * @param itemComboDetailMap
	 * @param item
	 * @return
	 * @throws Exception
	 */
	private JSONObject getComboDetailByDetailsId(Map<String, JSONObject> itemComboDetailMap, OrderSetmealItemEntity item) throws Exception
	{
		JSONObject itemComboDetail = null;
		if (null != itemComboDetailMap)
		{
			if (CommonUtil.hv(item.getRpdid()))
			{
				itemComboDetail = itemComboDetailMap.get(String.valueOf(item.getRpdid()));
			}
			if (null == itemComboDetail)
			{
				if (itemComboDetailMap.containsKey(item.getDishid() + "_" + item.getUnitid()))
				{
					itemComboDetail = itemComboDetailMap.get(item.getDishid() + "_" + item.getUnitid());
				}
				else
				{
					itemComboDetail = itemComboDetailMap.get(String.valueOf(item.getDishid()));
				}
			}
		}
		return itemComboDetail;
	}

	/**
	 * 单品
	 * 
	 * @param item
	 * @param item_serial
	 * @param itemComboDetail
	 * @return
	 */
	private PosOrderItem createOrderItem(OrderDishEntity item, Integer item_serial)
	{
		PosOrderItem orderItem = new PosOrderItem();
		// 辅助Assist
		orderItem.setAssist_item_id("0");
		orderItem.setAssist_num("0");

		orderItem.setItem_id(String.valueOf(item.getDishid()));
		orderItem.setItem_name(item.getDish_name());
		orderItem.setItem_num(item.getDishno());
		orderItem.setItem_price(item.getPrice());
		orderItem.setItem_count(item.getNumber());
		orderItem.setItem_property(SysDictionary.ITEM_PROPERTY_SINGLE);
		orderItem.setItem_remark(PromptUtil.getItemRemarkByActiveType(item.getActive_type()));
		// orderItem.setSale_mode(sale_mode);
		// 规格id
		orderItem.setUnit_id(String.valueOf(item.getUnitid()));
		// 规格名称
		orderItem.setItem_unit_name(item.getUnit_name());
		// 口味备注
		orderItem.setItem_taste(PromptUtil.formatOrderMemo(item.getMemo()));
		// 做法
		orderItem.setMethod(createItemMethod(item.getCooks()));
		// 点菜序号
		orderItem.setItem_serial(item_serial);
		// 座位号
		orderItem.setSeat_num(null);
		// 套餐id
		orderItem.setSetmeal_id(null);
		// 套菜点菜号
		orderItem.setSetmeal_rwid(null);
		// 等叫标记
		// orderItem.setWaitcall_tag(waitcall_tag);
		return orderItem;
	}

	/**
	 * 套餐主项
	 * 
	 * @param setmeal
	 * @param item_serial
	 * @param itemComboDetail
	 * @return
	 */
	private PosOrderItem createOrderItem(OrderSetmealEntity setmeal, Integer item_serial)
	{
		PosOrderItem orderItem = new PosOrderItem();
		// 辅助Assist
		orderItem.setAssist_item_id("0");
		orderItem.setAssist_num("0");

		orderItem.setItem_id(String.valueOf(setmeal.getDishid()));
		orderItem.setItem_name(setmeal.getDish_name());
		orderItem.setItem_num(setmeal.getDishno());
		orderItem.setItem_price(setmeal.getPrice());
		orderItem.setItem_count(setmeal.getNumber());
		orderItem.setItem_property(SysDictionary.ITEM_PROPERTY_SETMEAL);
		orderItem.setItem_remark(PromptUtil.getItemRemarkByActiveType(setmeal.getActive_type()));
		// orderItem.setSale_mode(sale_mode);
		// 规格id
		orderItem.setUnit_id(String.valueOf(setmeal.getUnitid()));
		// 规格名称
		orderItem.setItem_unit_name(setmeal.getUnit_name());
		// 口味备注
		orderItem.setItem_taste(PromptUtil.formatOrderMemo(setmeal.getMemo()));
		// 做法
		orderItem.setMethod(createItemMethod(setmeal.getCooks()));
		// 点菜序号
		orderItem.setItem_serial(item_serial);
		// 座位号
		orderItem.setSeat_num(null);
		// 套餐id
		orderItem.setSetmeal_id(String.valueOf(setmeal.getDishid()));
		// 套菜点菜号
		orderItem.setSetmeal_rwid(item_serial);
		// 等叫标记
		// orderItem.setWaitcall_tag(waitcall_tag);
		return orderItem;
	}

	/**
	 * 套餐明细
	 * 
	 * @param setmeal
	 * @param item
	 * @param item_serial
	 * @param itemComboDetail
	 * @return
	 */
	private PosOrderItem createOrderItem(OrderSetmealEntity setmeal, OrderSetmealItemEntity item, Integer item_serial, JSONObject itemComboDetail)
	{
		PosOrderItem orderItem = new PosOrderItem();
		// 辅助Assist
		if (itemComboDetail != null)
		{
			orderItem.setOrder_number(itemComboDetail.optInt("combo_order"));
			orderItem.setAssist_item_id(itemComboDetail.getString("id"));
			orderItem.setAssist_num(String.valueOf(DoubleHelper.div(setmeal.getNumber(), item.getNumber(), 0)));
		}
		else
		{
			orderItem.setAssist_item_id("0");
			orderItem.setAssist_num("0");
		}

		orderItem.setItem_id(String.valueOf(item.getDishid()));
		orderItem.setItem_name(item.getDish_name());
		orderItem.setItem_num(item.getDishno());
		// orderItem.setItem_price(item.getAprice());
		orderItem.setItem_count(item.getNumber());
		orderItem.setItem_property(SysDictionary.ITEM_PROPERTY_MEALLIST);
		orderItem.setItem_remark(PromptUtil.getItemRemarkByActiveType(setmeal.getActive_type()));
		// orderItem.setSale_mode(sale_mode);
		// 规格id
		orderItem.setUnit_id(String.valueOf(item.getUnitid()));
		// 规格名称
		orderItem.setItem_unit_name(item.getUnit_name());
		// 口味备注
		orderItem.setItem_taste(PromptUtil.formatOrderMemo(item.getMemo()));
		// 做法
		orderItem.setMethod(createItemMethod(item.getCooks()));
		// 点菜序号
		orderItem.setItem_serial(item_serial);
		// 座位号
		orderItem.setSeat_num(null);
		// 套餐id
		orderItem.setSetmeal_id(String.valueOf(setmeal.getDishid()));
		// 套菜点菜号
		orderItem.setSetmeal_rwid(item_serial);
		// 等叫标记
		// orderItem.setWaitcall_tag(waitcall_tag);
		return orderItem;
	}

	/**
	 * 套餐明细排序
	 * 
	 * @param setmealItemList
	 * @return
	 * @throws Exception
	 */
	private List<PosOrderItem> sortOrderSetmealItem(List<PosOrderItem> setmealItemList) throws Exception
	{
		if (null == setmealItemList)
		{
			return null;
		}
		Collections.sort(setmealItemList);
		return setmealItemList;
	}

	/**
	 * 做法
	 * 
	 * @param cooks
	 * @return
	 */
	private List<PosOrderItemMethod> createItemMethod(List<OrderCookEntity> cooks)
	{
		List<PosOrderItemMethod> methods = new ArrayList<>();
		if (null != cooks)
		{
			for (OrderCookEntity cook : cooks)
			{
				PosOrderItemMethod method = new PosOrderItemMethod();
				if (CommonUtil.isNullOrEmpty(cook.getCookid()))
				{
					continue;
				}
				method.setMethod_id(String.valueOf(cook.getCookid()));
				method.setMethod_name(cook.getCook_name());
				methods.add(method);
			}
		}
		return methods;
	}

	private JSONObject closedBill(String tenancyId, Integer storeId, PayMemberEntity memberInfo, List<PayInfoEntity> payInfoList, PosOrderAssist orderAssist, String isInvoice, String orderSource) throws Exception
	{
		PosBill posBill = paymentDao.getPosBillBeanByBillnum(tenancyId, storeId, orderAssist.getBill_num());

		Map<String, PaymentWay> paymentWayMap = getPaymentWayMap(tenancyId, storeId, payInfoList,orderSource);

		List<PosBillPayment> posBillPaymentList = new ArrayList<PosBillPayment>();
		List<PosBillMember> posBillMemberList = new ArrayList<PosBillMember>();
		List<PosBillPaymentCoupons> posBillPaymentCouponsList = new ArrayList<PosBillPaymentCoupons>();

		//付款明细
		Double sumPaymentAmount = 0d;
		for (PayInfoEntity payInfo : payInfoList)
		{
			if ((PromptConstant.PAY_SOURCE_WEIXIN.equals(payInfo.getSource()) || PromptConstant.PAY_SOURCE_ALIPAY.equals(payInfo.getSource())) && 0 == payInfo.getAmount() && 0d < posBill.getPayment_amount())
			{
				//
				continue;
			}

			// 付款金额合计
			sumPaymentAmount = DoubleHelper.add(sumPaymentAmount, payInfo.getAmount(), DEFAULT_SCALE);

			// 付款明细
			PaymentWay paymentWay = getPaymentWay(paymentWayMap, payInfo.getSource(), orderSource);
			PosBillPayment createPosBillPayment = createPosBillPayment(tenancyId, storeId, memberInfo, posBill, paymentWay, payInfo, orderSource);
			posBillPaymentList.add(createPosBillPayment);

			// 优惠券明细
			if (PromptConstant.PAY_SOURCE_COUPON_LIST.contains(payInfo.getSource()))
			{
				String paymentUid=Md5Utils.cmd5(JSONObject.fromObject(createPosBillPayment).toString());
				createPosBillPayment.setPayment_uid(paymentUid);
				
				posBillPaymentCouponsList.addAll(this.createPosBillPaymentCoupons(tenancyId, storeId, posBill.getBill_num(), posBill.getReport_date(), posBill.getSource(), paymentUid, payInfo.getCoupons_info(),SysDictionary.COUPON_TYPE_CODE));
			}

			// 如果有会员信息
			if (memberInfo != null && PromptConstant.PAY_SOURCE_MEMBER_LIST.contains(payInfo.getSource()))
			{
				if (PromptConstant.PAY_SOURCE_BALANCE_LIST.contains(payInfo.getSource()) && 0d < payInfo.getAmount())
				{
					PosBillMember createPosBillMember = createPosBillMember(tenancyId, storeId, posBill.getBill_num(), posBill.getReport_date(), memberInfo, payInfo, SysDictionary.BILL_MEMBERCARD_CZXF03);
					posBillMemberList.add(createPosBillMember);

					List<CrmCardTradingListEntity> crmCardTradingListEntityList = new ArrayList<>();
					CrmCardTradingListEntity crmCardTradingList = createCrmCardTradingList(tenancyId, storeId, memberInfo, posBill, payInfo);
					crmCardTradingListEntityList.add(crmCardTradingList);
					paymentDao.saveCrmCardTradingList(crmCardTradingListEntityList);
				}
				else if (PromptConstant.PAY_SOURCE_CREDIT_LIST.contains(payInfo.getSource()) && 0d < payInfo.getCredit())
				{
					PosBillMember createPosBillMember = createPosBillMember(tenancyId, storeId, posBill.getBill_num(), posBill.getReport_date(), memberInfo, payInfo, SysDictionary.BILL_MEMBERCARD_JFDX05);
					posBillMemberList.add(createPosBillMember);
				}
				else if (PromptConstant.PAY_SOURCE_COUPON_LIST.contains(payInfo.getSource()))
				{
					PosBillMember createPosBillMember = createPosBillMember(tenancyId, storeId, posBill.getBill_num(), posBill.getReport_date(), memberInfo, payInfo, SysDictionary.BILL_MEMBERCARD_YHJ04);
					posBillMemberList.add(createPosBillMember);
				}
			}
		}
		
		// 会员奖励积分以及优惠券
		if (null != memberInfo && (0d < memberInfo.getReceive_credit() || CommonUtil.hv(memberInfo.getReceive_coupons())))
		{
			PosBillMember createPosBillMember = createPosBillMember(tenancyId, storeId, posBill.getBill_num(), posBill.getReport_date(), memberInfo, null, SysDictionary.BILL_MEMBERCARD_JFZS06);
			createPosBillMember.setAmount(posBill.getPayment_amount());
			createPosBillMember.setCredit(memberInfo.getReceive_credit());
			posBillMemberList.add(createPosBillMember);
		}

		if (null != posBillPaymentList && 0 < posBillPaymentList.size())
		{
			paymentDao.savePosBillPayment(posBillPaymentList);
		}

		if (null != posBillPaymentCouponsList && 0 < posBillPaymentCouponsList.size())
		{
			paymentDao.savePosBillPaymentCouponsList(posBillPaymentCouponsList);
		}

		if (null != posBillMemberList && 0 < posBillMemberList.size())
		{
			memberDao.batchInsertPosBillMember(tenancyId, storeId, posBillMemberList);
		}

		posBill.setPos_num(orderAssist.getPos_num());
		posBill.setCashier_num(orderAssist.getOpt_num());
		posBill.setPayment_time(DateUtil.currentTimestamp());
		posBill.setPayment_source(orderSource);
		posBill.setPayment_state(SysDictionary.PAYMENT_STATE_PAY);

		String isPrintBill = "Y";
		JSONObject transferJson = new JSONObject();
		transferJson.put("isprint_bill", isPrintBill);
		transferJson.put("is_invoice", isInvoice);
		posBill.setTransfer_remark(transferJson.toString());

		paymentDao.updatePosBillByPay(tenancyId, storeId, posBill);

		JSONObject printJson = new JSONObject();
		if (DoubleHelper.sub(posBill.getPayment_amount(), sumPaymentAmount, DEFAULT_SCALE) == 0)
		{
			JSONObject resultJson = new JSONObject();
			posPaymentService.closedProgramPosBill(tenancyId, storeId, posBill.getBill_num(), DateUtil.formatDate(posBill.getReport_date()), posBill.getShift_id(), posBill.getPos_num(), posBill.getCashier_num(), isPrintBill, isInvoice, resultJson, printJson, "1", DateUtil.currentTimestamp());

			logger.info("pay.success:单号：" + posBill.getBill_num() + "=======================================================门店清台成功");
		}
		else
		{
			throw SystemException.getInstance(PosErrorCode.BILL_DIFFERENCE_MORE_ZERO_ERROR);
		}

		return printJson;
	}

	private Map<String, PaymentWay> getPaymentWayMap(String tenantId, Integer storeId, List<PayInfoEntity> payInfoList, String paymentSource) throws Exception
	{
		List<String> paymentClassList = new ArrayList<>();
		for (PayInfoEntity payInfo : payInfoList)
		{
			String paymentClass = PromptUtil.getPaymentClassBySource(payInfo.getSource(), paymentSource);
			if (!StringUtils.isEmpty(paymentClass))
			{
				paymentClassList.add(paymentClass);
			}
		}
		List<PaymentWay> paymentWayList = paymentDao.findPaymentWay(tenantId, storeId, paymentClassList);
		Map<String, PaymentWay> paymentWayMap = new HashMap<>();
		if (paymentWayList != null)
		{
			for (PaymentWay bean : paymentWayList)
			{
				paymentWayMap.put(bean.getPayment_class(), bean);
			}
		}
		return paymentWayMap;
	}

	private PaymentWay getPaymentWay(Map<String, PaymentWay> paymentWayMap, String paySource, String orderSource) throws Exception
	{
		String paymentClass = PromptUtil.getPaymentClassBySource(paySource, orderSource);
		if (paymentClass == null)
		{
			logger.error(String.format("未知的支付方式：%s", paySource));
			return null;
		}
		
		PaymentWay paymentWay = paymentWayMap.get(paymentClass);
		if (paymentWay == null)
		{
			logger.error(String.format("支付方式%s未启用", paySource));
			return null;
		}
		return paymentWay;
	}

	private PosBillPayment createPosBillPayment(String tenancyId, Integer storeId, PayMemberEntity memberInfo, PosBill posBill, PaymentWay paymentWay, PayInfoEntity payInfo, String orderSource) throws Exception
	{
		PosBillPayment posBillPayment = new PosBillPayment();
		posBillPayment.setTenancy_id(tenancyId);
		posBillPayment.setStore_id(storeId);
		posBillPayment.setBill_num(posBill.getBill_num());
		posBillPayment.setBatch_num(posBill.getBatch_num()); // 批次编号
		posBillPayment.setTable_code(posBill.getTable_code());
		posBillPayment.setReport_date(posBill.getReport_date());
		posBillPayment.setShift_id(posBill.getShift_id());
		posBillPayment.setPos_num(posBill.getPos_num());
		posBillPayment.setCashier_num(posBill.getCashier_num());// 收款员号
		if (null != paymentWay)
		{
			posBillPayment.setType(paymentWay.getPayment_class());
			posBillPayment.setJzid(paymentWay.getId());
			posBillPayment.setName(paymentWay.getPayment_name1());
			posBillPayment.setName_english(paymentWay.getPayment_name2());
		}else
		{
			String paymentClass = PromptUtil.getPaymentClassBySource(payInfo.getSource(), orderSource);
			posBillPayment.setType(paymentClass);
			logger.warn(String.format("付款方式%s不存在", paymentClass));
		}
		
		posBillPayment.setAmount(payInfo.getAmount());
		posBillPayment.setCurrency_amount(payInfo.getAmount());
		// 付款流水号
		posBillPayment.setBill_code(payInfo.getSerilno());
		posBillPayment.setCount(1);
		// 付款号码
		if (memberInfo != null && (PromptConstant.PAY_SOURCE_MEMBER_LIST.contains(payInfo.getSource())))
		{
			posBillPayment.setNumber(memberInfo.getCno());
			posBillPayment.setPhone(memberInfo.getMobile());
		}
		
		if (PromptConstant.PAY_SOURCE_COUPON_LIST.contains(payInfo.getSource()))
		{
			// 优惠券类型
			posBillPayment.setCoupon_type(SysDictionary.COUPON_TYPE_CODE);
		}
		posBillPayment.setLast_updatetime(DateUtil.currentTimestamp());
		posBillPayment.setIs_ysk("N");
		posBillPayment.setRate(1d);
		posBillPayment.setPayment_state(SysDictionary.PAYMENT_STATE_PAY_COMPLETE);

		Double couponBuyPrice = payInfo.getBuyer_pay_amount();
		Double due = payInfo.getReceipt_amount();
		Double tenancyAssume = DoubleHelper.psub(payInfo.getAmount(), due);
		Double thirdAssume = DoubleHelper.psub(due, couponBuyPrice);
		Double thirdFee = 0d;

		// 用户实付
		posBillPayment.setCoupon_buy_price(couponBuyPrice);
		// 券账单净收
		posBillPayment.setDue(due);
		// 商家优惠承担
		posBillPayment.setTenancy_assume(tenancyAssume);
		// 第三方优惠承担
		posBillPayment.setThird_assume(thirdAssume);
		// 第三方票券服务费
		posBillPayment.setThird_fee(thirdFee);
		return posBillPayment;
	}

	private PosBillMember createPosBillMember(String tenancyId, Integer storeId, String billNum, Date reportDate, PayMemberEntity wlife, PayInfoEntity payInfo, String operatType) throws Exception
	{
		PosBillMember posBillMember = new PosBillMember();
		posBillMember.setTenancy_id(tenancyId);
		posBillMember.setStore_id(storeId);
		posBillMember.setBill_num(billNum);
		posBillMember.setReport_date(reportDate);
		posBillMember.setType(operatType);
		
		if (null != payInfo)
		{
			posBillMember.setAmount(payInfo.getAmount());
			if (PromptConstant.PAY_SOURCE_CREDIT_LIST.contains(payInfo.getSource()))
			{
				posBillMember.setCredit(payInfo.getCredit());
			}
			posBillMember.setBill_code(payInfo.getSerilno());
		}
		
		posBillMember.setCard_code(wlife.getCno());
		posBillMember.setMobil(wlife.getMobile());
		posBillMember.setCustomer_code(null);
		posBillMember.setCustomer_name(wlife.getName());
		// 交易前积分
		posBillMember.setConsume_before_credit(wlife.getBeforeCredit());
		// 交易后积分
		posBillMember.setConsume_after_credit(wlife.getCredit());
		// 交易前主账户余额
		posBillMember.setConsume_before_main_balance(wlife.getBeforeBalance());
		// 交易前赠送账户余额
		posBillMember.setConsume_before_reward_balance(0d);
		// 交易后主账户余额
		posBillMember.setConsume_after_main_balance(wlife.getBalance());
		// 交易后赠送账户余额
		posBillMember.setConsume_after_reward_balance(0d);
		//会员类型
		posBillMember.setCustomer_type(wlife.getCustomerType());
		posBillMember.setLast_updatetime(DateUtil.currentTimestamp());

		return posBillMember;
	}

	private List<PosBillPaymentCoupons> createPosBillPaymentCoupons(String tenancyId, Integer storeId, String billNum,Date reportDate,String chanel,String paymentUid, List<ConsumeCouponsEntity> consumeCouponsList,int couponType)
	{
		Timestamp currentTime = DateUtil.currentTimestamp();
		List<PosBillPaymentCoupons> couponsList = new ArrayList<PosBillPaymentCoupons>();
		if (null != consumeCouponsList && 0 < consumeCouponsList.size())
		{
			for (ConsumeCouponsEntity couponInfo : consumeCouponsList)
			{
				String couponCode = couponInfo.getCoupon_code();
				if (CommonUtil.hv(couponCode))
				{
					couponCode = couponInfo.getCoupon_id();
				}

				String couponId = null;
				if (CommonUtil.isDigital(couponInfo.getCoupon_id()))
				{
					couponId = couponInfo.getCoupon_id();
				}

				Double couponCount = 1d;
				if (CommonUtil.hv(couponInfo.getCoupon_count()))
				{
					couponCount = couponInfo.getCoupon_count().doubleValue();
				}
				Double couponAmount = couponInfo.getCoupon_amount();
				if (CommonUtil.isNullOrEmpty(couponAmount) || 0d == couponAmount)
				{
					couponAmount = couponInfo.getCoupon_value();
				}

				String couponPro = PromptUtil.getCouponPro(couponInfo.getCoupon_pro());

				PosBillPaymentCoupons paymentCoupons = new PosBillPaymentCoupons(tenancyId, storeId, billNum, reportDate, paymentUid, couponCode, couponInfo.getCoupon_value(), couponInfo.getCoupon_name(), currentTime, null, null, couponId, couponAmount, couponCount, chanel,
						couponPro, couponType);

				if (SysDictionary.COUPONS_PRO_DISH.equals(couponPro))
				{
					Integer itemId = null;
					if (CommonUtil.isDigital(couponInfo.getItem_id()))
					{
						itemId = Integer.parseInt(couponInfo.getItem_id());
					}
					// 抵扣菜品
					paymentCoupons.setItem_id(itemId);
					// 菜品单价
					paymentCoupons.setPrice(null);
					// 菜品数量
					paymentCoupons.setItem_num(null);
				}

				Double tenancyAssume = DoubleHelper.psub(couponAmount, couponInfo.getReceipt_amount());
				paymentCoupons.setCoupon_buy_price(couponInfo.getBuyer_pay_amount());
				paymentCoupons.setDue(couponInfo.getReceipt_amount());
				paymentCoupons.setTenancy_assume(tenancyAssume);
				paymentCoupons.setThird_assume(0d);
				paymentCoupons.setThird_fee(0d);

				couponsList.add(paymentCoupons);
			}
		}
		return couponsList;
	}

	private CrmCardTradingListEntity createCrmCardTradingList(String tenancyId, Integer storeId, PayMemberEntity wlife, PosBill posBill, PayInfoEntity acewillPayInfo)
	{
		CrmCardTradingListEntity crmCardTradingList = new CrmCardTradingListEntity();
		Timestamp timestamp = new Timestamp(System.currentTimeMillis());
		// AcewillPayWLife wlife = microLifePayOrder.getWlife();
		// 商户id
		crmCardTradingList.setTenancy_id(tenancyId);
		// Id
		crmCardTradingList.setId(null);
		// 卡id
		crmCardTradingList.setCard_id(null);
		// 卡号 
		crmCardTradingList.setCard_code(wlife.getCno());
		// 交易单号
		crmCardTradingList.setBill_code(acewillPayInfo.getSerilno());
		// 交易渠道
		crmCardTradingList.setChanel(posBill.getSource());
		// 交易门店ID
		crmCardTradingList.setStore_id(storeId);
		// 交易日期
		crmCardTradingList.setBusiness_date(posBill.getReport_date());
		// 主账户交易金额
		crmCardTradingList.setMain_trading(acewillPayInfo.getAmount());
		// 赠送账户交易金额
		crmCardTradingList.setReward_trading(0d);
		// 交易类型
		crmCardTradingList.setOperat_type(SysDictionary.OPERAT_TYPE_XF);

		// 原主账户金额
		crmCardTradingList.setMain_original(DoubleHelper.add(wlife.getBalance(), acewillPayInfo.getAmount(), DEFAULT_SCALE));
		// 原赠送账户金额
		crmCardTradingList.setReward_original(0d);
		// 押金金额
		crmCardTradingList.setDeposit(0d);
		// 操作员
		crmCardTradingList.setOperator(null);
		// 操作时间
		crmCardTradingList.setOperate_time(timestamp);
		// 账单金额
		crmCardTradingList.setBill_money(posBill.getBill_amount());
		// 第三方账单号
		crmCardTradingList.setThird_bill_code(posBill.getBill_num());
		// 原账单号
		crmCardTradingList.setBill_code_original(null);
		// 活动ID
		crmCardTradingList.setActivity_id(null);
		// 会员ID
		crmCardTradingList.setCustomer_id(null);
		// 已撤销金额
		crmCardTradingList.setRevoked_trading(0d);
		//
		crmCardTradingList.setBatch_num(posBill.getBatch_num());
		// 最后修改时间
		crmCardTradingList.setLast_updatetime(timestamp);
		// 门店修改时间
		crmCardTradingList.setStore_updatetime(timestamp);
		// 卡类ID
		crmCardTradingList.setCard_class_id(null);
		// 会员name
		crmCardTradingList.setName(wlife.getName());
		// 会员电话
		crmCardTradingList.setMobil(wlife.getMobile());
		// 操作员ID
		crmCardTradingList.setOperator_id(null);
		// 班次ID
		crmCardTradingList.setShift_id(posBill.getShift_id());
		// 卡余额
		crmCardTradingList.setTotal_balance(wlife.getBalance());
		// 卡赠送账户余额
		crmCardTradingList.setReward_balance(0d);
		// 卡主账户余额
		crmCardTradingList.setMain_balance(wlife.getBalance());
		// 付款方式
		crmCardTradingList.setPay_type(null);

		// 销售人员ID
		crmCardTradingList.setSalesman(null);
		// 人员提成金额
		crmCardTradingList.setCommission_saler_money(0d);
		// 机构提成金额
		crmCardTradingList.setCommission_store_money(0d);
		// 可开票金额
		crmCardTradingList.setInvoice_balance(0d);

		crmCardTradingList.setIs_invoice("0");
		crmCardTradingList.setPayment_state("1");
		crmCardTradingList.setRecharge_state("1");
		crmCardTradingList.setRequest_status("02");
		crmCardTradingList.setRequest_code(null);
		crmCardTradingList.setRequest_msg(null);

		return crmCardTradingList;
	}

	private JSONObject buildSuccessData(String outOrderId, String billNum)
	{
		String mealNumber = "";
		if (null != billNum && 3 <= billNum.length())
		{
			mealNumber = billNum.substring(billNum.length() - 3);
		}
		JSONObject responseDataJson = new JSONObject();
		responseDataJson.put("identify", outOrderId);
		responseDataJson.put("meal_number", mealNumber);
		responseDataJson.put("ordermode", SysDictionary.ORDER_MODE_FIRSTPAY);

		JSONObject responseData = new JSONObject();
		responseData.put("success", 1);
		responseData.put(MSG, "SUCCESS");
		responseData.put(DATA, responseDataJson);
		return responseData;
	}

	/**打印单据
	 * @param tenancyId
	 * @param storeId
	 * @param orderPrintObj
	 * @param billPrintObj
	 */
	private void orderPrint(String tenancyId, Integer storeId,JSONObject orderPrintObj, JSONObject billPrintObj)
	{
		try
		{
			//打印点菜单,以及后厨单据
			if (orderPrintObj != null)
			{
				orderPrintObj.put("source", SysDictionary.SOURCE_PROGRAM);
                if(posPrintNewService.isNONewPrint(tenancyId, storeId))
				{   // 如果启用新的打印模式
					if (orderPrintObj.optString("mode").equalsIgnoreCase("0") && orderPrintObj.optString("is_print").equalsIgnoreCase("Y") )
					{
						if(orderPrintObj.containsKey("rwids") && orderPrintObj.getString("rwids").length() > 0)
						{
							posPrintNewService.posPrintByFunction(tenancyId, storeId, FunctionCode.ORDERING, orderPrintObj);
						}
					}
				}
				else
				{
					//打印点菜单
					if(orderPrintObj.containsKey("is_print_order") && "Y".equalsIgnoreCase(orderPrintObj.optString("is_print_order")))
					{
						List<JSONObject> printParaList = new ArrayList<JSONObject>();
						printParaList.add(orderPrintObj);

						Data printData = Data.get();
						printData.setOper(Oper.check);
						printData.setType(Type.PRINT_BILL);
						printData.setData(printParaList);

						posPrintService.printPosBill(printData, null);
					}

					//厨房打印
					if (orderPrintObj.optString("mode").equalsIgnoreCase("0"))
					{
						if (orderPrintObj.optString("is_print").equalsIgnoreCase("Y"))
						{
							List<Integer> list = posPrintService.orderChef(tenancyId, orderPrintObj.optString("bill_num"), storeId, "0");
							posPrintService.orderPrint(tenancyId, orderPrintObj.optString("bill_num"), storeId, list);
						}
					}
				}
			}
			
			//打印结账单
			if (null != billPrintObj && !billPrintObj.isEmpty())
			{
				billPrintObj.put("source", SysDictionary.SOURCE_PROGRAM);
				
				String isPrint = billPrintObj.optString("isprint");
				if ("Y".equalsIgnoreCase(isPrint))
				{
					posPrintService.getBindOptNum(tenancyId, storeId, billPrintObj);
					if (posPrintNewService.isNONewPrint(tenancyId, storeId))
					{
						// 如果启用新的打印模式
						posPrintNewService.posPrintByFunction(tenancyId, storeId, FunctionCode.BILL_PAYMENT, billPrintObj);
					}
					else
					{
						billPrintObj.put("print_code", SysDictionary.PRINT_CODE_1102);
						billPrintObj.put("print_type", "0");
						billPrintObj.put("mode", "0");

						List<JSONObject> printList = new ArrayList<JSONObject>();
						printList.add(billPrintObj);

						Data printData = new Data();
						printData.setTenancy_id(tenancyId);
						printData.setStore_id(storeId);
						printData.setType(Type.PRINT_BILL);
						printData.setData(printList);

						Data resultData = new Data();

						int printCount = billPrintObj.optInt("print_count");
						for (int i = 0; i < printCount; i++)
						{
							posPrintService.printPosBill(printData, resultData);

						}
					}
				}
			}
		}catch(Exception e1){
			logger.info(ExceptionMessage.getExceptionMessage(e1));
		}
	}
}
