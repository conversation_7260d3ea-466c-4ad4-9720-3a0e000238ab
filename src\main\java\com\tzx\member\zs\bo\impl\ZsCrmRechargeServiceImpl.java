package com.tzx.member.zs.bo.impl;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import com.tzx.base.bo.PosCodeService;
import com.tzx.framework.common.constant.Code;
import com.tzx.framework.common.constant.SysParameterCode;
import com.tzx.framework.common.constant.Type;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.CrmErrorCode;
import com.tzx.framework.common.exception.ExceptionMessage;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.CommonUtil;
import com.tzx.framework.common.util.DateUtil;
import com.tzx.framework.common.util.DoubleHelper;
import com.tzx.member.common.entity.CrmCardPaymentListEntity;
import com.tzx.member.common.entity.CrmCardTradingListEntity;
import com.tzx.member.common.entity.PosCustomerOperateListEntity;
import com.tzx.member.zs.bo.ZsCrmRechargeService;
import com.tzx.member.zs.common.constant.ZsCrmConstant;
import com.tzx.member.zs.common.util.ZSRequestUtil;
import com.tzx.member.zs.po.springjdbc.dao.ZSCustomerDao;
import com.tzx.member.zs.request.data.IData;
import com.tzx.member.zs.request.data.impl.DataAddPreCard;
import com.tzx.member.zs.request.data.impl.DataQueryActivityList;
import com.tzx.member.zs.request.data.impl.DataQueryPreCardList;
import com.tzx.pos.base.Constant;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.util.ParamUtil;
import com.tzx.pos.bo.PosPrintNewService;
import com.tzx.pos.bo.PosPrintService;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

/**
 * 众赏会员系统充值服务接口实现类
 * 
 * <AUTHOR> email:<EMAIL>
 * @version 1.0  2018-12-21
 * @see     
 * @since   JDK7.0
 * @update  
 */
@Service(ZsCrmRechargeService.NAME)
public class ZsCrmRechargeServiceImpl implements ZsCrmRechargeService {
	
	private static final Logger	logger	= Logger.getLogger(ZsCrmRechargeService.class);

	/** 3.30 查询储值方案 */
	private static final String	QUERY_ACTIVITY_LIST	= "/rs/queryPreActivityList";
	/** 3.29 储值卡充值 */
	private static final String	PRECARD_RECHARGE	= "/rs/addPreCard";
	/** 3.31 查询储值明细 */
	private static final String	QUERY_RECHARGE_LIST	= "/rs/queryPreCardList";

	private static final String	ACTIVITY_INFO		= "充值%s元,赠送金额:%s元%s.";
	private static final String	ACTIVITY_F_INFO		= ",首充赠送金额:%s元";
	
	@Resource(name = PosCodeService.NAME)
	protected PosCodeService			codeService;
	
	@Resource(name = PosPrintNewService.NAME)
	protected PosPrintNewService		posPrintNewService;

	@Resource(name = PosPrintService.NAME)
	protected PosPrintService			posPrintService;
	
	@Resource(name = ZSCustomerDao.NAME)
	protected ZSCustomerDao customerDao;
	
	@Override
	public Data queryPreActivityList(Data paramData) throws Exception
	{
		String tenancyId = paramData.getTenancy_id();
		Integer storeId = paramData.getStore_id();
		
		Data resultData = Data.get(paramData);
		
		//请求
		IData<?> data = new DataQueryActivityList();
		Data responseData = ZSRequestUtil.sendPostRequest(QUERY_ACTIVITY_LIST, data.getParams(tenancyId, storeId, new JSONObject(), customerDao));

		if(Constant.CODE_SUCCESS ==responseData.getCode())
		{
			JSONArray bodyArray = ZSRequestUtil.getResponseArray(responseData);
			
			List<JSONObject> activityList = new ArrayList<JSONObject>();
			if(null!= bodyArray)
			{
				JSONObject activityJson= null;
				JSONObject bodyJson= null;
				for(Object obj:bodyArray)
				{
					bodyJson = JSONObject.fromObject(obj);
					String title = ParamUtil.getStringValueByObject(bodyJson, "name");
					Double rechargeLimits = DoubleHelper.convertF2Y(ParamUtil.getLongValueByObject(bodyJson, "rechargeMoney"));
					Double rewardMoney = DoubleHelper.convertF2Y(ParamUtil.getLongValueByObject(bodyJson, "giveMoney"));
					Double firstRewardMoney = DoubleHelper.convertF2Y(ParamUtil.getLongValueByObject(bodyJson, "firstGiveMoney"));
					String info = null;
					if (0 < firstRewardMoney)
					{
						info = String.format(ACTIVITY_INFO, rechargeLimits, rewardMoney,String.format(ACTIVITY_F_INFO, firstRewardMoney));
					}else
					{
						info = String.format(ACTIVITY_INFO, rechargeLimits, rewardMoney,"");
					}
					
					activityJson = new JSONObject();
					activityJson.put("title", title);
					activityJson.put("recharge_limits", rechargeLimits);
					activityJson.put("reward_money", rewardMoney);
					activityJson.put("first_reward_money", firstRewardMoney);
					activityJson.put("info", info);
					activityList.add(activityJson);
				}
			}
			
			JSONObject dataJson = new JSONObject();
			dataJson.put("datalist", activityList);
			
			List<JSONObject> dataList = new ArrayList<JSONObject>();
			dataList.add(dataJson);
			
			resultData.setData(dataList);
		}
		else if(Constant.CODE_CONN_EXCEPTION ==responseData.getCode())
		{
			resultData.setCode(Constant.CODE_CONN_EXCEPTION);
			resultData.setMsg(Constant.CODE_CONN_EXCEPTION_MSG);
			resultData.setSuccess(false);
		}
		else 
		{
			resultData.setCode(Constant.CODE_AUTH_FAILURE);
			resultData.setMsg(responseData.getMsg());
			resultData.setSuccess(false);
		}
		return resultData;
	
	}
	
	@Override
	public Data memberRecharge(Data paramData, JSONObject printJson) throws Exception
	{
		String tenancyId = paramData.getTenancy_id();
		Integer storeId = paramData.getStore_id();

		List<?> paramList = paramData.getData();
		if (null == paramList || 0 == paramList.size())
		{
			throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
		}

		JSONObject paramJson = JSONObject.fromObject(paramList.get(0));
		String reportDateStr = ParamUtil.getStringValueByObject(paramJson, "report_date");
		String optNum = ParamUtil.getStringValueByObject(paramJson, "opt_num");
		Integer paymentId = ParamUtil.getIntegerValueByObject(paramJson, "payment", false, null);

		// 校验签到
		customerDao.checkKssy(DateUtil.parseDate(reportDateStr), optNum, storeId);

		// 获取单号
		String thirdBillCode = this.createThirdBillCode(tenancyId, storeId, reportDateStr);

		// 校验付款方式
		JSONObject paymentWayJson = null;
		if (null != paymentId && paymentId > 0)
		{
			paymentWayJson = customerDao.getPaymentWayByID(tenancyId, storeId, paymentId);
			if (null == paymentWayJson || paymentWayJson.isEmpty())
			{
				logger.info("支付方式id" + paymentId + "不存在");
				throw SystemException.getInstance(PosErrorCode.NOT_EXIST_JZID);
			}
		}

		// 获取会员操作记录
		PosCustomerOperateListEntity cardRecharge = this.getCustomerOperateListForRecharge(tenancyId, storeId, thirdBillCode, paramJson, paymentWayJson);
		cardRecharge.setPayment_state(SysDictionary.THIRD_PAY_STATUS_SUCCESS);

		// 请求充值
		Data resultData = this.customerCardRecharge(tenancyId, storeId, cardRecharge, printJson);
		resultData.setType(paramData.getType());
		resultData.setOper(paramData.getOper());
		return resultData;
	}
	
	/**
	 * @param tenancyId
	 * @param storeId
	 * @param reportDateStr
	 * @return
	 * @throws Exception
	 */
	private String createThirdBillCode(String tenancyId, Integer storeId, String reportDateStr) throws Exception
	{
		String billCode = "";
		try
		{
			// 生成新的账单号和流水单号
			JSONObject object = new JSONObject();
			object.element("store_id", storeId);
			object.element("busi_date", reportDateStr);
			billCode = codeService.getCode(tenancyId, Code.POS_THIRD_PAYMENT_ORDER_NUM, object);// 调用统一接口来实现
		}
		catch (Exception e)
		{
			logger.error("会员充值：" + ExceptionMessage.getExceptionMessage(e));
			throw new SystemException(PosErrorCode.GET_BILLNUM_SERVICE_ERROR);
		}
		
		if (CommonUtil.isNullOrEmpty(billCode))
		{
			throw new SystemException(PosErrorCode.GET_BILLNUM_SERVICE_ERROR);
		}
		
		return billCode;
	}
	
	/**
	 * @param tenancyId
	 * @param storeId
	 * @param paramJson
	 * @return
	 * @throws Exception
	 */
	private PosCustomerOperateListEntity getCustomerOperateListForRecharge(String tenancyId, Integer storeId,String thirdBillCode, JSONObject paramJson,JSONObject payWayJson) throws Exception
	{
		String reportDateStr = ParamUtil.getStringValueByObject(paramJson, "report_date");
		Integer shiftId = ParamUtil.getIntegerValueByObject(paramJson, "shift_id");
		String optNum = ParamUtil.getStringValueByObject(paramJson, "opt_num");
		String posNum = ParamUtil.getStringValueByObject(paramJson, "pos_num");
		String chanel = ParamUtil.getStringValueByObject(paramJson, "chanel");
		Double tradeAmount = ParamUtil.getDoubleValueByObject(paramJson, "income");
		String cardCode = ParamUtil.getStringValueByObject(paramJson, "card_code");
		Integer type = ParamUtil.getIntegerValueByObject(paramJson, "type");
		String mobile = ParamUtil.getStringValueByObject(paramJson, "mobile");
		String originalBillCode = ParamUtil.getStringValueByObject(paramJson, "bill_code_original");
		String salesman = ParamUtil.getStringValueByObject(paramJson, "salesman");
		String isInvoice = ParamUtil.getStringValueByObject(paramJson, "is_invoice");
		String payNo = ParamUtil.getStringValueByObject(paramJson, "pay_no");
		String name = ParamUtil.getStringValueByObject(paramJson, "nick_name");
		String realName = ParamUtil.getStringValueByObject(paramJson, "real_name");
		String gender = ParamUtil.getStringValueByObject(paramJson, "gender");
		String custLevelName = ParamUtil.getStringValueByObject(paramJson, "cust_level_name");
		Integer paymentId = ParamUtil.getIntegerValueByObject(paramJson, "payment");
		String paymentClass = ParamUtil.getStringValueByObject(payWayJson, "payment_class");
		String paymentName = ParamUtil.getStringValueByObject(payWayJson, "payment_name");
		Double rexchangeRate = ParamUtil.getDoubleValueByObject(payWayJson, "rate");
		if (null == rexchangeRate || 0d == rexchangeRate.doubleValue())
		{
			rexchangeRate = 1d;
		}
		
		if (CommonUtil.isNullOrEmpty(isInvoice))
		{
			isInvoice = "0";
		}
		
		double currencyAmount = DoubleHelper.pmui(tradeAmount, rexchangeRate);

		Timestamp currentTime = DateUtil.currentTimestamp();

		PosCustomerOperateListEntity cardRecharge = new PosCustomerOperateListEntity();
		cardRecharge.setTenancy_id(tenancyId);
		cardRecharge.setStore_id(storeId);
		cardRecharge.setBusiness_date(DateUtil.parseDate(reportDateStr));
		cardRecharge.setOperator_id(Integer.valueOf(optNum));
		cardRecharge.setPos_num(posNum);
		cardRecharge.setShift_id(shiftId);
		cardRecharge.setOperate_time(currentTime);
		cardRecharge.setChanel(chanel);
		cardRecharge.setService_type(SysDictionary.SERVICE_TYPE_RECHARGE);
		cardRecharge.setOperat_type(SysDictionary.OPERAT_TYPE_CZ);
		cardRecharge.setCard_code(cardCode);
		cardRecharge.setCard_class_id(type);
		cardRecharge.setMobil(mobile);
		cardRecharge.setThird_bill_code(thirdBillCode);
		cardRecharge.setBill_code_original(originalBillCode);
		cardRecharge.setTrade_amount(currencyAmount);
		cardRecharge.setBill_money(currencyAmount);
		cardRecharge.setTrade_credit(0d);
		cardRecharge.setDeposit(0d);
		cardRecharge.setSales_price(0d);
		cardRecharge.setSales_person(salesman);
		cardRecharge.setPayment_id(paymentId);
		cardRecharge.setPayment_class(paymentClass);
		cardRecharge.setIs_invoice(isInvoice);
		cardRecharge.setPayment_state(SysDictionary.THIRD_PAY_STATUS_PAYING);
		cardRecharge.setOperate_state(SysDictionary.CUSTOMER_OPERATE_STATE_WAIT);
		cardRecharge.setCancel_state(SysDictionary.CUSTOMER_OPERATE_CANCEL_STATE_N);
		cardRecharge.setRequest_status(SysDictionary.REQUEST_STATUS_ING);
		cardRecharge.setLast_query_time(currentTime);
		cardRecharge.setQuery_count(0);
		cardRecharge.setAction_type(Type.CUSTOMER_CARD_RECHARGE.name());

		JSONObject extendJson = new JSONObject();
		extendJson.put("payment_name", paymentName);
		extendJson.put("pay_money", tradeAmount);
		extendJson.put("local_currency", currencyAmount);
		extendJson.put("pay_no", payNo);
		extendJson.put("rexchange_rate", rexchangeRate);
		extendJson.put("name", name);
		extendJson.put("realName", realName);
		extendJson.put("custLevelName", custLevelName);
		extendJson.put("gender", gender);
		cardRecharge.setExtend_param(extendJson.toString());
		
		return cardRecharge;
	}
	
	/**
	 * @param tenancyId
	 * @param storeId
	 * @param cardRecharge
	 * @param printJson
	 * @return
	 * @throws Exception
	 */
	public Data customerCardRecharge(String tenancyId, Integer storeId, PosCustomerOperateListEntity cardRecharge, JSONObject printJson) throws Exception
	{
		JSONObject paramJson = new JSONObject();
		paramJson.put("third_bill_code", cardRecharge.getThird_bill_code());
		paramJson.put("card_code", cardRecharge.getCard_code());
		paramJson.put("income", cardRecharge.getTrade_amount());
		paramJson.put("type", cardRecharge.getCard_class_id());
		paramJson.put("opt_num", cardRecharge.getOperator_id());
		paramJson.put("salesman", cardRecharge.getSales_person());

		// 请求充值
		IData<?> data = new DataAddPreCard();
		Data responseData = ZSRequestUtil.sendPostRequest(PRECARD_RECHARGE, data.getParams(tenancyId, storeId, paramJson, customerDao));

		// 根据结果处理会员操作记录
		Data resultData = Data.get(responseData);
		if (Constant.CODE_SUCCESS == responseData.getCode())
		{
			JSONObject responseJson = ZSRequestUtil.getResponseObject(responseData);
			Double giveMoney = DoubleHelper.convertF2Y(ParamUtil.getLongValueByObject(responseJson, "giveMoney"));// 赠送金额
			Double cardMoney = DoubleHelper.convertF2Y(ParamUtil.getLongValueByObject(responseJson, "cardMoney"));// 储值余额
			Double cardGiveMoney = DoubleHelper.convertF2Y(ParamUtil.getLongValueByObject(responseJson, "cardGiveMoney"));// 赠送余额
			String name = ParamUtil.getStringValueByObject(responseJson, "name");// 会员名称

			JSONObject extendJson = cardRecharge.getExtendParamJson();
			extendJson.putAll(responseJson);
			extendJson.put("giveMoney", giveMoney);
			extendJson.put("cardMoney", cardMoney);
			extendJson.put("cardGiveMoney", cardGiveMoney);

			cardRecharge.setMain_trading(cardRecharge.getTrade_amount());
			cardRecharge.setReward_trading(giveMoney);
			cardRecharge.setTotal_balance(DoubleHelper.padd(cardMoney, cardGiveMoney));
			cardRecharge.setMain_balance(cardMoney);
			cardRecharge.setReward_balance(cardGiveMoney);
			if (CommonUtil.isNullOrEmpty(cardRecharge.getCustomer_name()))
			{
				cardRecharge.setCustomer_name(name);
			}

			cardRecharge.setBill_code(cardRecharge.getThird_bill_code());
			cardRecharge.setExtend_param(extendJson.toString());
			cardRecharge.setOperate_state(SysDictionary.CUSTOMER_OPERATE_STATE_SUCCESS);
			cardRecharge.setRequest_status(SysDictionary.REQUEST_STATUS_COMPLETE);
			cardRecharge.setFinish_time(DateUtil.currentTimestamp());

			// 如果充值成功记录会员流水
			this.addCrmCardTradingListForRecharge(tenancyId, storeId, cardRecharge);

			// 生成打印参数
			if (null == printJson)
			{
				printJson = new JSONObject();
			}
			printJson.putAll(this.getPrintForRecharge(tenancyId, storeId, cardRecharge));

			JSONObject resultJson = this.getResultJsonForRecharge(tenancyId, storeId, cardRecharge);

			List<JSONObject> resultList = new ArrayList<JSONObject>();
			resultList.add(resultJson);
			resultData.setData(resultList);
		}
		else if (Constant.CODE_CONN_EXCEPTION == responseData.getCode())
		{
			cardRecharge.setOperate_state(SysDictionary.CUSTOMER_OPERATE_STATE_PROCESS);
			cardRecharge.setRequest_status(SysDictionary.REQUEST_STATUS_FAILURE);
			cardRecharge.setRequest_code(String.valueOf(responseData.getCode()));
			cardRecharge.setRequest_msg(responseData.getMsg());

			List<JSONObject> resultList = new ArrayList<JSONObject>();
			resultList.add(paramJson);
			resultData.setData(resultList);
			resultData.setCode(responseData.getCode());
			resultData.setMsg(responseData.getMsg());
		}
		else
		{
			cardRecharge.setOperate_state(SysDictionary.CUSTOMER_OPERATE_STATE_FAIL);
			cardRecharge.setRequest_status(SysDictionary.REQUEST_STATUS_COMPLETE);
			cardRecharge.setRequest_code(String.valueOf(responseData.getCode()));
			cardRecharge.setRequest_msg(responseData.getMsg());
			cardRecharge.setFinish_time(DateUtil.currentTimestamp());

			resultData.setCode(responseData.getCode());
			resultData.setMsg(responseData.getMsg());
		}

		if (CommonUtil.hv(cardRecharge.getId()))
		{
			customerDao.updatePosCustomerOperateList(tenancyId, storeId, cardRecharge);
		}
		else
		{
			customerDao.insertPosCustomerOperateList(tenancyId, storeId, cardRecharge);
		}
		return resultData;
	}
	
	private boolean addCrmCardTradingListForRecharge(String tenancyId, Integer storeId, PosCustomerOperateListEntity cardRecharge) throws Exception
	{
		JSONObject extendJson = cardRecharge.getExtendParamJson();
		
		CrmCardTradingListEntity cardTradingList = new CrmCardTradingListEntity();
		cardTradingList.setThird_bill_code(cardRecharge.getThird_bill_code());
		cardTradingList.setBill_code(cardRecharge.getBill_code());
		cardTradingList.setCard_id(null);
		cardTradingList.setCard_code(cardRecharge.getCard_code());
		cardTradingList.setCard_class_id(null);
		cardTradingList.setName(cardRecharge.getCustomer_name());
		cardTradingList.setMobil(cardRecharge.getMobil());
		cardTradingList.setBusiness_date(cardRecharge.getBusiness_date());
		cardTradingList.setShift_id(cardRecharge.getShift_id());
		cardTradingList.setChanel(cardRecharge.getChanel());
		cardTradingList.setOperat_type(cardRecharge.getOperat_type());
		cardTradingList.setBill_money(cardRecharge.getTrade_amount());
		cardTradingList.setMain_trading(cardRecharge.getMain_trading());
		cardTradingList.setReward_trading(cardRecharge.getReward_trading());
		cardTradingList.setRevoked_trading(0d);
		cardTradingList.setMain_original(0d);
		cardTradingList.setReward_original(0d);
		cardTradingList.setTotal_balance(cardRecharge.getTotal_balance());
		cardTradingList.setReward_balance(cardRecharge.getReward_balance());
		cardTradingList.setMain_balance(cardRecharge.getMain_balance());
		cardTradingList.setDeposit(cardRecharge.getDeposit());
		cardTradingList.setActivity_id(null);
		cardTradingList.setCustomer_id(null);
		cardTradingList.setSalesman(cardRecharge.getSales_person());
		cardTradingList.setOperate_time(cardRecharge.getOperate_time());
		cardTradingList.setLast_updatetime(cardRecharge.getFinish_time());
		cardTradingList.setStore_updatetime(cardRecharge.getFinish_time());
		cardTradingList.setOperator_id(cardRecharge.getOperator_id());
		String operator = customerDao.getEmpNameById(String.valueOf(cardRecharge.getOperator_id()), tenancyId, storeId);
		cardTradingList.setOperator(operator);
		cardTradingList.setIs_invoice(cardRecharge.getIs_invoice());
		cardTradingList.setInvoice_balance(0d);
		if("1".equals(cardRecharge.getIs_invoice()))
		{
			cardTradingList.setInvoice_balance(cardRecharge.getTrade_amount());
		}
		cardTradingList.setPayment_state(cardRecharge.getPayment_state());
		cardTradingList.setRecharge_state(cardRecharge.getOperate_state());
		cardTradingList.setRequest_status(cardRecharge.getRequest_status());
		cardTradingList.setBill_code_original(null);
		cardTradingList.setBatch_num(null);
		cardTradingList.setCommission_saler_money(0d);
		cardTradingList.setCommission_store_money(0d);
		cardTradingList.setPay_type(null);

		customerDao.insertCrmCardTradingList(tenancyId, storeId, cardTradingList);

		CrmCardPaymentListEntity cardPaymentList = new CrmCardPaymentListEntity();
		cardPaymentList.setBill_code(cardRecharge.getBill_code());
		cardPaymentList.setThird_bill_code(cardRecharge.getThird_bill_code());
		cardPaymentList.setCard_id(cardRecharge.getCard_class_id());
		cardPaymentList.setPayment_id(cardRecharge.getPayment_id());
		cardPaymentList.setPay_money(ParamUtil.getDoubleValueByObject(extendJson, "pay_money"));
		cardPaymentList.setLocal_currency(ParamUtil.getDoubleValueByObject(extendJson, "local_currency"));
		cardPaymentList.setPay_no(ParamUtil.getStringValueByObject(extendJson, "pay_no"));
		cardPaymentList.setRexchange_rate(ParamUtil.getDoubleValueByObject(extendJson, "rexchange_rate"));
		cardPaymentList.setStore_updatetime(cardRecharge.getFinish_time());
		cardPaymentList.setLast_updatetime(cardRecharge.getFinish_time());

		customerDao.insertCrmCardPaymentList(tenancyId, storeId, cardPaymentList);
		return true;
	}
	
	private JSONObject getPrintForRecharge(String tenancyId, Integer storeId, PosCustomerOperateListEntity cardRecharge) throws Exception
	{
		String strUrlContent = "";
		String strUrlPath = "";
		
		JSONObject extendJson = cardRecharge.getExtendParamJson();
		
		String cardClassName ="会员卡";
		if(ZsCrmConstant.CARD_TYPE_PRE==cardRecharge.getCard_class_id().intValue())
		{
			cardClassName = "储值卡";
		}
		String levelName=ParamUtil.getStringValueByObject(extendJson, "custLevelName");
		if(CommonUtil.isNullOrEmpty(levelName))
		{
			levelName = "无星级";
		}
				
		// 打印入参
		JSONObject printJson = new JSONObject();
		printJson.put("print_code", SysDictionary.PRINT_CODE_1008);
		printJson.put("mode", "1");
		printJson.put("pos_num", cardRecharge.getPos_num());
		
		printJson.put("bill_code", cardRecharge.getThird_bill_code());
		printJson.put("income", cardRecharge.getTrade_amount());
		printJson.put("bill_money", cardRecharge.getTrade_amount());
		printJson.put("pay_money", ParamUtil.getDoubleValueByObject(extendJson, "pay_money"));
		printJson.put("paymenttypename", ParamUtil.getStringValueByObject(extendJson, "payment_name"));
		printJson.put("level_name", levelName);
		printJson.put("card_class_name", cardClassName);
		printJson.put("card_code", cardRecharge.getCard_code());
		printJson.put("name", cardRecharge.getCustomer_name());
		printJson.put("mobil", cardRecharge.getMobil());
		printJson.put("main_trading", cardRecharge.getMain_trading());
		printJson.put("reward_trading", cardRecharge.getReward_trading());
		printJson.put("main_balance", cardRecharge.getMain_balance());
		printJson.put("reward_balance", cardRecharge.getReward_balance());
		printJson.put("reward_credit", 0d);
		printJson.put("useful_credit", ParamUtil.getDoubleValueByObject(extendJson, "creVal"));
		printJson.put("total_main", 0d);
		printJson.put("total_reward", 0d);
		printJson.put("credit", 0d);
		String operator = customerDao.getEmpNameById(String.valueOf(cardRecharge.getOperator_id()), tenancyId, storeId);
		printJson.put("operator", operator);
		printJson.put("channel", cardRecharge.getChanel());
		printJson.put("updatetime", DateUtil.format(cardRecharge.getFinish_time()));
		printJson.put("reward_coupon", "[]");
		printJson.put("url_content", strUrlContent);
		printJson.put("url_path", strUrlPath);
		printJson.put("is_invoice", cardRecharge.getIs_invoice());
		printJson.put("invoice_amount", 0d);
		if("1".equals(cardRecharge.getIs_invoice()))
		{
			printJson.put("invoice_amount", cardRecharge.getTrade_amount());
		}
		return printJson;
	}

	private JSONObject getResultJsonForRecharge(String tenancyId, Integer storeId, PosCustomerOperateListEntity cardRecharge) throws Exception
	{
		JSONObject extendJson = cardRecharge.getExtendParamJson();
		
		JSONObject resultJson = new JSONObject();
		resultJson.put("level_name", ParamUtil.getStringValueByObject(extendJson, "custLevelName"));
		resultJson.put("card_code", cardRecharge.getCard_code());
		resultJson.put("mobil", cardRecharge.getMobil());
		resultJson.put("name", cardRecharge.getCustomer_name());
		resultJson.put("third_bill_code", cardRecharge.getThird_bill_code());
		resultJson.put("total_balance", cardRecharge.getTotal_balance());
		resultJson.put("main_balance", cardRecharge.getMain_balance());
		resultJson.put("reward_balance", cardRecharge.getReward_balance());
		resultJson.put("main_original", 0d);
		resultJson.put("reward_original", 0d);
		resultJson.put("income", cardRecharge.getTrade_amount());
		resultJson.put("main_trading", cardRecharge.getMain_trading());
		resultJson.put("reward_trading", cardRecharge.getReward_trading());
		resultJson.put("useful_credit", ParamUtil.getDoubleValueByObject(extendJson, "creVal"));
		resultJson.put("reward_credit", 0);
		resultJson.put("bill_code", cardRecharge.getBill_code());
		resultJson.put("operator_id", cardRecharge.getOperator_id());
		resultJson.put("last_updatetime", DateUtil.format(cardRecharge.getFinish_time()));
		return resultJson;
	}
	
	@Override
	public Data queryMemberRecharge(Data paramData, JSONObject printJson) throws Exception
	{
		String tenancyId = paramData.getTenancy_id();
		Integer storeId = paramData.getStore_id();

		List<?> paramList = paramData.getData();
		if (null == paramList || 0 == paramList.size())
		{
			throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
		}

		JSONObject paramJson = JSONObject.fromObject(paramList.get(0));
		String thirdBillCode = ParamUtil.getStringValueByObject(paramJson, "third_bill_code");
		
		Data resultData = null;
		//查询本地充值记录
		PosCustomerOperateListEntity cardRecharge = customerDao.getCustomerOperateListByThirdBillCode(tenancyId, storeId, thirdBillCode, SysDictionary.OPERAT_TYPE_CZ);
		if (null != cardRecharge)
		{
			if (SysDictionary.CUSTOMER_OPERATE_CANCEL_STATE_Y.equals(cardRecharge.getCancel_state()))
			{
				SystemException se = SystemException.getInstance(CrmErrorCode.CUSTOMER_RECHARGE_ALREADY_CANCLE_ERROR);
				resultData = Data.get(paramData);
				resultData.setCode(se.getErrorCode().getNumber());
				resultData.setMsg(se.getErrorMsg());
				return resultData;
			}
			else if (SysDictionary.CUSTOMER_OPERATE_STATE_SUCCESS.equals(cardRecharge.getOperate_state()))
			{
				resultData = Data.get(paramData);
				JSONObject resultJson = this.getResultJsonForRecharge(tenancyId, storeId, cardRecharge);
				
				List<JSONObject> resultList = new ArrayList<JSONObject>();
				resultList.add(resultJson);
				resultData.setData(resultList);
				return resultData;
			}
			else if (SysDictionary.CUSTOMER_OPERATE_STATE_FAIL.equals(cardRecharge.getOperate_state()))
			{
				resultData = Data.get(paramData);
				resultData.setCode(CrmErrorCode.CUSTOMER_RECHARGE_FAIL_ERROR.getNumber());
				resultData.setMsg(cardRecharge.getRequest_msg());
				return resultData;
			}
		}
		else
		{
			SystemException se = SystemException.getInstance(CrmErrorCode.NOT_EXISTS_CUSTOMER_OPERATE_ERROR);
			resultData = Data.get(paramData);
			resultData.setCode(se.getErrorCode().getNumber());
			resultData.setMsg(se.getErrorMsg());
			return resultData;
		}
		
		//查询会员操作记录
		IData<?> data = new DataQueryPreCardList();
		Data responseData = ZSRequestUtil.sendPostRequest(QUERY_RECHARGE_LIST, data.getParams(tenancyId, storeId, paramJson, customerDao));
		
		//判断是否充值成功
		JSONArray preList = null;
		if (Constant.CODE_SUCCESS == responseData.getCode())
		{
			JSONObject responseJson = ZSRequestUtil.getResponseObject(responseData);
			if (null != responseJson && responseJson.containsKey("preList"))
			{
				preList = responseJson.optJSONArray("preList");
			}
		}
		
		resultData = Data.get(responseData);
		if (Constant.CODE_SUCCESS == responseData.getCode() && null != preList && 0 < preList.size())
		{
			//充值成功
			Double payMoney=0d;
			Double giveMoney=0d;
			Double cardMoney=0d;
			Double cardGiveMoney=0d;
			for (Object obj : preList)
			{
				JSONObject responseJson = JSONObject.fromObject(obj);
				Double cardMoneyTemp = DoubleHelper.convertF2Y(ParamUtil.getLongValueByObject(responseJson, "cardMoney"));
				if (cardMoney.doubleValue() < cardMoneyTemp.doubleValue())
				{
					cardMoney = cardMoneyTemp.doubleValue();
				}
				Double cardGiveMoneyTemp = DoubleHelper.convertF2Y(ParamUtil.getLongValueByObject(responseJson, "cardGiveMoney"));
				if (cardGiveMoney.doubleValue() < cardGiveMoneyTemp.doubleValue())
				{
					cardGiveMoney = cardGiveMoneyTemp.doubleValue();
				}
				
				if (ZsCrmConstant.OPERATE_TYPE_RECHARGE.equals(responseJson.optString("operateType")))
				{
					payMoney = DoubleHelper.convertF2Y(ParamUtil.getLongValueByObject(responseJson, "money"));
//					cardMoney = DoubleHelper.convertF2Y(ParamUtil.getLongValueByObject(responseJson, "cardMoney"));
				}
				else if (ZsCrmConstant.OPERATE_TYPE_GIVE.equals(responseJson.optString("operateType")))
				{
					giveMoney = DoubleHelper.convertF2Y(ParamUtil.getLongValueByObject(responseJson, "money"));
//					cardGiveMoney = DoubleHelper.convertF2Y(ParamUtil.getLongValueByObject(responseJson, "cardMoney"));
				}
			}

			JSONObject extendJson = cardRecharge.getExtendParamJson();
			extendJson.put("giveMoney", giveMoney);
			extendJson.put("cardMoney", cardMoney);
			extendJson.put("cardGiveMoney", cardGiveMoney);
//			extendJson.put("creVal", 0);
//			extendJson.put("custLevel", 0);
//			extendJson.put("name", "");

			cardRecharge.setMain_trading(payMoney);
			cardRecharge.setReward_trading(giveMoney);
			cardRecharge.setTotal_balance(DoubleHelper.padd(cardMoney, cardGiveMoney));
			cardRecharge.setMain_balance(cardMoney);
			cardRecharge.setReward_balance(cardGiveMoney);
			
			cardRecharge.setBill_code(cardRecharge.getThird_bill_code());
			cardRecharge.setExtend_param(extendJson.toString());
			cardRecharge.setOperate_state(SysDictionary.CUSTOMER_OPERATE_STATE_SUCCESS);
			cardRecharge.setRequest_status(SysDictionary.REQUEST_STATUS_COMPLETE);
			cardRecharge.setFinish_time(DateUtil.currentTimestamp());

			// 如果充值成功记录会员流水
			this.addCrmCardTradingListForRecharge(tenancyId, storeId, cardRecharge);
			
			// 生成打印参数
			if (null == printJson)
			{
				printJson = new JSONObject();
			}
			printJson.putAll(this.getPrintForRecharge(tenancyId, storeId, cardRecharge));
			
			JSONObject resultJson = this.getResultJsonForRecharge(tenancyId, storeId, cardRecharge);
			
			List<JSONObject> resultList = new ArrayList<JSONObject>();
			resultList.add(resultJson);
			resultData.setData(resultList);
		}
		else if (Constant.CODE_CONN_EXCEPTION == responseData.getCode())
		{
			//查询请求异常
			cardRecharge.setOperate_state(SysDictionary.CUSTOMER_OPERATE_STATE_PROCESS);
			cardRecharge.setRequest_status(SysDictionary.REQUEST_STATUS_FAILURE);
			cardRecharge.setRequest_code(String.valueOf(responseData.getCode()));
			cardRecharge.setRequest_msg(responseData.getMsg());

			resultData.setCode(responseData.getCode());
			resultData.setMsg(responseData.getMsg());
		}
		else
		{
			//查询失败
			cardRecharge.setOperate_state(SysDictionary.CUSTOMER_OPERATE_STATE_FAIL);
			cardRecharge.setRequest_status(SysDictionary.REQUEST_STATUS_COMPLETE);
			cardRecharge.setRequest_code(String.valueOf(responseData.getCode()));
			cardRecharge.setRequest_msg(responseData.getMsg());
			cardRecharge.setFinish_time(DateUtil.currentTimestamp());
			
			resultData.setCode(responseData.getCode());
			resultData.setMsg(responseData.getMsg());
		}
		
		customerDao.updatePosCustomerOperateList(tenancyId, storeId, cardRecharge);
		
		return resultData;
	}

	@Override
	public void printForCustomerCardRecharge(String tenancyId, Integer storeId, String printCode, JSONObject printJson)
	{
		try
		{
			if (posPrintNewService.isNONewPrint(tenancyId, storeId))
			{// 如果启用新的打印模式
				posPrintNewService.posPrintByMode(tenancyId, storeId, printCode, printJson);
			}
			else
			{
				List<JSONObject> printList = new ArrayList<>();
				printList.add(printJson);

				Data paramData = Data.get(tenancyId, storeId, Constant.CODE_SUCCESS);
				paramData.setData(printList);

				Data result = new Data();

				String printCountStr = customerDao.getSysParameter(tenancyId, storeId, SysParameterCode.MEMBER_RECEIPT_COUNT_KEY);
				if (CommonUtil.isNullOrEmpty(printCountStr))
				{
					printCountStr = "0";
				}
				Integer printCount = Integer.parseInt(printCountStr);
				if (printCount <= 0)
				{
					printCount = 1;
				}

				for (int i = 0; i < printCount; i++)
				{
					posPrintService.printPosBill(paramData, result);
				}
			}
		}
		catch (Exception e)
		{
			logger.info("充值打印失败", e);
		}
	}
}
