package com.tzx.base.cache.util;

import java.util.Map;

import net.sf.json.JSONObject;

import com.tzx.base.cache.CacheManager;
import com.tzx.base.constant.CacheTableConstant;

public class CacheTableUtil
{
	/**
	 * @param keyValue
	 * @param cacheTableName
	 * @return
	 * @throws Exception
	 */
	public static JSONObject getCacheDataForJson(String keyValue, CacheTableConstant cacheTableName) throws Exception
	{
		if (cacheTableName.getIsToMap())
		{
			Map<?, ?> cacheMap = CacheManager.getCacheMap(cacheTableName.name());
			return getCacheDataForJson(keyValue, cacheMap);
		}
		return null;
	}

	/**
	 * @param keyValue
	 * @param cacheMap
	 * @return
	 * @throws Exception
	 */
	public static JSONObject getCacheDataForJson(Object keyValue, Map<?, ?> cacheMap) throws Exception
	{
		if (null != cacheMap && cacheMap.containsKey(keyValue))
		{
			return JSONObject.fromObject(cacheMap.get(keyValue));
		}
		return null;
	}

	/**
	 * @param paraCode
	 * @return
	 * @throws Exception
	 */
	public static String getSysParameter(String paraCode) throws Exception
	{
		Map<?, ?> sysParameterMap = CacheManager.getCacheMap(CacheTableConstant.SYS_PARAMETER.name());
		return getSysParameter(paraCode, sysParameterMap);
	}

	/**
	 * @param paraCode
	 * @param sysParaMap
	 * @return
	 * @throws Exception
	 */
	public static String getSysParameter(String paraCode, Map<?, ?> sysParaMap) throws Exception
	{
		if (sysParaMap == null || !sysParaMap.containsKey(paraCode))
		{
			return "";
		}
		JSONObject sysParaJson = JSONObject.fromObject(sysParaMap.get(paraCode));
		if (sysParaJson == null || sysParaJson.size() == 0)
		{
			return "";
		}
		return sysParaJson.optString("para_value");
	}

	/**
	 * @param iItemId
	 * @param comboDetailsId
	 * @param chanel
	 * @return
	 * @throws Exception
	 */
	public static JSONObject getComboDetailsForJson(Integer iItemId, Integer comboDetailsId, String chanel) throws Exception
	{
		String keyValue = String.valueOf(iItemId) + "_" + String.valueOf(comboDetailsId) + "_" + String.valueOf(chanel);

		return getCacheDataForJson(keyValue, CacheTableConstant.COMBO_DETAILS);
	}

	/**
	 * @param groupId
	 * @param itemId
	 * @param itemUnitId
	 * @return
	 * @throws Exception
	 */
	public static JSONObject getGroupDetailsForJson(Integer groupId, Integer itemId, Integer itemUnitId) throws Exception
	{
		String keyValue = String.valueOf(groupId) + "_" + String.valueOf(itemId) + "_" + String.valueOf(itemUnitId);

		return getCacheDataForJson(keyValue, CacheTableConstant.ITEM_GROUP_DETAILS);
	}

	/**
	 * @param discountCaseId
	 * @param itemId
	 * @param itemUnitName
	 * @return
	 * @throws Exception
	 */
	public static JSONObject getDiscountCaseDetailsForJson(Integer discountCaseId, Integer itemId, String itemUnitName) throws Exception
	{
		String keyValue = String.valueOf(discountCaseId) + "_" + String.valueOf(itemId) + "_" + String.valueOf(itemUnitName);

		return getCacheDataForJson(keyValue, CacheTableConstant.DISCOUNT_DETAIL);
	}

    /**
     *
     * @param tenancyId
     * @param storeId
     * @param organInfo
     * @return
     * @throws Exception
     */
	public static JSONObject getOrganInfo(String tenancyId, int storeId, Map<?, ?> organInfo) throws Exception{
        
		String keyValue = tenancyId + "_" + storeId;
		if (null == organInfo || organInfo.isEmpty() || !organInfo.containsKey(keyValue))
		{
			return null;
		}
		JSONObject organJson = JSONObject.fromObject(organInfo.get(keyValue));
		return organJson;
    }

}
