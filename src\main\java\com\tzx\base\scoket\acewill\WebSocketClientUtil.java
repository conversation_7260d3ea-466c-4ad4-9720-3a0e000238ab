package com.tzx.base.scoket.acewill;
/**
 * 微生活webSocket客户端工具类
 * 
 * <AUTHOR> email:<EMAIL>
 * @version 1.0 2018-03-10
 * @see
 * @since JDK7.0
 * @update
 */
public class WebSocketClientUtil {

	public static void connect() {
		Thread t = new Thread(new WebSocketConnectionThread(),"acewillwebSocketConnectionThread");
		t.start();
	}
	
	public static WebSocketClientHandler getWebSocketClientHandler() {
		if(WebSocketConnectionThread.webSocketClientHandler==null) {
			connect();
			try {
				Thread.sleep(1000);
			} catch (InterruptedException e) {
				// TODO Auto-generated catch block
				//e.printStackTrace();
			}
		}
		return WebSocketConnectionThread.webSocketClientHandler;
	}
	
	public static void send(String text) {
		getWebSocketClientHandler().send(text);
	}
	
}
