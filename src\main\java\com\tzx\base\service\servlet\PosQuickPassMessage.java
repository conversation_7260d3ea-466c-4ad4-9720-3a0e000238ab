package com.tzx.base.service.servlet;


import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import net.sf.json.JSONObject;

import org.apache.http.client.utils.DateUtils;
import org.apache.log4j.Logger;
import org.springframework.context.ApplicationContext;

import com.tzx.base.listener.PosQuickPassRunable;
import com.tzx.framework.common.constant.Oper;
import com.tzx.framework.common.constant.Type;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.util.DateUtil;
import com.tzx.framework.common.util.SpringConext;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;
import com.tzx.framework.common.util.dao.impl.GenericDaoImpl;
import com.tzx.member.crm.bo.CustomerService;
import com.tzx.orders.base.util.OrderUtil;
import com.tzx.pos.base.Constant;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.controller.CometDataNoticeClientRunnable;
import com.tzx.pos.base.util.JsonToDataUtil;
import com.tzx.pos.base.util.ParamUtil;
import com.tzx.pos.bo.PosDiscountService;
import com.tzx.pos.bo.PosPrintNewService;
import com.tzx.pos.bo.PosPrintService;
import com.tzx.pos.bo.PosService;
import com.tzx.pos.bo.QuickPassService;
import com.tzx.pos.bo.payment.imp.PosPaymentServiceImp;
import com.tzx.pos.bo.payment.imp.ext.SecondsPaymentWayServiceImp;
import com.tzx.pos.po.springjdbc.dao.PosBillDao;
import com.tzx.pos.po.springjdbc.dao.PosBillMemberDao;
import com.tzx.pos.po.springjdbc.dao.PosDishDao;
import com.tzx.pos.po.springjdbc.dao.PosPaymentDao;
import com.tzx.pos.po.springjdbc.dao.imp.PosBillDaoImp;
import com.tzx.pos.po.springjdbc.dao.imp.PosBillMemberDaoImp;
import com.tzx.pos.po.springjdbc.dao.imp.PosPaymentDaoImp;

public class PosQuickPassMessage {

	private Logger				logger		= Logger.getLogger(ProcessMessage.class);
	private Logger				tipLogger	= Logger.getLogger("tip");
	private ApplicationContext	context		= SpringConext.getApplicationContext();
	public PosQuickPassMessage()
	{
	}

	/**
	 * 
	 * @param data
	 * @return
	 * @throws Exception
	 */
//	public String doExecute(JSONObject getJson) throws Exception
//	{
//		String returnRes = "";
//		 String tenancy_id = getJson.optString("tenancy_id");
//		 int organId = Integer.valueOf(getJson.optInt("store_id"));
//		String type = getJson.optString("type");
//		//this.paymentRefund();//測試退款
//		//String orderState=this.findOrderState(getJson);
////		String orderState="1";
//			// 秒付付款
//			if (Type.BILL_PAYMENT.name().equals(type.toUpperCase()))
//			{
//				//0支付失败，1支付成功，2处理中，3未知
////				if("1".equals(orderState)) {
//
////				}else{
////					String msg="未知";
////					if("0".equals(orderState)){
////						msg="支付失败";
////					}else if("2".equals(orderState)){
////						msg="处理中";
////					}
////					Data cjData = new Data();
////					List list=new ArrayList<>();
////					cjData.setType(Type.BILL_PAYMENT);
////					cjData.setOper(Oper.find);
////					cjData.setCode(Constant.CODE_SUCCESS);
////					cjData.setMsg(msg);
////					list.add(cjData);
////					cjData.setData(list);
////					Comet4jUtil.sendMessage2All(Comet4jUtil.POS_PC, JSONObject.fromObject(cjData).toString());
////				}
//				returnRes = "秒付 数据下发指令执行成功!";
//			}
//		return returnRes;
//	}

	/**
	 *
	 * 查询订单状态
	 * 0支付失败，1支付成功，2处理中，3未知
	 * @param param
	 * @return
	 */
	public String findOrderState(JSONObject param)throws Exception{
		String tenancy_id = param.optString("tenancy_id");
		int organId = Integer.valueOf(param.optInt("store_id"));
		List<JSONObject> list=param.getJSONArray("data");
		JSONObject data = JSONObject.fromObject(list.get(0));
		String  opt_num=ParamUtil.getStringValueByObject(data,"opt_num",false,null);
		String  pos_num=ParamUtil.getStringValueByObject(data,"pos_num",false,null);
		String bill_num=ParamUtil.getStringValueByObject(data,"bill_num",false,null);
		PosQuickPassRunable posQuickPassRunable=new PosQuickPassRunable(tenancy_id,organId+"",bill_num, SysDictionary.SERVICE_TYPE_CONSUME,pos_num,opt_num,SysDictionary.PAYMENT_CLASS_SECONDS_PAY);
		JSONObject jsonObject2=posQuickPassRunable.getPaymentState();
		String res="";
				if(jsonObject2!=null){
					res=ParamUtil.getStringValueByObject(jsonObject2,"payment_state",false,null);
				}
		return res;
	}
	/**
	 * 秒付业务处理
	 * @param param
	 * @throws Exception
	 */
	public void quickPayMent(QuickPassService q, PosPaymentServiceImp paymentWayService, JSONObject param)throws Exception{
		logger.info("获取MQ数据，数据为：>>>>"+param.toString());
		List<JSONObject> list1=param.getJSONArray("data");
		JSONObject data = JSONObject.fromObject(list1.get(0));
		JSONObject  item= JSONObject.fromObject(data.optString("item"));
		String bill_num=ParamUtil.getStringValueByObject(data,"bill_num",false,null);
		String table_code=ParamUtil.getStringValueByObject(data,"table_code",false,null);
		Double bill_amount =ParamUtil.getDoubleValueByObject(data,"bill_amount",false,null);//订单总金额
		String bill_code=ParamUtil.getStringValueByObject(item,"bill_code",false,null);
		Double amount=ParamUtil.getDoubleValueByObject(item,"amount",false,null);//支付金额

		Data cjData = new Data();
		List list=new ArrayList<>();
		cjData.setType(Type.BILL_PAYMENT);
		cjData.setOper(Oper.find);
		//jsonObject为返回前台数据
		JSONObject jsonObject=new JSONObject();
		jsonObject.put("bill_code", bill_code);
		jsonObject.put("table_code", table_code);
		jsonObject.put("amount", bill_amount);
		jsonObject.put("pay_amount", amount);
		jsonObject.put("bill_num", bill_num);
//		jsonObject.put("is_online_payment", "1");
		//插入闪付表数据
			int res=q.insert(param);
			if(res>0){
				jsonObject.put("state", "1");//闪付插入成功
				/**
				 * 如果bill_code 为空，给前台提示空信息
				 */
				if(bill_num==null||"".equals(bill_num)){
					cjData.setCode(Constant.CODE_PARAM_FAILURE);
					cjData.setMsg("桌台号:"+table_code+"的bill_num不能为空第（"+res+"）次");
					logger.info("bill_code为空，推送开始>>>>>>>"+res);
				}else{
					String tenancy_id = param.getString("tenancy_id");
					Integer store_id = ParamUtil.getIntegerValueByObject(param,"store_id",false,null);
					String  report_date=ParamUtil.getDateStringValue(data,"report_date");
					String  pos_num=ParamUtil.getStringValueByObject(data,"pos_num",false,null);
					String  opt_num=ParamUtil.getStringValueByObject(data,"opt_num",false,null);
					String mem=ParamUtil.getStringValueByObject(item,"mem",false,null);//是否是会员
					Integer discount_rate=ParamUtil.getIntegerValueByObject(item,"discount_rate",false,null);//折扣率
					Double discount_amount=ParamUtil.getDoubleValueByObject(item,"discount_amount",false,null);//折扣金额
					Integer  shift_id=ParamUtil.getIntegerValueByObject(data,"shift_id",false,null);
					String sale_mode="";
					boolean ifStore=true;//是否门店计算折扣
					try{
						//从账单 表取出对应数据
//						if(null==pos_num||"".equals(pos_num)){
							List<JSONObject> list2=this.findExistBill(bill_num,tenancy_id);
							logger.debug("从账单 表取出数据》》"+list2.toString());
							if(null!=list2&&list2.size()>0){
								JSONObject jsonObject1=list2.get(0);
								pos_num=jsonObject1.getString("open_pos_num");
								opt_num=jsonObject1.getString("open_opt");
								report_date=jsonObject1.getString("report_date");
								sale_mode=jsonObject1.getString("sale_mode");
                                Double  discountr_amount=jsonObject1.optDouble("discountr_amount",0);
                                Integer  discount_case_id=jsonObject1.optInt("discount_case_id",0);
                                Integer  discount_rate1=jsonObject1.optInt("discount_rate",100);
								Integer  discount_mode_id=jsonObject1.optInt("discount_mode_id",5);
//                                String  discount_mode_id=jsonObject1.get("discount_mode_id")!=null?jsonObject1.get("discount_mode_id").toString():"5";
                                jsonObject.put("discount_case_id",discount_case_id);
                                jsonObject.put("discount_mode_id",discount_mode_id);
                                jsonObject.put("discount_rate",discount_rate1);
                                jsonObject.put("discountr_amount",discountr_amount);
                                //不从门店计算折扣
                                if(100-discount_rate1==0&&discountr_amount<1){
									jsonObject.put("discount_rate",discount_rate==null?100:discount_rate);
									jsonObject.put("discountr_amount",discount_amount);
									ifStore=false;
								}
								jsonObject.put("pos_num",pos_num);
								jsonObject.put("opt_num",opt_num);
								jsonObject.put("sale_mode",sale_mode);
								Date reportDate=DateUtil.parseDate(report_date);
								try{
									this.setOptNum(jsonObject,pos_num,reportDate);
								}catch (Exception e){
									logger.error("设置秒付收银机台号异常"+e);
								}

							}else {
								logger.info("账单表中没数据》"+bill_num);
								return ;
							}
						if(shift_id==null){
							Date reportDate=DateUtil.parseDate(report_date);
							PosDishDao posDishDao= (PosDishDao) SpringConext.getApplicationContext().getBean(PosDishDao.NAME);
							shift_id = posDishDao.getShiftId(tenancy_id, store_id, reportDate, opt_num, pos_num);
							jsonObject.put("shift_id",shift_id);
							jsonObject.put("report_date",report_date);
						}else{
							jsonObject.put("shift_id",shift_id);
							jsonObject.put("report_date",report_date);
						}
//						}
						//1会员 ，否则为非会员
						if("1".equals(mem)){

							Double jifen=item.optDouble("jifen",0);//积分
							String mobile=item.optString("mobile","");//会员手机号
							Double dixian=item.optDouble("dixian",0);//抵现金额
							String card_code=item.optString("card_code","");//抵现金额
							Double sub_balance=item.optDouble("sub_balance",0);//储值消费金额
							jsonObject.put("mobile",mobile);
							jsonObject.put("dixian",dixian);
							jsonObject.put("card_code",card_code);
							jsonObject.put("sub_balance",sub_balance);
							try{
								//会员信息处理
								this.memUserInfo(tenancy_id,store_id,jifen,jsonObject,ifStore);
							}catch (Exception e){
								logger.error("会员查询出现异常"+e);
							}

							//门店打折处理流程
							if(ifStore){
								try{
									//门店处理会员折扣
									this.discountUser(tenancy_id,store_id,jsonObject);
								}catch (Exception e){
									logger.error("会员折扣出现异常"+e);
								}
							}
							//从saas打折后处理流程
							if(!ifStore){
								this.storeDiscount(tenancy_id,store_id,bill_num,bill_amount,table_code,item,jsonObject);
							}
							if(sub_balance>0){
								//储值卡消费
								this.MemberCardPay(param, item, data, jsonObject, sub_balance/100, cjData, sale_mode, paymentWayService);
							}
							if(jifen>0){
								//积分支付
								this.MemberPay(param, item, data, jsonObject, jifen, cjData, sale_mode, paymentWayService);
							}
							if(amount>0){
								//非积分支付
								Double a =amount / 100;//付款金额
								this.NOMemberPay(param, item, data, jsonObject, a, cjData, sale_mode, paymentWayService);
							}


						}else {
							Double payAmount = findAmount(bill_num, store_id);//账单金额
							Double a = amount / 100;//付款金额
							if (a-payAmount >= 0) {
								this.NOMemberPay(param, item, data, jsonObject, a, cjData, sale_mode, paymentWayService);
							}else{
								logger.info("线上付款成功,结账失败，原因为付款金额小于账单金额");
							}
						}

					}catch(Exception e){
						logger.info("付款失败state=3"+e);
						jsonObject.put("state", "3");//付款失败
						cjData.setMsg("付款失败");
					}

				}
				
			}else{
				jsonObject.put("state", "0");//闪付插入失败
				logger.info("闪付插入失败state=0");
			}
		list.add(jsonObject);
		cjData.setData(list);
		
		//Comet4jUtil.sendMessage2All(Comet4jUtil.POS_PC, JSONObject.fromObject(cjData).toString());
		
		CometDataNoticeClientRunnable noticeClientRunnable = new CometDataNoticeClientRunnable(cjData);
		Thread noticeThread = new Thread(noticeClientRunnable);
		noticeThread.start();
	}

	/**
	 * 门店处理SAAS折扣流程
	 */
   public void storeDiscount(String tenantId,Integer store_id,String bill_num,Double bill_amount,String table_code,JSONObject item,JSONObject jsonObject) throws Exception {
	    Double amount=item.optDouble("amount",0);//支付金额
		Integer manager_num=jsonObject.optInt("manager_num",0);
		Integer discount_mode_id=jsonObject.optInt("discount_mode_id",0);
		Integer discount_id=jsonObject.optInt("discount_id",0);
		Integer discount_rate=jsonObject.optInt("discount_rate",100);
		Double discountr_amount=jsonObject.optDouble("discountr_amount",0);
	   String sqlBill = " update pos_bill set  payment_amount=?,difference=?,discountk_amount=?,discount_amount=?," +
			   "discount_mode_id = ?, discount_rate=?, discountr_amount=?, discount_case_id=? where bill_num = ? and store_id = ? ";
	   PosDishDao posDishDao= (PosDishDao) SpringConext.getApplicationContext().getBean(com.tzx.pos.po.springjdbc.dao.PosDishDao.NAME);
	   // 更新账单
	   posDishDao.update(sqlBill, new Object[]
			   { bill_amount/100,0,discountr_amount,discountr_amount,discount_mode_id, discount_rate, 0, discount_id, bill_num, store_id });
		   //菜品明细Lst
		   List<JSONObject> details_list=item.optJSONArray("detaillist");
	   		StringBuffer detailsSql=new StringBuffer("update pos_bill_item set item_price=?,item_count=?,item_amount=?,real_amount=?,discount_amount=?,discount_rate=?,discount_state=? where rwid=? and item_id=? and bill_num=?");
	   	List<Object[]> itemDetailsList = new ArrayList<Object[]>();
	   	if(details_list!=null&&details_list.size()>0){
			for(JSONObject obj : details_list){
				Object[] objs = new Object[]{obj.optDouble("item_price",0), obj.optInt("item_count",0), obj.optDouble("item_amount",0),obj.optDouble("real_amount",0),obj.optDouble("discount_amount",0),obj.optInt("discount_rate",100),obj.optString("discount_state"),obj.optInt("rwid",0), obj.optInt("item_id",0), bill_num};
				itemDetailsList.add(objs);
			}
			posDishDao.batchUpdate(detailsSql.toString(),itemDetailsList);
		}


   }


	/**
	 * 设置秒付收银机台号
	 */
	public void setOptNum(JSONObject jsonObject,String pos_num,Date report_date){
		String sql="select op.pos_num,op.opt_num from pos_opt_state op " +
				" left join (select hd.tenancy_id,hd.store_id,(case when hd.show_type in ('PAD','YD') then sd.pos_num else hd.devices_code end) as pos_num,hd.devices_code from public.hq_devices hd  " +
				" left join pos_opt_state_devices sd on hd.tenancy_id=sd.tenancy_id and hd.store_id=sd.store_id and hd.devices_code=sd.devices_num and sd.is_valid='1') hd  " +
				" on op.tenancy_id=hd.tenancy_id and op.store_id=hd.store_id and op.pos_num=hd.pos_num " +
				" where op.report_date=? and hd.devices_code=? and op.content=? and tag='0'";
		GenericDaoImpl q = (GenericDaoImpl) SpringConext.getApplicationContext().getBean("genericDaoImpl");
		List <Map<String,Object>>list=q.jdbcTemplate.queryForList(sql,new Object[]{report_date,pos_num,SysDictionary.OPT_STATE_KSSY});
		if(list!=null&&list.size()>0) {
			Map<String,Object> map = list.get(0);
			String posNum = map.get("pos_num").toString();
			String optNum = map.get("opt_num").toString();
			jsonObject.put("pos_num",posNum);
			jsonObject.put("opt_num",optNum);
			logger.info("设置秒付收银机台号成功");
		}
		else
		{
			String querySql = "select op.pos_num,op.opt_num from pos_opt_state op where op.report_date=? and op.content=? and tag='0' order by op.last_updatetime desc";
			list = q.jdbcTemplate.queryForList(querySql, new Object[]
			{ report_date, SysDictionary.OPT_STATE_KSSY });
			if (list != null && list.size() > 0)
			{
				Map<String, Object> map = list.get(0);
				String posNum = map.get("pos_num").toString();
				String optNum = map.get("opt_num").toString();
				jsonObject.put("pos_num",posNum);
				jsonObject.put("opt_num",optNum);
				logger.info("设置秒付收银机台号成功");
			}
		}
	}

	/**
	 * 会员价折扣
	 * @param tenancy_id
	 * @param store_id
	 * @param jsonObject
	 */
	public void discountUser(String tenancy_id,Integer store_id,JSONObject jsonObject) throws Exception {
		Data data= new Data();
		data.setType(Type.BILL_DISCOUNT);
		data.setOper(Oper.find);
		data.setTenancy_id(tenancy_id);
		data.setStore_id(store_id);
		data.setSecret("0");
		data.setSource(SysDictionary.SOURCE_WIN_POS);
		List list=new ArrayList();
		jsonObject.put("chanel",SysDictionary.CHANEL_MD01);
		jsonObject.put("mode","1");
        Integer discount_case_id=jsonObject.optInt("discount_case_id",0);
        Integer discount_mode_id=jsonObject.optInt("discount_mode_id",5);
		jsonObject.put("discount_id",discount_case_id);
		jsonObject.put("discount_mode",discount_mode_id);
		jsonObject.put("discount_reason_id",0);
//		Double bill_amount=jsonObject.getDouble("amount")/100;
		Double discountr_amount=jsonObject.optDouble("discountr_amount",0);
		if(discountr_amount>0){
            jsonObject.put("discount_rate",100);
        }
		jsonObject.put("discountr_amount",discountr_amount);
		jsonObject.put("manager_num","");

		list.add(jsonObject);
		data.setData(list);
		logger.info("开始进行折扣处理>>>>"+data.getData().toString());
		PosDiscountService posDiscountService= (PosDiscountService) SpringConext.getApplicationContext().getBean(com.tzx.pos.bo.PosDiscountService.NAME);
		posDiscountService.newBillDiscount(data);
	}
	/**
	 * 会员信息处理
	 */
	public void memUserInfo(String tenancy_id,Integer store_id,Double jifen,JSONObject jsonObject,boolean ifStore) throws Exception {
		Data data= new Data();
		data.setType(Type.CUSTOMERINFO);
		data.setOper(Oper.find);
		data.setTenancy_id(tenancy_id);
		data.setStore_id(store_id);
		data.setSecret("0");
		data.setSource("SERVER");
		List list=new ArrayList();
		jsonObject.put("ischeck","Y");
		jsonObject.put("wechat","");
		jsonObject.put("qq","");
		jsonObject.put("email","");
//		jsonObject.put("card_code","");
		jsonObject.put("third_code","");
		jsonObject.put("mobil",jsonObject.optString("mobile",""));
		list.add(jsonObject);
		data.setData(list);
		Data resData=new Data();
		//会员查询
		CustomerService customerService= (CustomerService) SpringConext.getApplicationContext().getBean(com.tzx.member.crm.bo.CustomerService.NAME);
		customerService.commonPost(JsonToDataUtil.DataToJson(data),resData);
		if(resData.getCode()==0){
			list=resData.getData();
			JSONObject resJsonObj= JSONObject.fromObject(list.get(0));
			//插入本地会员信息表
			PosBillMemberDaoImp memberDao = (PosBillMemberDaoImp) SpringConext.getApplicationContext().getBean(PosBillMemberDao.NAME);
			Double useful_credit= resJsonObj.optDouble("useful_credit",0);
			if(Double.isNaN(jifen)){
				jifen=0.0;
			}
			Double consumeBeforeCredit=useful_credit+jifen;
			Timestamp currentTime=DateUtil.currentTimestamp();
			Date report_date= DateUtils.parseDate(jsonObject.getString("report_date"));
//			Double pay_amount=jsonObject.optDouble("pay_amount",0);
//			Double sub_balance=jsonObject.optDouble("sub_balance",0);
//			//只有会员卡消费或现金消费才会赠积分
//			if(pay_amount>0||sub_balance>0){
				memberDao.insertPosBillMember(tenancy_id, store_id,jsonObject.getString("bill_num") ,report_date , SysDictionary.BILL_MEMBERCARD_JFDX05, resJsonObj.get("code")+"", null, jsonObject.getString("mobile"), currentTime, null,  resJsonObj.get("name")+"", consumeBeforeCredit,useful_credit ,jifen,jsonObject.optDouble("dixian",0), jsonObject.optString("bill_code"),
						SysDictionary.REQUEST_STATUS_COMPLETE);
//			}

			jsonObject.put("useful_credit",useful_credit);
			jsonObject.put("customer_credit",consumeBeforeCredit);
			jsonObject.put("code",resJsonObj.optString("code",""));
			jsonObject.put("customer_code",resJsonObj.optString("code",""));
			jsonObject.put("name",resJsonObj.optString("name",""));
			jsonObject.put("customer_name",resJsonObj.optString("name",""));
			if(!ifStore){
				jsonObject.put("discount_rate",resJsonObj.optInt("rate",0));
			}
//			List <JSONObject>cardLst= (List) resJsonObj.get("cardlist");
//			if(cardLst!=null&&cardLst.size()>=1){
//				Map map= (Map) cardLst.get(0);
//				jsonObject.put("card_code",map.get("card_code"));
//			}
			logger.info("会员信息查询结束"+jsonObject.toString());
		}
	}

	/**
	 * 会员储值卡付款
	 * @param param

	 */
	public void MemberCardPay(JSONObject param,	JSONObject  item,JSONObject data,JSONObject jsonObject,Double a,Data cjData,String sale_mode,PosPaymentServiceImp paymentWayService)throws Exception{
		String tenancy_id = param.getString("tenancy_id");
		Integer store_id = param.optInt("store_id",0);
		Integer count=item.optInt("count",0);
		Integer jzid_sub=item.optInt("jzid_sub",0);//储值Id
		String number=jsonObject.optString("code","");
		Double useful_credit=jsonObject.optDouble("useful_credit",0);
		String name=jsonObject.optString("name","");
		String bill_num=data.optString("bill_num","");
		String bill_code=item.optString("bill_code","");
		Double dixian=item.optDouble("dixian",0);//抵现金额
		Integer customer_id=item.optInt("customer_id",0);//会员id
		if(jzid_sub==null){
			jzid_sub=0;
			logger.debug("MQ数据异常，没有积分结账ID，数据为：>>>>"+param.toString());
			throw new Exception();
		}
		JSONObject jsonObject1=new JSONObject();
		logger.debug("开始查询会员卡付款类型");
		jsonObject1=this.getPayType(tenancy_id,store_id,jzid_sub,jsonObject1);
		jsonObject1.put("amount",a);
		jsonObject1.put("currency_amount",a);
		jsonObject1.put("count",count==null?0:count);
		jsonObject1.put("jzid",jzid_sub);
		jsonObject1.put("name",name);
		jsonObject1.put("useful_credit",useful_credit);
		jsonObject1.put("bill_code",bill_code);
		jsonObject1.put("customer_id",customer_id);
		jsonObject1.put("mobile",jsonObject.optString("mobil",""));
		jsonObject1.put("phone",jsonObject.optString("mobil",""));
		jsonObject1.put("number",jsonObject.optString("card_code",""));
		jsonObject1=this.getBillPayMent(bill_num,store_id,jsonObject1,a,customer_id);
		logger.debug("会员卡开始付款>>");
		//调用付款方法
		JSONObject printJson = new JSONObject();
		Data result=new Data();
		Data parmedt=new Data();
		parmedt.setTenancy_id(tenancy_id);
		parmedt.setStore_id(store_id);
		List list4=new ArrayList();
		List list3=new ArrayList();
		list3.add(jsonObject1);
		jsonObject.put("isprint_bill", OrderUtil.getSysPara(tenancy_id,store_id,"JZSFDY"));
		jsonObject.put("item",list3);
		list4.add(jsonObject);
		parmedt.setData(list4);
		logger.info("会员卡开始付款，传入参数为"+ JsonToDataUtil.DataToJson(parmedt).toString());
		paymentWayService.posBillPayment(parmedt,result,printJson,false);
//							paymentWayService.payment(tenancy_id, store_id, bill_num, null,null==report_date?reportDate:DateUtil.parseDate(report_date), shift_id, pos_num, opt_num, table_code, jsonObject1, DateUtil.getNowTimestamp(), is_online_payment);
		jsonObject.put("state", "2");//付款成功
		cjData.setMsg("会员卡付款成功");
		logger.info("会员卡付款成功，准备打印小票");
		PosService posService= (PosService) SpringConext.getApplicationContext().getBean(PosService.NAME);
		PosPrintService posPrintService= (PosPrintService) SpringConext.getApplicationContext().getBean(PosPrintService.NAME);
		PosPrintNewService posPrintNewService= (PosPrintNewService) SpringConext.getApplicationContext().getBean(PosPrintNewService.NAME);
//							posService.updateDevicesDataState();
		posPrintService.printPosBillForPayment(printJson, posPrintNewService);
		logger.debug("会员卡付款操作成功");
	}
	/**
	 * 会员积分付款
	 * @param param
	 * @param item
	 * @param data
	 * @param jsonObject
	 * @param a
	 * @param cjData
	 * @param sale_mode
	 * @param
	 */
	public void MemberPay(JSONObject param,	JSONObject  item,JSONObject data,JSONObject jsonObject,Double a,Data cjData,String sale_mode,PosPaymentServiceImp paymentWayService)throws Exception{
		String tenancy_id = param.getString("tenancy_id");
		Integer store_id = ParamUtil.getIntegerValueByObject(param,"store_id",false,null);
		Integer count=ParamUtil.getIntegerValueByObject(item,"count",false,null);
		Integer jzid_credit=ParamUtil.getIntegerValueByObject(item,"jzid_credit",false,null);
		String number=ParamUtil.getStringValueByObject(jsonObject,"code",false,null);
		Double useful_credit=ParamUtil.getDoubleValueByObject(jsonObject,"useful_credit",false,null);
		String name=ParamUtil.getStringValueByObject(jsonObject,"name",false,null);
		String bill_num=ParamUtil.getStringValueByObject(data,"bill_num",false,null);
		String bill_code=ParamUtil.getStringValueByObject(item,"bill_code",false,null);
		Double dixian=ParamUtil.getDoubleValueByObject(item,"dixian",false,null);//抵现金额
		Integer customer_id=ParamUtil.getIntegerValueByObject(item,"customer_id",false,null);//会员id
		if(jzid_credit==null){
			jzid_credit=0;
			logger.debug("MQ数据异常，没有积分结账ID，数据为：>>>>"+param.toString());
			throw new Exception();
		}
		JSONObject jsonObject1=new JSONObject();
		logger.debug("开始查询积分付款类型");
		jsonObject1=this.getPayType(tenancy_id,store_id,jzid_credit,jsonObject1);
		jsonObject1.put("amount",dixian/100);
		jsonObject1.put("jifen",a);
		jsonObject1.put("count",count);
		jsonObject1.put("jzid",jzid_credit);
		jsonObject1.put("name",name);
		jsonObject1.put("useful_credit",useful_credit);
		jsonObject1.put("bill_code",bill_code);
		jsonObject1.put("customer_id",customer_id);
		jsonObject1.put("mobile",jsonObject.optString("mobil",""));
		jsonObject1.put("number",jsonObject.optString("card_code",""));
		jsonObject1=this.getBillPayMent(bill_num,store_id,jsonObject1,a,customer_id);
		logger.debug("积分开始付款>>");
		//调用付款方法
		JSONObject printJson = new JSONObject();
		Data result=new Data();
		Data parmedt=new Data();
		parmedt.setTenancy_id(tenancy_id);
		parmedt.setStore_id(store_id);
		List list4=new ArrayList();
		List list3=new ArrayList();
		list3.add(jsonObject1);
		jsonObject.put("isprint_bill", OrderUtil.getSysPara(tenancy_id,store_id,"JZSFDY"));
		jsonObject.put("item",list3);
		list4.add(jsonObject);
		parmedt.setData(list4);
		logger.info("积分开始付款，传入参数为"+ JsonToDataUtil.DataToJson(parmedt).toString());
		paymentWayService.posBillPayment(parmedt,result,printJson,false);
//							paymentWayService.payment(tenancy_id, store_id, bill_num, null,null==report_date?reportDate:DateUtil.parseDate(report_date), shift_id, pos_num, opt_num, table_code, jsonObject1, DateUtil.getNowTimestamp(), is_online_payment);
		jsonObject.put("state", "2");//付款成功
		cjData.setMsg("积分付款成功");
		logger.info("积分付款成功，准备打印小票");
		PosService posService= (PosService) SpringConext.getApplicationContext().getBean(PosService.NAME);
		PosPrintService posPrintService= (PosPrintService) SpringConext.getApplicationContext().getBean(PosPrintService.NAME);
		PosPrintNewService posPrintNewService= (PosPrintNewService) SpringConext.getApplicationContext().getBean(PosPrintNewService.NAME);
//							posService.updateDevicesDataState();
		posPrintService.printPosBillForPayment(printJson, posPrintNewService);
		logger.debug("积分付款操作成功");
	}
	/**
	 * 非积分付款
	 */
	public void NOMemberPay(JSONObject param,	JSONObject  item,JSONObject data,JSONObject jsonObject,Double a,Data cjData,String sale_mode,PosPaymentServiceImp paymentWayService) throws Exception{
		String tenancy_id = param.getString("tenancy_id");
		Integer store_id = ParamUtil.getIntegerValueByObject(param,"store_id",false,null);
		Integer count=ParamUtil.getIntegerValueByObject(item,"count",false,null);
		Integer jzid=ParamUtil.getIntegerValueByObject(item,"jzid",false,null);
		String number=ParamUtil.getStringValueByObject(item,"number",false,null);
		String bill_num=ParamUtil.getStringValueByObject(data,"bill_num",false,null);
		String bill_code=ParamUtil.getStringValueByObject(item,"bill_code",false,null);
		Integer customer_id=ParamUtil.getIntegerValueByObject(item,"customer_id",false,null);//会员id

		if(jzid==null){
			jzid=0;
			logger.info("MQ数据异常，没有结账ID，数据为：>>>>"+param.toString());
			throw new Exception();
		}

		JSONObject jsonObject1=new JSONObject();
		logger.debug("开始查询付款类型");
		jsonObject1=this.getPayType(tenancy_id,store_id,jzid,jsonObject1);
		jsonObject1.put("amount",a);
		jsonObject1.put("count",count);
		jsonObject1.put("jzid",jzid);
		jsonObject1.put("mobile",jsonObject.optString("mobil",""));
		jsonObject1.put("bill_code",bill_code);
		jsonObject1.put("number",jsonObject.optString("card_code",""));
		jsonObject1=this.getBillPayMent(bill_num,store_id,jsonObject1,a,customer_id);

		logger.debug("开始付款>>");
		//调用付款方法
		JSONObject printJson = new JSONObject();
		Data result=new Data();
		Data parmedt=new Data();
		parmedt.setTenancy_id(tenancy_id);
		parmedt.setStore_id(store_id);
		List list4=new ArrayList();
		List list3=new ArrayList();
		list3.add(jsonObject1);
		jsonObject.put("isprint_bill", OrderUtil.getSysPara(tenancy_id,store_id,"JZSFDY"));
		jsonObject.put("item",list3);
		list4.add(jsonObject);
		parmedt.setData(list4);
		logger.info("开始付款，传入参数为"+ JsonToDataUtil.DataToJson(parmedt).toString());
		paymentWayService.posBillPayment(parmedt,result,printJson,false);
		jsonObject.put("state", "2");//付款成功
		cjData.setMsg("付款成功");
		logger.info("付款成功，准备打印小票");
		PosService posService= (PosService) SpringConext.getApplicationContext().getBean(PosService.NAME);
		PosPrintService posPrintService= (PosPrintService) SpringConext.getApplicationContext().getBean(PosPrintService.NAME);
		PosPrintNewService posPrintNewService= (PosPrintNewService) SpringConext.getApplicationContext().getBean(PosPrintNewService.NAME);
//							posService.updateDevicesDataState();
		posPrintService.printPosBillForPayment(printJson, posPrintNewService);
		logger.debug("操作成功");
	}
	/**
	 * 查询原账单金额
	 * @param billNum
	 * @param storeId
	 * @return
	 */
	public Double findAmount(String billNum,Integer storeId){
		String sql="select payment_amount from pos_bill where bill_num=? and store_id=?";
		GenericDaoImpl q = (GenericDaoImpl) SpringConext.getApplicationContext().getBean("genericDaoImpl");
		List <Map<String,Object>>list=q.jdbcTemplate.queryForList(sql,new Object[]{billNum,storeId});
		Double amount=0.0;
		if(list!=null&&list.size()>0){
			Map map=list.get(0);
			String amountstr=  map.get("payment_amount").toString();
			amount=Double.parseDouble(amountstr);
		}
		return amount;
	}


	/**
	 * 美大秒付付款方式
	 * @param tenancy_id
	 * @param store_id
	 * @param jzid
	 * @param jsonObject
	 * @return
	 * @throws Exception
	 */
	public JSONObject getPayType(String tenancy_id,Integer store_id,Integer jzid,JSONObject jsonObject) throws Exception {
		PosPaymentDaoImp posPaymentServiceImp = (PosPaymentDaoImp) SpringConext.getApplicationContext().getBean(PosPaymentDao.NAME);
		JSONObject object=posPaymentServiceImp.getPaymentWayByID(tenancy_id,store_id,jzid);
		if(object!=null&&!object.isEmpty()){
			jsonObject.put("payment_class",object.get("payment_class"));
			jsonObject.put("payment_name",object.get("payment_name"));
			jsonObject.put("payment_english_name",object.get("name_english"));
			jsonObject.put("rate",object.get("rate"));
		}else{
			logger.info("数据库没有对应付款类型，请下发");
			jsonObject.put("payment_class","");
			jsonObject.put("payment_name","");
			jsonObject.put("payment_english_name","");
			jsonObject.put("rate","");
		}

		return jsonObject;
	}
	/**
	 * 美大秒付付款参数

	 * @return
	 * @throws Exception
	 */
	public JSONObject getBillPayMent(String bill_num,Integer store_id,JSONObject jsonObject,Double currency_amount,Integer customer_id) throws Exception {
		PosBillDaoImp posBillDaoImp = (PosBillDaoImp) SpringConext.getApplicationContext().getBean(PosBillDao.NAME);
		logger.debug("付款方式确认");
		List <JSONObject>list=posBillDaoImp.findPosPayment(bill_num);
		if(list!=null&&list.size()>0){
			JSONObject jsonObject1= (JSONObject) list.get(0);
			jsonObject.put("remark",SysDictionary.PAYMENT_CLASS_SECONDS_PAY);
			jsonObject.put("is_ysk",jsonObject1.optString("is_ysk"));
			jsonObject.put("currency_amount",jsonObject1.optDouble("currency_amount",currency_amount));
			jsonObject.put("customer_id",jsonObject1.optInt("customer_id",0));
			jsonObject.put("phone",jsonObject1.optString("phone",""));
			jsonObject.put("shift_id",jsonObject1.optInt("shift_id",0));

		}else{
			jsonObject.put("remark",SysDictionary.PAYMENT_CLASS_SECONDS_PAY);
			jsonObject.put("is_ysk","");
			jsonObject.put("currency_amount",currency_amount);
			jsonObject.put("customer_id",customer_id);
			jsonObject.put("phone","");
			logger.info("付款方式确认失败>>>>bill_num="+bill_num+">>>jsonObject="+jsonObject.toString());
//			jsonObject.put("shift_id",0);
		}

		return jsonObject;
	}

	/**
	 * 测试
	 * @throws Exception
	 */
	public void paymentRefund() throws Exception {

		String bill_num = "1362016110";
		String payOrderId = "34567890";
		String serviceType = "";
		String pos_num = "111";
		String opt_num = "22";
		Double amount = 2.0;
		String payType = "";
		String currency = "CNY";
		String reportDate = "2016-08-11 15:11:48";
		String shift = "1";
		String channel = "BD06";


		String tenancyId="hdl";
		int storeId=369;
		String billNum="";
		String oldBillNum="";
		String orderNum="";
		String chanel="";
		Double billPaymentAmount=null;
		JSONObject payment=new JSONObject();
		payment.put("bill_num",bill_num);
		payment.put("bill_code",payOrderId);
		payment.put("serviceType",serviceType);
		payment.put("pos_num",pos_num);
		payment.put("optNum",opt_num);
		payment.put("amount",amount);
		payment.put("pay_type",payType);
		payment.put("currency_amount",currency);
		payment.put("report_Date",reportDate);
		payment.put("shift_id",shift);
		payment.put("channel",channel);


		Timestamp currentTime=null;
//		JSONObject resultJson=new JSONObject();
//		Data resultData=new Data();


		DBContextHolder.setTenancyid(tenancyId);
		context = SpringConext.getApplicationContext();
		SecondsPaymentWayServiceImp paymentWayService = (SecondsPaymentWayServiceImp) SpringConext.getApplicationContext().getBean(SecondsPaymentWayServiceImp.class);
		paymentWayService.paymentRefund(tenancyId,storeId,billNum,oldBillNum,orderNum,chanel,billPaymentAmount,payment,currentTime,"");
	}

	public  List<JSONObject> findExistBill(String billNum,String tenent_id) throws Exception {
		logger.debug("查询是否存在未结账账单");
		DBContextHolder.setTenancyid(tenent_id);
		PosBillDaoImp posBillDaoImp = (PosBillDaoImp) SpringConext.getApplicationContext().getBean(PosBillDao.NAME);
		List<JSONObject> list= posBillDaoImp.findPosPayment(billNum);
		logger.debug("未结账账单"+list.toString());
		return list;
	}
}
