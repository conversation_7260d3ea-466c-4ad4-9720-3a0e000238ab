 package com.tzx.clientorder.retail.common;
 
 import com.tzx.pos.base.constant.SysDictionary;
 
 public enum ChannelMapping
 {
   PF(SysDictionary.CHANEL_WX02, "PF"), 
   TF(SysDictionary.CHANEL_PAD, "TF"), 
   ESH(SysDictionary.CHANEL_EZZ, "ESH");
 
   private String channlCode;
   private String retailCode;
 
   private ChannelMapping(String channlCode, String retailCode)
   {
     this.channlCode = channlCode;
     this.retailCode = retailCode;
   }
 
   public String getChannlCode() {
     return this.channlCode;
   }
 
   public void setChannlCode(String channlCode) {
     this.channlCode = channlCode;
   }
 
   public String getRetailCode() {
     return this.retailCode;
   }
 
   public void setRetailCode(String retailCode) {
     this.retailCode = retailCode;
   }
 
   public static ChannelMapping getChannelMapping(String name) {
     if (null != name) {
       return valueOf(name);
     }
 
     return PF;
   }
 }

