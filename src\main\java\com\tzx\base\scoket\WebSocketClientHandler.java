/** 
 * @(#)CodeFormat.java    1.0   2017-10-06
 * 
 * Copyright 北京普照天星科技有限公司. All rights reserved. 
 * PROPRIETARY/CONFIDENTIAL. Use is subject to license terms. 
 */
package com.tzx.base.scoket;

import com.tzx.base.common.util.ConsumerThreadPoolUtil;
import com.tzx.base.common.util.PosPropertyUtil;
import com.tzx.base.listener.PushPosConsumerThreadPoolTask;
import com.tzx.base.service.servlet.PushProcessMessage;
import com.tzx.framework.common.constant.Constant;
import net.sf.json.JSONObject;
import org.java_websocket.client.WebSocketClient;
import org.java_websocket.drafts.Draft;
import org.java_websocket.framing.Framedata;
import org.java_websocket.handshake.ServerHandshake;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.URI;
import java.util.Iterator;
import java.util.Set;
import java.util.concurrent.ConcurrentSkipListSet;

/**
 * Netty消息推送服务客户端
 * 
 * <AUTHOR> email:she<PERSON><PERSON><PERSON>@tzx.com.cn
 * @version 1.0 2017-10-09
 * @see
 * @since JDK7.0
 * @update
 */
public class WebSocketClientHandler extends WebSocketClient{
	private final static String MSG_RETURN = "success";
	private final static String MSG_PING = "PING";
	private static final Logger LOGGER = LoggerFactory.getLogger(WebSocketClientHandler.class);

    public static final Set<String> UNIQUE_CODE_SET = new ConcurrentSkipListSet<>();
	
	public WebSocketClientHandler( URI serverUri , Draft draft ) { 
 		super( serverUri, draft ); 
 	}  
 
 	public WebSocketClientHandler( URI serverURI ) { 
 		super( serverURI ); 
 	}  
 
 	@Override 
 	public void onOpen( ServerHandshake handshakedata ) { 
 		LOGGER.info("opened connection"); 
 		if(handshakedata == null){
 			LOGGER.info("handshakedata为null"); 
 		}else{
 			LOGGER.info("handshakedata："); 
 	 		for(Iterator<String> it=handshakedata.iterateHttpFields();it.hasNext();) {
 	            String key = it.next();
 	            //不要使用 System.out.println()，如有必要，用logger.debug()替代(key+":"+handshakedata.getFieldValue(key));
 	        }
 		}
 	}  
 
 	/**
 	 * push平台处理下发处理消息
 	 */
	@Override 
 	public void onMessage( String message ) { 
 		//LOGGER.debug("onMessage:"+message);
 		if(message==null || "".equals(message)){
 			LOGGER.info("消息为空");
 		}else{
 			// 判断是否为ping消息
 			if(message.substring(0,4).equals(MSG_PING)){
 				String[] pingInfo = message.split("_");
 				if(pingInfo != null && pingInfo.length==2){
 					Constant.msgMap.put(pingInfo[1],MSG_RETURN);
 				}	
 				return;
 			}

 			LOGGER.info("received: " + message);
 			String  divsion= PosPropertyUtil.getMsg("websocket.divsion");
 			if(message.contains(divsion)){
 	 	 		String[] str = message.split(divsion);	// 拆分消息
 	 	 		if(str != null && str.length ==2){
 	 	 			this.send(str[0]);// 向消息推送平台回复消息
 	 	 		}
 	 	 		message = str[1];
 			}

            if(UNIQUE_CODE_SET.size() >= 500)
            {
                UNIQUE_CODE_SET.clear();
            }

 			JSONObject getJson = null;
 			if(message!=null){
 	 			try {
 	 				getJson = JSONObject.fromObject(message);
 	 				String sy = getJson.optString("s"); //处理方式
 					if("1".equals(sy)){//异步处理
 						LOGGER.info("异步处理消息");

                        if(isContainUniqueCode(getJson))
                        {
                            return;
                        }

 	 					ConsumerThreadPoolUtil.getConsumerThreadPool().execute(new PushPosConsumerThreadPoolTask(message));
 					}else{//同步处理消息  默认同步处理
 	 					LOGGER.info("同步处理消息");
 	 					new PushProcessMessage().doExecute(message);
 					}
 	 			}catch (Exception e) {
 	 				//e.printStackTrace();
 	 			}
 	 	 		
 			}

 		}
 	}

    /**
     * 推送平台发送的消息是否重复（只判断云POS）
     * @param getJson
     * @return
     */
 	private boolean isContainUniqueCode(JSONObject getJson)
    {
        try
        {
            String d = getJson.optString("d");
            JSONObject dJson = JSONObject.fromObject(d);
            if(dJson != null && !dJson.isEmpty())
            {
                String type = dJson.optString("type");
                String oper = dJson.optString("oper");
                String para_value = dJson.optString("para_value");
                if("DATA_DELIVERY".equals(type) && "data_fetch".equals(oper) && "11".equals(para_value))
                {
                    String unique_code = dJson.optString("unique_code");
                    if(!UNIQUE_CODE_SET.add(unique_code))
                    {
                        return true;
                    }
                }
            }
        }
        catch (Exception e)
        {
            LOGGER.error("boh处理云POS推送消息异常：", e);
        }

        return false;
    }

    @Override
 	public void onFragment( Framedata fragment ) {
 		LOGGER.info("received fragment: " + new String( fragment.getPayloadData().array() )); 
 	}  
 
 	@Override 
 	public void onClose( int code, String reason, boolean remote ) {  
 		LOGGER.info("Connection closed by " + ( remote ? "remote peer" : "us" ));	
 	}  
 
 	@Override 
 	public void onError( Exception ex ) { 
 		LOGGER.info("Connection Error： "+ex.toString());	
 	} 
}
