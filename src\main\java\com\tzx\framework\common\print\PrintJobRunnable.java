package com.tzx.framework.common.print;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.print.Doc;
import javax.print.DocFlavor;
import javax.print.DocPrintJob;
import javax.print.PrintService;
import javax.print.SimpleDoc;
import javax.print.attribute.DocAttributeSet;
import javax.print.attribute.HashDocAttributeSet;
import javax.print.attribute.HashPrintRequestAttributeSet;
import javax.print.attribute.PrintRequestAttributeSet;
import javax.print.attribute.standard.MediaPrintableArea;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.apache.log4j.Logger;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;

import com.tzx.framework.common.constant.Constant;
import com.tzx.framework.common.newprint.NewPrintJob;
import com.tzx.framework.common.print.template.PrintTemplate_HTML;
import com.tzx.framework.common.util.DateUtil;
import com.tzx.framework.common.util.CommonUtil;
import com.tzx.framework.common.util.SpringConext;
import com.tzx.framework.common.util.dao.GenericDao;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;

public class PrintJobRunnable implements Runnable
{

	private static final Logger	logger	= Logger.getLogger(PrintJobRunnable.class);

	@Override
	public void run()
	{
		try
		{
			Map<String, String> systemMap = Constant.getSystemMap();
			String tenancyId = systemMap.get("tenent_id");
			DBContextHolder.setTenancyid(tenancyId);
			GenericDao dao = (GenericDao) SpringConext.getApplicationContext().getBean("genericDaoImpl");
			//检查一次是否启用新打印机参数
			if(Constant.DEFAULT_PRINT_TYPE_CHECK)
			{
				List<JSONObject> listnewprint = dao.query4Json(tenancyId, "SELECT para_value FROM sys_parameter	WHERE para_code = 'new_printer_type' ");
				if(listnewprint.size()>0)
				{
					Constant.DEFAULT_PRINT_TYPE = listnewprint.get(0).optInt("para_value");
				}
				Constant.DEFAULT_PRINT_TYPE_CHECK = false;
			}
			
			if(!"1".equals(Constant.INIT_BASIC_DATA_STATE))
			{
				switch (Constant.DEFAULT_PRINT_TYPE)
				{
					// 默认打印
					case 0:
						newPrint(dao);
						break;
				    // 新打印JAVA
					case 1:
						NewPrintJob newprint = new NewPrintJob(tenancyId,dao);
						newprint.print();
						break;
				    // 新打印 --
					case 2:
					
						break;	
					default:
						break;
				}
			}
			else
			{
				logger.info("正在初始化数据.................");
			}
		}
		catch (Throwable e)
		{
			//e.printStackTrace();
		}
	}

	public void newPrint(GenericDao dao) throws Exception
	{
		Map<String, String> systemMap = Constant.getSystemMap();
		String tenantId = systemMap.get("tenent_id");
		Integer storeId = Integer.valueOf(systemMap.get("store_id"));
		
		String qPrintC = "select tenancy_id,store_id,id,print_format,printer_id,bill_num,printer_name,print_cont from pos_print where print_tag='*' and (print_cont <=5 or print_cont is null) and tenancy_id='" + tenantId + "' and store_id=" + storeId + " order by id asc limit 20 offset 0";
		List<JSONObject> conditionList = dao.query4Json(systemMap.get("tenent_id"), qPrintC);
		for (JSONObject condition : conditionList)
		{
			logger.info("进入newPrint打印方法-打印参数："+condition.toString());
			String printFormat = condition.optString("print_format");
			String printName = condition.optString("printer_name");
			
			Integer printTaskId = condition.optInt("id");
			try
			{
				String uPitem = new String("update pos_print set print_cont = ? where id = ?");
				dao.getJdbcTemplate(tenantId).update(uPitem, new Object[]
				{ condition.optInt("print_cont")+1, printTaskId });
				
				if (CommonUtil.hv(printName))
				{
					// 根据打印类型查询模板信息
					String sql = "select id,page_size,trim(param_pairs) as param_pairs,content from sys_print_format where class_item_code='" + printFormat + "' and valid_state='1'";

					List<JSONObject> list = dao.query4Json(tenantId, sql);

					if (list.size() > 0)
					{
						int id = list.get(0).optInt("id");
						int pageSize = list.get(0).optInt("page_size");
						JSONObject paramPairs = JSONObject.fromObject(list.get(0).get("param_pairs"));
						JSONArray rows = paramPairs.optJSONArray("rows");
						String content = list.get(0).optString("content");

						sql = "select result_set_title,sql from sys_print_format_detail where parent_id=" + id;
						list = dao.query4Json(tenantId, sql);

						List<JSONObject> param = new ArrayList<JSONObject>();

						for (JSONObject json : list)
						{
							JSONObject p = new JSONObject();
							p.put("result_set_title", json.optString("result_set_title"));

							String testSql = json.optString("sql").trim();

							List<Object> values = new ArrayList<Object>();// 参数列表

							if (testSql.substring(0, 6).equalsIgnoreCase("select"))
							{
								StringBuilder sb = new StringBuilder(testSql);

								int i = 0;
								String temp = "";

								while (true)
								{
									i = sb.indexOf(":");
									int j = 0;

									if (i > 0)
									{
										temp = sb.substring(i, sb.length());

										j = temp.indexOf(" ");

										if (j < 0)
										{
											j = temp.length();
										}

										String t = temp.substring(1, j);

										Object value = null;
										for (Object row : rows)
										{
											JSONObject r = JSONObject.fromObject(row);

											if (t.equals(r.optString("paramName")))
											{

												if ("test".equals(condition.optString("printmode")))
												{
													value = r.optString("value");
												}
												else
												{
													value = condition.get(t); // 返回object类型
												}
												// 这块判断日期格式，不加的话，有日期参数就查不出来内容，报类型错误
												if (CommonUtil.isNullOrEmpty(value) == false && DateUtil.isDate(value.toString()))
												{
													Date vDate = DateUtil.parseDate(value.toString());
													values.add(vDate);
												}
												else
												{
													values.add(value);
												}
												break;
											}
										}

										sb.replace(i, i + j, "?");
									}
									else
									{
										break;
									}
								}
								Object[] objs = values.toArray();
								p.put("result", dao.query4JSONArray(tenantId, sb.toString(), objs));
							}
							param.add(p);
						}

						PrintTemplate_HTML html = new PrintTemplate_HTML();

						String doc = html.parse(pageSize, content, param);

						 logger.info("进入newPrint打印方法-打印文档："+doc);

						print(printName, pageSize, html, Jsoup.parse(doc), 1, printTaskId);
					}
					else
					{
						logger.error("打印失败：厨打任务ID 为 【" + printTaskId + "】" + "未找到打印模板【" + printFormat + "】");
						throw new Exception("未找到打印模板【" + printFormat + "】");
					}
				}
				else
				{
					logger.error("打印失败：厨打任务ID 为 【" + printTaskId + "】" + "未配置打印机");
					throw new Exception("未配置打印机");
				}
			}
			catch (Exception e)
			{
				logger.error("打印失败：厨打任务ID 为 【" + printTaskId + "】" + e);
			}
		}
	}

	public void print(String printerName, float width, PrintTemplate_HTML html, Document dom, int copies, int taskId) throws Exception
	{
		List<PrintLine> printLineList = html.setupPrintLineList(dom);
		float page_height = html.computePrintHeight(printLineList);

		PrintableImpl printableImpl = new PrintableImpl(printLineList);

		DocAttributeSet attr = new HashDocAttributeSet();

		attr.add(new MediaPrintableArea(0, 0, (float) (width / 25.4 * 72), page_height, MediaPrintableArea.MM));

		Doc doc = new SimpleDoc(printableImpl, DocFlavor.SERVICE_FORMATTED.PRINTABLE, attr);

		PrintService service = PrintHelperNew.getPrintService(printerName);

		for (int i = 0; i < copies; i++)
		{
			DocPrintJob printJob = service.createPrintJob();
			printJob.addPrintJobListener(new PrintJobAdapterHandler(taskId));

			PrintRequestAttributeSet pras = new HashPrintRequestAttributeSet();
			
			printJob.print(doc, pras);
		}
		
		logger.info("进入newPrint打印方法-打印成功：");
	}

}
