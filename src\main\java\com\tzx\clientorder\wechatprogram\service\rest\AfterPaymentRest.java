package com.tzx.clientorder.wechatprogram.service.rest;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import com.tzx.clientorder.wechatprogram.common.constant.PromptConstant;
import com.tzx.clientorder.wechatprogram.runnable.UploadOrderRunnable;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import com.tzx.clientorder.common.util.ExceptionPrintUtil;
import com.tzx.clientorder.wechatprogram.bo.AfterPaymentService;
import com.tzx.clientorder.wechatprogram.common.constant.PromptMsgConstant;
import com.tzx.framework.common.constant.Constant;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.exception.OtherSystemException;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.exception.WxErrorCode;
import com.tzx.pos.asyn.pool.ThreadPool;
import com.tzx.pos.asyn.thread.OrderingPrintThread;
import com.tzx.pos.base.util.ParamUtil;

import net.sf.json.JSONObject;

/**
 * 微生活小程序调用接口（后付业务）
 */

@Controller("PromptAfterPaymentRest")
@RequestMapping("/order/api")
public class AfterPaymentRest
{
	private static final Logger	logger	= Logger.getLogger(AfterPaymentRest.class);

	@Resource(name = AfterPaymentService.NAME)
	private AfterPaymentService	afterPaymentService;

	/**
	 * 查询桌台是否有订单
	 *
	 * @param request
	 * @param jsobj
	 */
	@RequestMapping(value = "/tableStatus", method = RequestMethod.POST)
	@ResponseBody
	public JSONObject getTableOrder(HttpServletRequest request, @RequestBody JSONObject jsobj)
	{
		logger.info("查询桌台请求入参===" + jsobj.toString());

		// 拉取桌台的订单信息
		JSONObject responseJson = null;
		try
		{
			String tenancyId = null;
			if (Constant.systemMap.containsKey("tenent_id"))
			{
				tenancyId = Constant.systemMap.get("tenent_id");
			}
			Integer storeId = null;
			if (Constant.systemMap.containsKey("store_id"))
			{
				storeId = Integer.valueOf(Constant.systemMap.get("store_id"));
			}

			if (StringUtils.isEmpty(tenancyId) || storeId == 0)
			{
				throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
			}
			String orderChannel = ParamUtil.getStringValueByObject(jsobj, "request_source", true, WxErrorCode.NOT_NULL_REQUEST_SOURCE_ERROR);

			responseJson = afterPaymentService.getTableOrder(tenancyId, storeId, jsobj, orderChannel);
		}
		catch (OtherSystemException se)
		{
			responseJson = new JSONObject();
			ExceptionPrintUtil.buildProgramOtherSysExceptionData(logger, se, responseJson, PromptMsgConstant.QUERY_ORDER_FAILURE);
		}
		catch (SystemException se)
		{
			responseJson = new JSONObject();
			ExceptionPrintUtil.buildProgramSysExceptionData(logger, se, responseJson, PromptMsgConstant.QUERY_ORDER_FAILURE);
		}
		catch (Exception e)
		{
			responseJson = new JSONObject();
			ExceptionPrintUtil.buildProgramExceptionData(logger, e, responseJson, PromptMsgConstant.QUERY_ORDER_FAILURE);
		}

		responseJson.put("request_id", ParamUtil.getStringValueByObject(jsobj, "request_id"));
		logger.info("查询桌台响应出参===" + responseJson.toString());
		return responseJson;
	}

	/**
	 * 后付下单接口
	 *
	 * @param request
	 * @param jsobj
	 */
	@RequestMapping(value = "/order", method = RequestMethod.POST)
	@ResponseBody
	public JSONObject ordering(HttpServletRequest request, @RequestBody JSONObject jsobj)
	{
		logger.info("ordering 请求入参===" + jsobj.toString());

		JSONObject responseJson = null;
		try
		{
			String tenancyId = null;
			if (Constant.systemMap.containsKey("tenent_id"))
			{
				tenancyId = Constant.systemMap.get("tenent_id");
			}
			Integer storeId = null;
			if (Constant.systemMap.containsKey("store_id"))
			{
				storeId = Integer.valueOf(Constant.systemMap.get("store_id"));
			}

			if (StringUtils.isEmpty(tenancyId) || storeId == 0)
			{
				throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
			}
			String orderChannel = ParamUtil.getStringValueByObject(jsobj, "request_source", true, WxErrorCode.NOT_NULL_REQUEST_SOURCE_ERROR);
			JSONObject printJson = new JSONObject();
			responseJson = afterPaymentService.saveOrUpdateOrder(tenancyId, storeId, jsobj, printJson,orderChannel);

			//执行成功上传
			if("1".equals(responseJson.optString(PromptConstant.SUCCESS))) {
				String bill_num = responseJson.optJSONObject("data").optJSONObject("order_info").optString("billnum");
				//异步上传订单接口
				UploadOrderRunnable uploadOrderRunnable = new UploadOrderRunnable(tenancyId, storeId, bill_num, orderChannel, PromptConstant.UPLOADORDER_OPER_ADD,"","","");
				ThreadPool.getUploadThreadPool().execute(uploadOrderRunnable);
			}

			// 异步执行分单打印
			Data data = new Data();
			data.setTenancy_id(tenancyId);
			data.setStore_id(storeId);
			OrderingPrintThread orderingPrintThread = new OrderingPrintThread(printJson, data);
			ThreadPool.getPrintThreadPool().execute(orderingPrintThread);
		}
		catch (OtherSystemException se)
		{
			responseJson = new JSONObject();
			ExceptionPrintUtil.buildProgramOtherSysExceptionData(logger, se, responseJson, PromptMsgConstant.ORDER_DISH_FAILURE);
		}
		catch (SystemException se)
		{
			responseJson = new JSONObject();
			ExceptionPrintUtil.buildProgramSysExceptionData(logger, se, responseJson, PromptMsgConstant.ORDER_DISH_FAILURE);
		}
		catch (Exception e)
		{
			logger.error(" ordering failied ...  order_id" + jsobj.optString("order_id"), e);
			responseJson = new JSONObject();
			ExceptionPrintUtil.buildProgramExceptionData(logger, e, responseJson, PromptMsgConstant.ORDER_DISH_FAILURE);
		}

		responseJson.put("request_id", ParamUtil.getStringValueByObject(jsobj, "request_id"));
		logger.info("ordering 响应出参===" + responseJson.toString());
		return responseJson;
	}

	/**
	 * 支付上传订单接口
	 *
	 * @param request
	 * @param jsobj
	 * @return
	 */
	@RequestMapping(value = "/queryOrders", method = RequestMethod.POST)
	@ResponseBody
	public JSONObject payUploadBill(HttpServletRequest request, @RequestBody JSONObject jsobj)
	{
		logger.info("payUploadBill 请求入参===" + jsobj.toString());

		JSONObject responseJson = null;
		try
		{
			String tenancyId = null;
			if (Constant.systemMap.containsKey("tenent_id"))
			{
				tenancyId = Constant.systemMap.get("tenent_id");
			}
			Integer storeId = null;
			if (Constant.systemMap.containsKey("store_id"))
			{
				storeId = Integer.valueOf(Constant.systemMap.get("store_id"));
			}

			if (StringUtils.isEmpty(tenancyId) || storeId == 0)
			{
				throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
			}
			String orderChannel = ParamUtil.getStringValueByObject(jsobj, "request_source", true, WxErrorCode.NOT_NULL_REQUEST_SOURCE_ERROR);
			responseJson = afterPaymentService.payUploadBill(tenancyId, storeId, jsobj, orderChannel);
		}
		catch (OtherSystemException se)
		{
			responseJson = new JSONObject();
			ExceptionPrintUtil.buildProgramOtherSysExceptionData(logger, se, responseJson, PromptMsgConstant.UPLOAD_BILL_FAILURE);
		}
		catch (SystemException se)
		{
			responseJson = new JSONObject();
			ExceptionPrintUtil.buildProgramSysExceptionData(logger, se, responseJson, PromptMsgConstant.UPLOAD_BILL_FAILURE);
		}
		catch (Exception e)
		{
			logger.error(" payUploadBill failed ...  order_id" + jsobj.optString("order_id"), e);
			responseJson = new JSONObject();
			ExceptionPrintUtil.buildProgramExceptionData(logger, e, responseJson, PromptMsgConstant.UPLOAD_BILL_FAILURE);
		}

		responseJson.put("request_id", ParamUtil.getStringValueByObject(jsobj, "request_id"));
		logger.info("payUploadBill 响应出参===" + responseJson.toString());
		return responseJson;
	}

	/**
	 * 结账接口
	 *
	 * @param request
	 * @param jsobj
	 * @return
	 */
	@RequestMapping(value = "/payOrder", method = RequestMethod.POST)
	@ResponseBody
	public JSONObject billPayment(HttpServletRequest request, @RequestBody JSONObject jsobj)
	{
		logger.info("billPayment 请求入参===" + jsobj.toString());

		JSONObject responseJson = null;
		try
		{
			String tenancyId = null;
			if (Constant.systemMap.containsKey("tenent_id"))
			{
				tenancyId = Constant.systemMap.get("tenent_id");
			}
			Integer storeId = null;
			if (Constant.systemMap.containsKey("store_id"))
			{
				storeId = Integer.valueOf(Constant.systemMap.get("store_id"));
			}

			if (StringUtils.isEmpty(tenancyId) || storeId == 0)
			{
				throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
			}
			String orderChannel = ParamUtil.getStringValueByObject(jsobj, "request_source", true, WxErrorCode.NOT_NULL_REQUEST_SOURCE_ERROR);
			responseJson = afterPaymentService.paymentClose(tenancyId, storeId, jsobj,orderChannel);
		}
		catch (OtherSystemException se)
		{
			responseJson = new JSONObject();
			ExceptionPrintUtil.buildProgramOtherSysExceptionData(logger, se, responseJson, PromptMsgConstant.ORDER_PAYMENT_FAILURE);
		}
		catch (SystemException se)
		{
			responseJson = new JSONObject();
			ExceptionPrintUtil.buildProgramSysExceptionData(logger, se, responseJson, PromptMsgConstant.ORDER_PAYMENT_FAILURE);
		}
		catch (Exception e)
		{
			logger.error(" billPayment failied ...  order_id" + jsobj.optString("order_id"), e);
			responseJson = new JSONObject();
			ExceptionPrintUtil.buildProgramExceptionData(logger, e, responseJson, PromptMsgConstant.ORDER_PAYMENT_FAILURE);
		}

		responseJson.put("request_id", ParamUtil.getStringValueByObject(jsobj, "request_id"));
		logger.info("billPayment 响应出参===" + responseJson.toString());
		return responseJson;
	}

	/**
	 * 锁单接口
	 *
	 * @param request
	 * @param jsobj
	 * @return
	 */
	@RequestMapping(value = "/lockOrder", method = RequestMethod.POST)
	@ResponseBody
	public JSONObject lockOrder(HttpServletRequest request, @RequestBody JSONObject jsobj)
	{
		logger.info("lockOrder 请求入参===" + jsobj.toString());

		JSONObject responseJson = null;
		try
		{
			String tenancyId = null;
			if (Constant.systemMap.containsKey("tenent_id"))
			{
				tenancyId = Constant.systemMap.get("tenent_id");
			}
			Integer storeId = null;
			if (Constant.systemMap.containsKey("store_id"))
			{
				storeId = Integer.valueOf(Constant.systemMap.get("store_id"));
			}

			if (StringUtils.isEmpty(tenancyId) || storeId == 0)
			{
				throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
			}

			responseJson = afterPaymentService.lockOrder(tenancyId, storeId, jsobj);
		}
		catch (OtherSystemException se)
		{
			responseJson = new JSONObject();
			ExceptionPrintUtil.buildProgramOtherSysExceptionData(logger, se, responseJson, PromptMsgConstant.LOCK_ORDER_FAILURE);
		}
		catch (SystemException se)
		{
			responseJson = new JSONObject();
			ExceptionPrintUtil.buildProgramSysExceptionData(logger, se, responseJson, PromptMsgConstant.LOCK_ORDER_FAILURE);
		}
		catch (Exception e)
		{
			logger.error(" lockOrder failied ...  order_id" + jsobj.optString("order_id"), e);
			responseJson = new JSONObject();
			ExceptionPrintUtil.buildProgramExceptionData(logger, e, responseJson, PromptMsgConstant.LOCK_ORDER_FAILURE);
		}

		responseJson.put("request_id", ParamUtil.getStringValueByObject(jsobj, "request_id"));
		logger.info("lockOrder 响应出参===" + responseJson.toString());
		return responseJson;
	}

	/**
	 * 解锁接口
	 *
	 * @param request
	 * @param jsobj
	 * @return
	 */
	@RequestMapping(value = "/unlockOrder", method = RequestMethod.POST)
	@ResponseBody
	public JSONObject unlockOrder(HttpServletRequest request, @RequestBody JSONObject jsobj)
	{
		logger.info("unlockOrder 请求入参===" + jsobj.toString());

		JSONObject responseJson = null;
		try
		{
			String tenancyId = null;
			if (Constant.systemMap.containsKey("tenent_id"))
			{
				tenancyId = Constant.systemMap.get("tenent_id");
			}
			Integer storeId = null;
			if (Constant.systemMap.containsKey("store_id"))
			{
				storeId = Integer.valueOf(Constant.systemMap.get("store_id"));
			}

			if (StringUtils.isEmpty(tenancyId) || storeId == 0)
			{
				throw SystemException.getInstance(PosErrorCode.PARAM_ERROR);
			}

			responseJson = afterPaymentService.unlockOrder(tenancyId, storeId, jsobj);
		}
		catch (OtherSystemException se)
		{
			responseJson = new JSONObject();
			ExceptionPrintUtil.buildProgramOtherSysExceptionData(logger, se, responseJson, PromptMsgConstant.UNLOCK_ORDER_FAILURE);
		}
		catch (SystemException se)
		{
			responseJson = new JSONObject();
			ExceptionPrintUtil.buildProgramSysExceptionData(logger, se, responseJson, PromptMsgConstant.UNLOCK_ORDER_FAILURE);
		}
		catch (Exception e)
		{
			logger.error(" unlockOrder failied ...  order_id" + jsobj.optString("out_order_id"), e);
			responseJson = new JSONObject();
			ExceptionPrintUtil.buildProgramExceptionData(logger, e, responseJson, PromptMsgConstant.UNLOCK_ORDER_FAILURE);
		}

		responseJson.put("request_id", ParamUtil.getStringValueByObject(jsobj, "request_id"));
		logger.info("unlockOrder 响应出参===" + responseJson.toString());
		return responseJson;
	}
}
