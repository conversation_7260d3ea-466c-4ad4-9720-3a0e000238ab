package com.tzx.clientorder.wechatprogram.common.entity.program;

/** 会员信息
 * <AUTHOR>
 *
 */
public class MemberEntity
{
	private String	openid;
	private String	cno;
	private String	name;
	private String	mobile;
	private String	sex;
	private String	birthday;
	private String	grade;
	private String	grade_name;
	private Double	credit;
	private Double	balance;
	private Double	discount_rate	= 100d;
	private String	is_vipprice		= "0";
	
	private Double	beforeCredit;
	private Double	beforeBalance;
	private String	customerType;
	
	public String getOpenid()
	{
		return openid;
	}

	public void setOpenid(String openid)
	{
		this.openid = openid;
	}

	public String getCno()
	{
		return cno;
	}

	public void setCno(String cno)
	{
		this.cno = cno;
	}

	public String getName()
	{
		return name;
	}

	public void setName(String name)
	{
		this.name = name;
	}

	public String getMobile()
	{
		return mobile;
	}

	public void setMobile(String mobile)
	{
		this.mobile = mobile;
	}

	public String getSex()
	{
		return sex;
	}

	public void setSex(String sex)
	{
		this.sex = sex;
	}

	public String getBirthday()
	{
		return birthday;
	}

	public void setBirthday(String birthday)
	{
		this.birthday = birthday;
	}

	public String getGrade()
	{
		return grade;
	}

	public void setGrade(String grade)
	{
		this.grade = grade;
	}

	public String getGrade_name()
	{
		return grade_name;
	}

	public void setGrade_name(String grade_name)
	{
		this.grade_name = grade_name;
	}

	public Double getCredit()
	{
		return ((null != credit && !credit.isNaN()) ? credit : 0d);
	}

	public void setCredit(Double credit)
	{
		this.credit = credit;
	}

	public Double getBalance()
	{
		return ((null != balance && !balance.isNaN()) ? balance : 0d);
	}

	public void setBalance(Double balance)
	{
		this.balance = balance;
	}

	public Double getDiscount_rate()
	{
		return discount_rate;
	}

	public void setDiscount_rate(Double discount_rate)
	{
		this.discount_rate = discount_rate;
	}

	public String getIs_vipprice()
	{
		return is_vipprice;
	}

	public void setIs_vipprice(String is_vipprice)
	{
		this.is_vipprice = is_vipprice;
	}

	public Double getBeforeCredit()
	{
		return beforeCredit;
	}

	public void setBeforeCredit(Double beforeCredit)
	{
		this.beforeCredit = beforeCredit;
	}

	public Double getBeforeBalance()
	{
		return beforeBalance;
	}

	public void setBeforeBalance(Double beforeBalance)
	{
		this.beforeBalance = beforeBalance;
	}

	public String getCustomerType()
	{
		return customerType;
	}

	public void setCustomerType(String customerType)
	{
		this.customerType = customerType;
	}
}
