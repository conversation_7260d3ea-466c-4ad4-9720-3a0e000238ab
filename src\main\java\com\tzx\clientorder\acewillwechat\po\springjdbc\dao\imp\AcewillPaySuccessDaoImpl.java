package com.tzx.clientorder.acewillwechat.po.springjdbc.dao.imp;

import java.util.ArrayList;
import java.util.List;

import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import com.tzx.member.common.entity.CrmCardTradingListEntity;
import com.tzx.pos.base.dao.imp.BaseDaoImp;
import com.tzx.clientorder.common.entity.AcewillPaymentWay;
import com.tzx.clientorder.common.entity.AcewillPosBillMember;
import com.tzx.clientorder.common.entity.AcewillPosBillPayment;
import com.tzx.clientorder.common.entity.AcewillPosBillPaymentCoupons;
import com.tzx.clientorder.acewillwechat.po.springjdbc.dao.AcewillPaySucessDao;

import net.sf.json.JSONObject;
@Repository(AcewillPaySucessDao.NAME)
public class AcewillPaySuccessDaoImpl extends BaseDaoImp implements AcewillPaySucessDao{

	@Override
	public JSONObject getPosBillByBillNum(String tenantId,String billNum) throws Exception {
		if(!StringUtils.isEmpty(billNum)) {
			StringBuilder sql = new StringBuilder();
			sql.append("select tenancy_id,store_id,id,bill_num,batch_num,serial_num,report_date,table_code,guest,opentable_time,payment_time,payment_num,open_pos_num,pos_num,waiter_num,open_opt,\n" +
					"cashier_num,shift_id,item_menu_id,service_id,service_amount,service_discount,order_num,print_time,print_count,subtotal,bill_amount,payment_amount,difference,\n" +
					"discountk_amount,discountr_amount,maling_amount,single_discount_amount,discount_amount,free_amount,givi_amount,more_coupon,average_amount,discount_num,discount_case_id,\n" +
					"discount_rate,billfree_reason_id,discount_mode_id,transfer_remark,sale_mode,bill_state,bill_property,upload_tag,deposit_count,copy_bill_num,source,opt_login_number,\n" +
					"guest_msg,integraloffset,remark,return_amount,advance_payment_amt,advance_refund_amt,is_refund,payment_id,pay_no,third_bill_code,payment_state,payment_manager_num,\n" +
					"shop_real_amount,total_fees,platform_charge_amount,settlement_type,bill_taste,fictitious_table,recover_count,tax_rate,tax_money,service_tax_rate,service_tax_money,\n" +
					"tax_amount,no_tax_amount,payment_tax_money,payment_notax,bill_tax_money,bill_notax,service_notax,settlement_price,discount_reason_id,dinner_type,coupon_buy_price,due,\n" +
					"tenancy_assume,third_assume,third_fee,item_discountr_amount,service_discountr_amount,order_source,payment_source from pos_bill where bill_num ='").append(billNum).append("'");
			List<JSONObject> query4Json = this.query4Json(tenantId, sql.toString());
			if(query4Json.size()>0) {
				return query4Json.get(0);
			}
		}
		return null;
	}
	@SuppressWarnings("unchecked")
	@Override
	public List<AcewillPaymentWay> findPaymentWay(String tenantId, Integer storeId,List<String> paymentClassList) throws Exception {
		String paymentClasses = StringUtils.collectionToDelimitedString(paymentClassList, ",", "'", "'");
		if(storeId!=null&&!StringUtils.isEmpty(paymentClasses)) {
			StringBuilder sql = new StringBuilder();
			sql.append("select pw.* from payment_way pw LEFT JOIN payment_way_of_ogran pwo on pw.id = pwo.payment_id where pwo.organ_id=")
			.append(storeId)
			.append(" and pw.payment_class in (")
			.append(paymentClasses)
			.append(")");
			return (List<AcewillPaymentWay>) this.query(tenantId, sql.toString(), AcewillPaymentWay.class);
		}
		return null;
	}
	@Override
	public void savePosBillPayment(List<AcewillPosBillPayment> list) throws Exception {
		if(list==null||list.size()<=0) {
			return;
		}
		StringBuilder sql = new StringBuilder();
		sql.append("INSERT INTO pos_bill_payment ( tenancy_id, store_id, bill_num, table_code, type, jzid, name, name_english, amount, count, number, phone, report_date, shift_id, pos_num, cashier_num, last_updatetime, is_ysk, rate, currency_amount, upload_tag, customer_id, bill_code, remark, payment_state, param_cach, batch_num, more_coupon, fee, fee_rate, coupon_type, yjzid, coupon_buy_price, due, tenancy_assume, third_assume, third_fee,payment_uid ) VALUES");
		for (int i = 0; i < list.size(); i++) {
			if(i!=0) {
				sql.append(",");
			}
			sql.append("(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");
		}
		List<Object> paramList = new ArrayList<>();
		for (AcewillPosBillPayment posBillPayment : list) {
			paramList.add(posBillPayment.getTenancy_id());
			paramList.add(posBillPayment.getStore_id());
			paramList.add(posBillPayment.getBill_num());
			paramList.add(posBillPayment.getTable_code());
			paramList.add(posBillPayment.getType());
			paramList.add(posBillPayment.getJzid());
			paramList.add(posBillPayment.getName());
			paramList.add(posBillPayment.getName_english());
			paramList.add(posBillPayment.getAmount());
			paramList.add(posBillPayment.getCount());
			paramList.add(posBillPayment.getNumber());
			paramList.add(posBillPayment.getPhone());
			paramList.add(posBillPayment.getReport_date());
			paramList.add(posBillPayment.getShift_id());
			paramList.add(posBillPayment.getPos_num());
			paramList.add(posBillPayment.getCashier_num());
			paramList.add(posBillPayment.getLast_updatetime());
			paramList.add(posBillPayment.getIs_ysk());
			paramList.add(posBillPayment.getRate());
			paramList.add(posBillPayment.getCurrency_amount());
			paramList.add(posBillPayment.getUpload_tag());
			paramList.add(posBillPayment.getCustomer_id());
			paramList.add(posBillPayment.getBill_code());
			paramList.add(posBillPayment.getRemark());
			paramList.add(posBillPayment.getPayment_state());
			paramList.add(posBillPayment.getParam_cach());
			paramList.add(posBillPayment.getBatch_num());
			paramList.add(posBillPayment.getMore_coupon());
			paramList.add(posBillPayment.getFee());
			paramList.add(posBillPayment.getFee_rate());
			paramList.add(posBillPayment.getCoupon_type());
			paramList.add(posBillPayment.getYjzid());
			paramList.add(posBillPayment.getCoupon_buy_price());
			paramList.add(posBillPayment.getDue());
			paramList.add(posBillPayment.getTenancy_assume());
			paramList.add(posBillPayment.getThird_assume());
			paramList.add(posBillPayment.getThird_fee());
			paramList.add(posBillPayment.getPayment_uid());
		}
		this.update(sql.toString(),paramList.toArray());
	}
	@Override
	public void savePosBillMember(List<AcewillPosBillMember> list) throws Exception {
		if(list==null||list.size()<=0) {
			return;
		}
		StringBuilder sql = new StringBuilder();
		sql.append("INSERT INTO pos_bill_member ( tenancy_id, store_id, bill_num, report_date, type, amount, credit, card_code, mobil, last_updatetime, upload_tag, remark, bill_code, request_state, customer_code, customer_name, consume_before_credit, consume_after_credit, consume_before_main_balance, consume_before_reward_balance, consume_after_main_balance, consume_after_reward_balance ) VALUES");
		for (int i = 0; i < list.size(); i++) {
			if(i!=0) {
				sql.append(",");
			}
			sql.append("(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");
		}
		List<Object> paramList = new ArrayList<>();
		for (AcewillPosBillMember posBillMember : list) {
			paramList.add(posBillMember.getTenancy_id());
			paramList.add(posBillMember.getStore_id());
			paramList.add(posBillMember.getBill_num());
			paramList.add(posBillMember.getReport_date());
			paramList.add(posBillMember.getType());
			paramList.add(posBillMember.getAmount());
			paramList.add(posBillMember.getCredit());
			paramList.add(posBillMember.getCard_code());
			paramList.add(posBillMember.getMobil());
			paramList.add(posBillMember.getLast_updatetime());
			paramList.add(posBillMember.getUpload_tag());
			paramList.add(posBillMember.getRemark());
			paramList.add(posBillMember.getBill_code());
			paramList.add(posBillMember.getRequest_state());
			paramList.add(posBillMember.getCustomer_code());
			paramList.add(posBillMember.getCustomer_name());
			paramList.add(posBillMember.getConsume_before_credit());
			paramList.add(posBillMember.getConsume_after_credit());
			paramList.add(posBillMember.getConsume_before_main_balance());
			paramList.add(posBillMember.getConsume_before_reward_balance());
			paramList.add(posBillMember.getConsume_after_main_balance());
			paramList.add(posBillMember.getConsume_after_reward_balance());
		}
		this.update(sql.toString(),paramList.toArray());
	}
	@Override
	public void saveCrmCardTradingList(List<CrmCardTradingListEntity> list) throws Exception {
		if(list==null||list.size()<=0) {
			return;
		}
		StringBuilder sql = new StringBuilder();
		sql.append("INSERT INTO crm_card_trading_list ( tenancy_id,card_id, card_code, bill_code, chanel, store_id, business_date, main_trading, reward_trading, operat_type, main_original, reward_original, deposit, operator, operate_time, bill_money, third_bill_code, bill_code_original, activity_id, customer_id, revoked_trading, batch_num, last_updatetime, store_updatetime, card_class_id, name, mobil, operator_id, shift_id, total_balance, reward_balance, main_balance, pay_type, salesman, commission_saler_money, commission_store_money, invoice_balance, is_invoice, payment_state, recharge_state, request_status, request_code, request_msg )values");
		for (int i = 0; i < list.size(); i++) {
			if(i!=0) {
				sql.append(",");
			}
			sql.append("(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");
		}
		List<Object> paramList = new ArrayList<>();
		for (CrmCardTradingListEntity crmCardTradingList : list) {
			paramList.add(crmCardTradingList.getTenancy_id());
			paramList.add(crmCardTradingList.getCard_id());
			paramList.add(crmCardTradingList.getCard_code());
			paramList.add(crmCardTradingList.getBill_code());
			paramList.add(crmCardTradingList.getChanel());
			paramList.add(crmCardTradingList.getStore_id());
			paramList.add(crmCardTradingList.getBusiness_date());
			paramList.add(crmCardTradingList.getMain_trading());
			paramList.add(crmCardTradingList.getReward_trading());
			paramList.add(crmCardTradingList.getOperat_type());
			paramList.add(crmCardTradingList.getMain_original());
			paramList.add(crmCardTradingList.getReward_original());
			paramList.add(crmCardTradingList.getDeposit());
			paramList.add(crmCardTradingList.getOperator());
			paramList.add(crmCardTradingList.getOperate_time());
			paramList.add(crmCardTradingList.getBill_money());
			paramList.add(crmCardTradingList.getThird_bill_code());
			paramList.add(crmCardTradingList.getBill_code_original());
			paramList.add(crmCardTradingList.getActivity_id());
			paramList.add(crmCardTradingList.getCustomer_id());
			paramList.add(crmCardTradingList.getRevoked_trading());
			paramList.add(crmCardTradingList.getBatch_num());
			paramList.add(crmCardTradingList.getLast_updatetime());
			paramList.add(crmCardTradingList.getStore_updatetime());
			paramList.add(crmCardTradingList.getCard_class_id());
			paramList.add(crmCardTradingList.getName());
			paramList.add(crmCardTradingList.getMobil());
			paramList.add(crmCardTradingList.getOperator_id());
			paramList.add(crmCardTradingList.getShift_id());
			paramList.add(crmCardTradingList.getTotal_balance());
			paramList.add(crmCardTradingList.getReward_balance());
			paramList.add(crmCardTradingList.getMain_balance());
			paramList.add(crmCardTradingList.getPay_type());
			paramList.add(crmCardTradingList.getSalesman());
			paramList.add(crmCardTradingList.getCommission_saler_money());
			paramList.add(crmCardTradingList.getCommission_store_money());
			paramList.add(crmCardTradingList.getInvoice_balance());
			paramList.add(crmCardTradingList.getIs_invoice());
			paramList.add(crmCardTradingList.getPayment_state());
			paramList.add(crmCardTradingList.getRecharge_state());
			paramList.add(crmCardTradingList.getRequest_status());
			paramList.add(crmCardTradingList.getRequest_code());
			paramList.add(crmCardTradingList.getRequest_msg());
		}
		this.update(sql.toString(),paramList.toArray());
	}
	@Override
	public void savePosBillPaymentCouponsList(List<AcewillPosBillPaymentCoupons> list) throws Exception {
		// TODO Auto-generated method stub
		if(list==null||list.size()<=0) {
			return;
		}
		StringBuilder sql = new StringBuilder();
		sql.append("INSERT INTO pos_bill_payment_coupons ( tenancy_id, store_id, bill_num, report_date, payment_id, coupons_code, deal_value, deal_name, last_updatetime, remark, upload_tag, is_cancel, class_id, type_id, discount_money, discount_num, chanel, price, item_id, item_num, coupons_pro, coupon_type, coupon_buy_price, due, tenancy_assume, third_assume, third_fee, request_state, rwid, item_unit_id )values");
		for (int i = 0; i < list.size(); i++) {
			if(i!=0) {
				sql.append(",");
			}
			sql.append("(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");
		}
		List<Object> paramList = new ArrayList<>();
		for (AcewillPosBillPaymentCoupons posBillPaymentCoupons : list) {
			paramList.add(posBillPaymentCoupons.getTenancy_id());
			paramList.add(posBillPaymentCoupons.getStore_id());
			paramList.add(posBillPaymentCoupons.getBill_num());
			paramList.add(posBillPaymentCoupons.getReport_date());
			paramList.add(posBillPaymentCoupons.getPayment_id());
			paramList.add(posBillPaymentCoupons.getCoupons_code());
			paramList.add(posBillPaymentCoupons.getDeal_value());
			paramList.add(posBillPaymentCoupons.getDeal_name());
			paramList.add(posBillPaymentCoupons.getLast_updatetime());
			paramList.add(posBillPaymentCoupons.getRemark());
			paramList.add(posBillPaymentCoupons.getUpload_tag());
			paramList.add(posBillPaymentCoupons.getIs_cancel());
			paramList.add(posBillPaymentCoupons.getClass_id());
			paramList.add(posBillPaymentCoupons.getType_id());
			paramList.add(posBillPaymentCoupons.getDiscount_money());
			paramList.add(posBillPaymentCoupons.getDiscount_num());
			paramList.add(posBillPaymentCoupons.getChanel());
			paramList.add(posBillPaymentCoupons.getPrice());
			paramList.add(posBillPaymentCoupons.getItem_id());
			paramList.add(posBillPaymentCoupons.getItem_num());
			paramList.add(posBillPaymentCoupons.getCoupons_pro());
			paramList.add(posBillPaymentCoupons.getCoupon_type());
			paramList.add(posBillPaymentCoupons.getCoupon_buy_price());
			paramList.add(posBillPaymentCoupons.getDue());
			paramList.add(posBillPaymentCoupons.getTenancy_assume());
			paramList.add(posBillPaymentCoupons.getThird_assume());
			paramList.add(posBillPaymentCoupons.getThird_fee());
			paramList.add(posBillPaymentCoupons.getRequest_state());
			paramList.add(posBillPaymentCoupons.getRwid());
			paramList.add(posBillPaymentCoupons.getItem_unit_id());
		}
		this.update(sql.toString(),paramList.toArray());
	}
}
