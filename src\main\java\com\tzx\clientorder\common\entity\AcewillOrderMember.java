package com.tzx.clientorder.common.entity;

import java.util.List;

import com.tzx.framework.common.util.CommonUtil;

public class AcewillOrderMember {

	private String openid;
	private String unionid;
	private String name;
	private String mobile;
	private String phone;
	private String cno;
	private String grade;
	private String grade_name;
	private String birthday;
	private Double credit;
	private Integer ratio;
	private Double balance;
	private String viptype;
	private List<String> coupons;
    private String total_credit;
    private String shop_consume_number;
    private String brand_consume_number;
	
	public String getOpenid() {
		return CommonUtil.hv(openid) ? openid : unionid;
	}
	public void setOpenid(String openid) {
		this.openid = openid;
	}
	public String getUnionid()
	{
		return CommonUtil.hv(unionid) ? unionid : openid;
	}
	public void setUnionid(String unionid)
	{
		this.unionid = unionid;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getMobile() {
		return CommonUtil.hv(mobile) ? mobile : phone;
	}
	public void setMobile(String mobile) {
		this.mobile = mobile;
	}
	public String getPhone()
	{
		return CommonUtil.hv(phone) ? phone : mobile;
	}
	public void setPhone(String phone)
	{
		this.phone = phone;
	}
	public String getCno() {
		return cno;
	}
	public void setCno(String cno) {
		this.cno = cno;
	}
	public String getGrade() {
		return grade;
	}
	public void setGrade(String grade) {
		this.grade = grade;
	}
	public Double getCredit() {
		return credit;
	}
	public void setCredit(Double credit) {
		this.credit = credit;
	}
	public void setBalance(Double balance) {
		this.balance = balance;
	}
	public Integer getRatio() {
		return ratio;
	}
	public void setRatio(Integer ratio) {
		this.ratio = ratio;
	}
	public String getViptype() {
		return viptype;
	}
	public void setViptype(String viptype) {
		this.viptype = viptype;
	}
	public List<String> getCoupons() {
		return coupons;
	}
	public void setCoupons(List<String> coupons) {
		this.coupons = coupons;
	}
	public Double getBalance() {
		return balance;
	}
	public String getGrade_name()
	{
		return grade_name;
	}
	public void setGrade_name(String grade_name)
	{
		this.grade_name = grade_name;
	}
	public String getBirthday()
	{
		return birthday;
	}
	public void setBirthday(String birthday)
	{
		this.birthday = birthday;
	}

    public String getTotal_credit() {
        return total_credit;
    }

    public void setTotal_credit(String total_credit) {
        this.total_credit = total_credit;
    }

    public String getShop_consume_number() {
        return shop_consume_number;
    }

    public void setShop_consume_number(String shop_consume_number) {
        this.shop_consume_number = shop_consume_number;
    }

    public String getBrand_consume_number() {
        return brand_consume_number;
    }

    public void setBrand_consume_number(String brand_consume_number) {
        this.brand_consume_number = brand_consume_number;
    }
}
