package com.tzx.clientorder.mtwechat.po.springjdbc.dao.imp;

import com.tzx.clientorder.mtwechat.po.springjdbc.dao.MtInsertOrderDao;
import com.tzx.framework.common.util.dao.impl.GenericDaoImpl;
import net.sf.json.JSONObject;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by qingui on 2018-06-08.
 */
@Repository(MtInsertOrderDao.NAME)
public class MtInsertOrderDaoImp extends GenericDaoImpl implements MtInsertOrderDao {

    @Override
    public JSONObject getPaymentWay(String tenancyId, String code) {
        JSONObject result = new JSONObject();
        StringBuffer sql = new StringBuffer();
        List<JSONObject> list = null;
        sql.append(" select payment_class as type,payment_name1 as name,id as jzid,payment_name2 as name_english from payment_way\n" +
                " where payment_class = '"+ code +"'");
        try {
            list = this.query4Json(tenancyId, sql.toString());
        } catch (Exception e) {
           //不要使用 //e.printStackTrace();，如果有必要，用logger.error替代;
        }
        if (null != list && list.size() > 0)
            result = list.get(0);
        return result;
    }

    @Override
    public void insertPayInfo(String tenancyId, JSONObject payInfo) throws Exception{
        this.insertIgnorCase(tenancyId, "pos_bill_payment", payInfo);
    }

    @Override
    public void updatePosBill(String tenancyId, String billNum) throws Exception {
        StringBuffer sql = new StringBuffer();
        sql.append(" update v_pos_bill set payment_state = '01',difference = 0 where bill_num = '"+ billNum +"'");
    }
}
