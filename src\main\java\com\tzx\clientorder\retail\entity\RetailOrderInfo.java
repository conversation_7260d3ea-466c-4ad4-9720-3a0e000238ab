package com.tzx.clientorder.retail.entity;

import java.util.List;

import com.tzx.clientorder.common.entity.AcewillOrderMember;
import com.tzx.clientorder.common.entity.AcewillOrderMemo;
import com.tzx.clientorder.common.entity.AcewillOrderNormalitem;
import com.tzx.clientorder.common.entity.AcewillOrderSetmeal;
import com.tzx.clientorder.wlifeprogram.common.entity.MicroLifeGrade;
import com.tzx.clientorder.wlifeprogram.common.entity.WlifeOrderPromotion;

public class RetailOrderInfo
{

    //门店订单号
    private String oid;
    //平台订单号
    //private String order_id;
    //桌位号
    private String tableno;
    //订单备注
    private AcewillOrderMemo ordermemo;
    //会员
    private AcewillOrderMember member;
    //应收
    private Double total;
    //实收
    private Double cost;
    //菜品
//    private List<AcewillOrderSetmeal> items;
    //套餐
    private List<AcewillOrderSetmeal> setmeal;
    //非套餐
    private List<AcewillOrderNormalitem> normalitems;
    //人数
    private String people;
    //服务费
    private String mealfee;
    //下单人昵称
    private String name;


    //member
    private String mobile;
    private String openid;
    private Double balance;
    private Double credit;
    private MicroLifeGrade upgrade;



   //=============
    private Double discount_money;
    private Double memberPrice;
    private Double weiXinPay;

    private String channel;
    private String billsource;
    
    //订单营销活动
    private List<WlifeOrderPromotion> order_promotion;
    
    public String getOid() {
        return oid;
    }

    public void setOid(String oid) {
        this.oid = oid;
    }


    public String getTableno() {
        return tableno;
    }

    public void setTableno(String tableno) {
        this.tableno = tableno;
    }

    public AcewillOrderMember getMember() {
        return member;
    }

    public void setMember(AcewillOrderMember member) {
        this.member = member;
    }

    public Double getTotal() {
        return total;
    }

    public void setTotal(Double total) {
        this.total = total;
    }

    public Double getCost() {
        return cost;
    }

    public void setCost(Double cost) {
        this.cost = cost;
    }

    public String getPeople() {
        return people;
    }

    public void setPeople(String people) {
        this.people = people;
    }

    public String getMealfee() {
        return mealfee;
    }

    public void setMealfee(String mealfee) {
        this.mealfee = mealfee;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<AcewillOrderSetmeal> getSetmeal() {
        return setmeal;
    }

    public void setSetmeal(List<AcewillOrderSetmeal> setmeal) {
        this.setmeal = setmeal;
    }

    public List<AcewillOrderNormalitem> getNormalitems() {
        return normalitems;
    }
    
    public void setNormalitems(List<AcewillOrderNormalitem> normalitems) {
        this.normalitems = normalitems;
    }

    public AcewillOrderMemo getOrdermemo() {
        return ordermemo;
    }

    public void setOrdermemo(AcewillOrderMemo ordermemo) {
        this.ordermemo = ordermemo;
    }

    public Double getDiscount_money() {
        return discount_money;
    }

    public void setDiscount_money(Double discount_money) {
        this.discount_money = discount_money;
    }

    public Double getMemberPrice() {
        return memberPrice;
    }

    public void setMemberPrice(Double memberPrice) {
        this.memberPrice = memberPrice;
    }

    public Double getWeiXinPay() {
        return weiXinPay;
    }

    public void setWeiXinPay(Double weiXinPay) {
        this.weiXinPay = weiXinPay;
    }

    public Double getBalance() {
        return balance;
    }

    public void setBalance(Double balance) {
        this.balance = balance;
    }

    public Double getCredit() {
        return credit;
    }

    public void setCredit(Double credit) {
        this.credit = credit;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getOpenid() {
        return openid;
    }

    public void setOpenid(String openid) {
        this.openid = openid;
    }

    public MicroLifeGrade getUpgrade() {
        return upgrade;
    }

    public void setUpgrade(MicroLifeGrade upgrade) {
        this.upgrade = upgrade;
    }
    
	public String getChannel(){
		return this.channel;
	}

	public void setChannel(String channel){
		this.channel = channel;
	}

	public String getBillsource(){
		return this.billsource;
	}

	public void setBillsource(String billsource){
		this.billsource = billsource;
	}

	public List<WlifeOrderPromotion> getOrder_promotion()
	{
		return order_promotion;
	}

	public void setOrder_promotion(List<WlifeOrderPromotion> order_promotion)
	{
		this.order_promotion = order_promotion;
	}
}
