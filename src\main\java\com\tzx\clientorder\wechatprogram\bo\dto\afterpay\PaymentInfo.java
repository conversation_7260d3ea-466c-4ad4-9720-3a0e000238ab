package com.tzx.clientorder.wechatprogram.bo.dto.afterpay;

import java.util.List;

import com.tzx.clientorder.wechatprogram.common.entity.program.PayMemberEntity;
import com.tzx.clientorder.wechatprogram.common.entity.program.PayInfoEntity;

/**结账请求参数
 * <AUTHOR>
 *
 */
public class PaymentInfo
{
	private String    		    request_id;
	private String 				request_source;
	private String				tableno;
	private String				ordernum;
	private String				openid;
	private PayOrderInfo		order_info;
	private PayMemberEntity		member_info;
	private List<PayInfoEntity>	pay_info;
	
	public PayOrderInfo getOrder_info()
	{
		return order_info;
	}
	public void setOrder_info(PayOrderInfo order_info)
	{
		this.order_info = order_info;
	}
	public PayMemberEntity getMember_info()
	{
		return member_info;
	}
	public void setMember_info(PayMemberEntity member_info)
	{
		this.member_info = member_info;
	}
	public List<PayInfoEntity> getPay_info()
	{
		return pay_info;
	}
	public void setPay_info(List<PayInfoEntity> pay_info)
	{
		this.pay_info = pay_info;
	}
	public String getTableno()
	{
		return tableno;
	}
	public void setTableno(String tableno)
	{
		this.tableno = tableno;
	}
	public String getOrdernum()
	{
		return ordernum;
	}
	public void setOrdernum(String ordernum)
	{
		this.ordernum = ordernum;
	}
	public String getOpenid()
	{
		return openid;
	}
	public void setOpenid(String openid)
	{
		this.openid = openid;
	}

}
