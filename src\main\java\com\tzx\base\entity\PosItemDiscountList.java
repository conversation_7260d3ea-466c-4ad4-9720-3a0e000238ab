package com.tzx.base.entity;

import java.sql.Timestamp;
import java.util.Date;

/** 营销活动明细
 * <AUTHOR>
 *
 */
public class PosItemDiscountList
{
	private String		tenancy_id;
	private Integer		store_id;
	private Integer		id;
	private Date		report_date;
	private String		bill_num;
	private String		batch_num;
	private String		bill_status;
	private Integer		item_id;
	private Integer		rwid;
	private Integer		active_id;
	private String		active_title;
	private Integer		rule_id;
	private Double		discount_amount;
	private Timestamp	last_update_time;
	private Integer		upload_tag	= 0;
	
	
	public String getTenancy_id()
	{
		return tenancy_id;
	}
	public void setTenancy_id(String tenancy_id)
	{
		this.tenancy_id = tenancy_id;
	}
	public Integer getStore_id()
	{
		return store_id;
	}
	public void setStore_id(Integer store_id)
	{
		this.store_id = store_id;
	}
	public Integer getId()
	{
		return id;
	}
	public void setId(Integer id)
	{
		this.id = id;
	}
	public Date getReport_date()
	{
		return report_date;
	}
	public void setReport_date(Date report_date)
	{
		this.report_date = report_date;
	}
	public String getBill_num()
	{
		return bill_num;
	}
	public void setBill_num(String bill_num)
	{
		this.bill_num = bill_num;
	}
	public String getBatch_num()
	{
		return batch_num;
	}
	public void setBatch_num(String batch_num)
	{
		this.batch_num = batch_num;
	}
	public String getBill_status()
	{
		return bill_status;
	}
	public void setBill_status(String bill_status)
	{
		this.bill_status = bill_status;
	}
	public Integer getItem_id()
	{
		return item_id;
	}
	public void setItem_id(Integer item_id)
	{
		this.item_id = item_id;
	}
	public Integer getRwid()
	{
		return rwid;
	}
	public void setRwid(Integer rwid)
	{
		this.rwid = rwid;
	}
	public Integer getActive_id()
	{
		return active_id;
	}
	public void setActive_id(Integer active_id)
	{
		this.active_id = active_id;
	}
	public String getActive_title()
	{
		return active_title;
	}
	public void setActive_title(String active_title)
	{
		this.active_title = active_title;
	}
	public Integer getRule_id()
	{
		return rule_id;
	}
	public void setRule_id(Integer rule_id)
	{
		this.rule_id = rule_id;
	}
	public Double getDiscount_amount()
	{
		return discount_amount;
	}
	public void setDiscount_amount(Double discount_amount)
	{
		this.discount_amount = discount_amount;
	}
	public Timestamp getLast_update_time()
	{
		return last_update_time;
	}
	public void setLast_update_time(Timestamp last_update_time)
	{
		this.last_update_time = last_update_time;
	}
	public Integer getUpload_tag()
	{
		return upload_tag;
	}
	public void setUpload_tag(Integer upload_tag)
	{
		this.upload_tag = upload_tag;
	}
}
