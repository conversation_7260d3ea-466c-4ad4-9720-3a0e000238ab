/** 
 * @(#)CodeFormat.java    1.0   2017-10-08
 * 
 * Copyright 北京普照天星科技有限公司. All rights reserved. 
 * PROPRIETARY/CONFIDENTIAL. Use is subject to license terms. 
 */
package com.tzx.base.scoket.acewill;

import java.net.URI;
import java.util.concurrent.atomic.AtomicLong;

import org.java_websocket.drafts.Draft_6455;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.tzx.base.cache.util.CacheTableUtil;
import com.tzx.base.common.util.PosPropertyUtil;
import com.tzx.framework.common.util.CommonUtil;


/**
 * webSocket连接线程类，包括重连和心跳。
 * 
 * <AUTHOR> email:<EMAIL>
 * @version 1.0 2018-03-10
 * @see
 * @since JDK7.0
 * @update
 */
public class WebSocketConnectionThread implements Runnable{
	
	private static final Logger LOGGER = LoggerFactory.getLogger(WebSocketConnectionThread.class);
	
	static WebSocketClientHandler webSocketClientHandler;
	
	private static AtomicLong LastMessageTime;
	
	@Override
	public void run() {
		
		WebSocketClientHandler c = null;
		try
		{
			c = new WebSocketClientHandler(new URI(getUrl()), new Draft_6455());
			c.connect();
			webSocketClientHandler = c;
			LOGGER.info("创建websocket连接...");
			Thread.sleep(getWaitTime()); // 休眠指定秒数，防止创建多个websocket连接。
		}
		catch (Exception e)
		{
			LOGGER.info("创建websocket连接失败..." + e.getMessage());
			LOGGER.error(e.getMessage(), e);
		}

		while (true) {
			try {
				/*
				 * 向消息推送平台发送ping指令，如果发送3次都失败，则重新创建websocket连接。
				 */
				boolean retryBo = true;
				if (c == null) {
					LOGGER.info("WebSocketClientHandler对象为null");
					retryBo = true;
				} else {
					// ping重试次数
					int pingNum = 3;
					for (int i = 0; i < pingNum; i++) {
						try {
							c.send("{\"msgType\":\"check\"}");
							Thread.sleep(100);//等待反馈
							if (isConnect()) {
								retryBo = false;
								break;
							}
						} catch (Exception ec) {
							LOGGER.error(ec.getMessage());
						}
					}
				}

				 /*
				  * 多次ping都不能返回结果，则启动重新创建websocket连接。
				  */				  
				Thread.sleep(getHeartBeatTime());
				if (retryBo) {
					LOGGER.info("重新创建websocket连接...");
					try
                    {
                        if(c != null)
                        {
                            c.close();
                        }
                    }
                    catch (Exception e1)
                    {
                        LOGGER.error("关闭websocket连接异常.....", e1);
                    }
					
					try
					{
						c = new WebSocketClientHandler(new URI(getUrl()), new Draft_6455());
						c.connect();
						webSocketClientHandler = c;
					}
					catch (Exception e)
					{
						LOGGER.info("重新创建websocket连接失败..." + e.getMessage());
						LOGGER.error(e.getMessage(), e);
					}
				}				
			} catch (Exception e) {
				LOGGER.debug(e.getMessage(), e);
			}
		}
	}

	static AtomicLong getLastMessageTime() {
		return LastMessageTime;
	}

	static void setLastMessageTime(AtomicLong lastMessageTime) {
		LastMessageTime = lastMessageTime;
	}
	
	private boolean isConnect() {
		return System.currentTimeMillis()-LastMessageTime.get()<getHeartBeatTime()+5000;
	}

	private long getWaitTime() {
		return Long.parseLong(PosPropertyUtil.getMsg("acewill.websocket.wait")); // 等待时间
	}

	private long getHeartBeatTime() {
		return Long.parseLong(PosPropertyUtil.getMsg("acewill.websocket.heartbeattime"));// 心跳间隔时间
	}

	private String getUrl() throws Exception {
		if(CommonUtil.isNullOrEmpty(PosPropertyUtil.getMsg("acewill.websocket.url")))
		{
			throw new Exception("未配置微生活地址");
 		}
		if(CommonUtil.isNullOrEmpty(CacheTableUtil.getSysParameter("WLIFE_BUSINESS_ID")))
		{
			throw new Exception("未配置微生活商户ID");
 		}
		if(CommonUtil.isNullOrEmpty(CacheTableUtil.getSysParameter("WLIFE_BRAND_ID")))
		{
			throw new Exception("未配置微生活品牌ID");
 		}
		if(CommonUtil.isNullOrEmpty(CacheTableUtil.getSysParameter("WLIFE_SHOP_ID")))
		{
			throw new Exception("未配置微生活门店ID");
 		}
		StringBuilder url = new StringBuilder(PosPropertyUtil.getMsg("acewill.websocket.url"));
		url.append("?identify=").append(CacheTableUtil.getSysParameter("WLIFE_BUSINESS_ID")).append("_").append(CacheTableUtil.getSysParameter("WLIFE_BRAND_ID")).append("_").append(CacheTableUtil.getSysParameter("WLIFE_SHOP_ID"));
		url.append("&group=").append(PosPropertyUtil.getMsg("acewill.websocket.group"));
		url.append("&sid=").append(CacheTableUtil.getSysParameter("WLIFE_SHOP_ID"));
		LOGGER.debug("===================================WebSocket: " + url.toString());
		return url.toString(); // 连接地址
	}
}
