package com.tzx.member.yazuo.request.vo;

public class YazuoRequestVo<T extends YazuoParam>
{
	private Long		timestamp;
	private int			version;
	private String		access_token;
	private YazuoParam	ciphertext;

	public Long getTimestamp()
	{
		return timestamp;
	}

	public void setTimestamp(Long timestamp)
	{
		this.timestamp = timestamp;
	}

	public int getVersion()
	{
		return version;
	}

	public void setVersion(int version)
	{
		this.version = version;
	}

	public String getAccess_token()
	{
		return access_token;
	}

	public void setAccess_token(String access_token)
	{
		this.access_token = access_token;
	}

	public YazuoParam getCiphertext()
	{
		return ciphertext;
	}

	public void setCiphertext(YazuoParam ciphertext)
	{
		this.ciphertext = ciphertext;
	}
}
