package com.tzx.clientorder.wechatprogram.common.constant;

import com.tzx.pos.base.constant.SysDictionary;

public enum QimaiPaymentWayEnum
{
	PAY_SOURCE_ALIPAY_QM(PromptConstant.PAY_SOURCE_ALIPAY, SysDictionary.PAYMENT_CLASS_QM_ALI_PAY), 
	PAY_SOURCE_WEIXIN_QM(PromptConstant.PAY_SOURCE_WEIXIN, SysDictionary.PAYMENT_CLASS_QM_WECHAT_PAY), 
	PAY_SOURCE_BALANCE_QM(PromptConstant.PAY_SOURCE_BALANCE_QM, SysDictionary.PAYMENT_CLASS_QM_BALANCE), 
	PAY_SOURCE_CREDIT_QM(PromptConstant.PAY_SOURCE_CREDIT_QM, SysDictionary.PAYMENT_CLASS_QM_CREDIT), 
	PAY_SOURCE_COUPON_QM(PromptConstant.PAY_SOURCE_COUPON_QM, SysDictionary.PAYMENT_CLASS_QM_COUPONS),
	PAY_SOURCE_BALANCE_YZ(PromptConstant.PAY_SOURCE_BALANCE_YZ, SysDictionary.PAYMENT_CLASS_YAZUO_BALANCE),
	PAY_SOURCE_CREDIT_YZ(PromptConstant.PAY_SOURCE_CREDIT_YZ, SysDictionary.PAYMENT_CLASS_YAZUO_CREDIT),
	PAY_SOURCE_COUPON_YZ(PromptConstant.PAY_SOURCE_COUPON_YZ, SysDictionary.PAYMENT_CLASS_YAZUO_COUPONS),
	PAY_SOURCE_COUPON_PHANTOM_YZ(PromptConstant.PAY_SOURCE_COUPON_PHANTOM_YZ, SysDictionary.PAYMENT_CLASS_YAZUO_COUPONS_PHANTOM),

    PAY_SOURCE_ALIPAY_ZS(PromptConstant.PAY_SOURCE_ALIPAY_ZS, SysDictionary.PAYMENT_CLASS_ZHONGSHANG_ALI_PAY),
    PAY_SOURCE_WEIXIN_ZS(PromptConstant.PAY_SOURCE_WEIXIN_ZS, SysDictionary.PAYMENT_CLASS_ZHONGSHANG_WECHAT_PAY),
    PAY_SOURCE_BALANCE_ZS(PromptConstant.PAY_SOURCE_BALANCE_ZS, SysDictionary.PAYMENT_CLASS_ZHONGSHANG_BALANCE),
    PAY_SOURCE_CREDIT_ZS(PromptConstant.PAY_SOURCE_CREDIT_ZS, SysDictionary.PAYMENT_CLASS_ZHONGSHANG_CREDIT),
    PAY_SOURCE_COUPON_ZS(PromptConstant.PAY_SOURCE_COUPON_ZS, SysDictionary.PAYMENT_CLASS_ZHONGSHANG_COUPONS),
    PAY_SOURCE_ACTIVITY_ZS(PromptConstant.PAY_SOURCE_ACTIVITY_ZS, SysDictionary.PAYMENT_CLASS_ZHONGSHANG_ACTIVITY);

	private String	paySource;
	private String	paymentClass;
	
	private QimaiPaymentWayEnum(String paySource, String paymentClass)
	{
		this.paySource = paySource;
		this.paymentClass = paymentClass;
	}

	public String getPaySource()
	{
		return paySource;
	}

	public void setPaySource(String paySource)
	{
		this.paySource = paySource;
	}

	public String getPaymentClass()
	{
		return paymentClass;
	}

	public void setPaymentClass(String paymentClass)
	{
		this.paymentClass = paymentClass;
	}
	
	/**
	 * @param paySource
	 * @return
	 */
	public static String getPaymentClassBySource(String paySource)
	{
		for (QimaiPaymentWayEnum t : QimaiPaymentWayEnum.values())
		{
			if (t.getPaySource().equals(paySource))
			{
				return t.getPaymentClass();
			}
		}
		return paySource;
	}
}
