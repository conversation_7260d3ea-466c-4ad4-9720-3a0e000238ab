package com.tzx.base.cache.entity;

public class DishInfoCache
{
	public static String getSql()
	{
		String sql = new String("select * from (select org.id as store_id,org.price_system,him.id as menu_id,himd.id as details_id,himd.item_id,himc.chanel,himc.class from public.hq_item_menu him left join public.hq_item_menu_details himd on him.id=himd.item_menu_id left join public.hq_item_menu_class himc on himd.id=himc.details_id left join public.hq_item_menu_organ himo on him.id=himo.item_menu_id left join public.organ org on himo.store_id=org.id) as him left join hq_item_info hii on him.item_id = hii.id");
		return sql;
	}
	
	public static String getKey()
	{
		return "item_id,chanel";
	}
}
