package com.tzx.clientorder.acewillwechat.bo.imp;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import com.tzx.base.common.util.PosPropertyUtil;
import com.tzx.clientorder.acewillwechat.bo.HoldBillService;
import com.tzx.clientorder.acewillwechat.bo.OrderService;
import com.tzx.clientorder.acewillwechat.bo.WshPosEntranceService;
import com.tzx.clientorder.acewillwechat.common.constant.WLifeH5Constant;
import com.tzx.clientorder.acewillwechat.po.springjdbc.dao.HoldBillDao;
import com.tzx.clientorder.acewillwechat.po.springjdbc.dao.OrderDao;
import com.tzx.clientorder.common.constant.WLifeConstant;
import com.tzx.framework.common.util.HttpUtil;
import com.tzx.framework.common.constant.SysParameterCode;
import com.tzx.framework.common.util.CommonUtil;
import com.tzx.pos.base.constant.SysDictionary;

/**
 * Created by qin-gui on 2018/3/10.
 */
@Service(OrderService.NAME)
public class OrderServiceImp implements OrderService{

    private static final Logger logger = Logger.getLogger(OrderServiceImp.class);
    
    @Resource(name = WshPosEntranceService.NAME)
    private WshPosEntranceService wshService;
    
    @Resource(name = OrderDao.NAME)
    private OrderDao orderDao;
    @Resource(name = HoldBillService.NAME)
    private HoldBillService holdBillService;
    @Resource(name = HoldBillDao.NAME)
    private HoldBillDao holdBillDao;

    @Override
    public JSONObject process(String tenancyId, Integer storeId,JSONObject jsonObject) {
        String tableCode = jsonObject.optString("table_sno");
        try {
            //上传订单，更新锁单表的openid
            holdBillService.saveOpenId(tenancyId, storeId, tableCode, jsonObject.optString("openid"));
            getOrderInfo(tenancyId, storeId, tableCode, WLifeConstant.OPT_TYPE_LOCK);
        } catch (Exception e) {
           //不要使用 //e.printStackTrace();，如果有必要，用logger.error替代;
        }
        return null;
    }

    @Override
    public JSONObject getOrderInfo(String tenancyId, int storeId, String tableCode, String opType) throws Exception{

    	if (!wshService.isWlifeOrderH5(tenancyId, storeId))
    	{
    		return null;
    	}
    	
    	Map<String, String> uploadParamMap = new HashMap<String, String>();
    	String businessId =null;
    	String brandId =null;
    	String shopId =null;
    	
        List<JSONObject> paramList = orderDao.getParam(tenancyId);
        if (null != paramList && paramList.size() > 0){
            for (int i=0; i < paramList.size(); i++){
                String code = paramList.get(i).optString("para_code");
                if (SysParameterCode.BUSSINESS_ID.equals(code)){
                	businessId = paramList.get(i).optString("para_value");
                }else if (SysParameterCode.BRAND_ID.equals(code)){
                	brandId = paramList.get(i).optString("para_value");
                }else if (SysParameterCode.SHOP_ID.equals(code)){
                	shopId = paramList.get(i).optString("para_value");
                }
            }
        }
        
        if(CommonUtil.isNullOrEmpty(businessId)||CommonUtil.isNullOrEmpty(brandId)||CommonUtil.isNullOrEmpty(shopId))
        {
        	return null;
        }
		// 查询微生活会员信息
		JSONObject member = orderDao.getWLifeMember(tenancyId, tableCode, storeId);
		String openid = null;
		if (null != member)
		{
			member.put("ratio", 1);
			member.put("viptype", "wlife");
			member.put("coupons", "[]");
			openid = member.optString("openid");
		}

		// 获取订单信息 order_info
		JSONObject orderObject = getOrder(tenancyId, tableCode, storeId);
		String oid = null;
		String outOrderId = null;
		String isLocked = null;
		if (null != orderObject)
		{
			oid = orderObject.optString("oid");
			outOrderId = orderObject.optString("identify");
			isLocked = orderObject.optString("is_locked");

			orderObject.put("tableno", tableCode);
			orderObject.put("brand_id", brandId);
			orderObject.put("business_id", businessId);
			orderObject.put("member", member);

			orderObject.remove("oid");
			orderObject.remove("is_locked");

			uploadParamMap.put("business_id", businessId);
			uploadParamMap.put("brand_id", brandId);
			uploadParamMap.put("shop_id", shopId);
			uploadParamMap.put("table_sno", tableCode);
			//查询最新的openid,查询的是锁单表的openid
			List<JSONObject> holdBillList = holdBillDao.getBillLockOpenId(tenancyId, storeId, tableCode);
			if (null != holdBillList && holdBillList.size() > 0)
			    openid = holdBillList.get(0).optString("open_id");
			uploadParamMap.put("openid", openid);
			uploadParamMap.put("oid", oid);
			uploadParamMap.put("out_order_id", outOrderId);
			uploadParamMap.put("is_locked", isLocked);

			uploadParamMap.put("order_info", orderObject.toString());
			uploadParamMap.put("optype", opType);

			// 订单模式 3是后付
			uploadParamMap.put("ordermode", SysDictionary.ORDER_MODE_AFTERPAY);
			// 是否预结订单
			if (WLifeConstant.OPT_TYPE_PAY.equals(opType)) 
				uploadParamMap.put("is_pre_checkout", "1");
			else 
				uploadParamMap.put("is_pre_checkout", "2");
        
	        //调用微生活的url，上传订单
//	        String msgFilePath=DataUploadRunnable.class.getResource("/").getPath()+ File.separator + "pos"+ File.separator +"acewill_config.properties";
//	        String url = PropertiesUtil.readValue(msgFilePath,"acewill.request.url");
			String url = PosPropertyUtil.getMsg("acewill.request.url");
	        url += WLifeH5Constant.UPLOAD_ORDER_URL;
            logger.info("开始调用微生活的上传订单接口" + uploadParamMap);
			String msg = HttpUtil.sendPostRequest(url, uploadParamMap);
	        logger.info("调用微生活的上传订单返回结果：" + msg);
	    }
        return JSONObject.fromObject(uploadParamMap);
    }

    /**
     * 获取订单信息
     * @param tenancyId
     * @param tableCode
     * @param storeId
     * @return
     * @throws Exception
     */
    private JSONObject getOrder(String tenancyId, String tableCode, int storeId) throws Exception{
        //订单信息
        JSONObject orderObject = new JSONObject();
        //查询微生活会员信息
        JSONObject member = orderDao.getWLifeMember(tenancyId, tableCode, storeId);
        //查询订单信息
        JSONObject billObject = orderDao.getBillInfo(tenancyId, tableCode, storeId);
        //设置订单的优惠信息
        setBillDiscount(billObject, orderObject);
        if (null != billObject){
            if (null != member){
                orderObject.put("openid", member.optString("openid"));
                orderObject.put("name", member.optString("name"));
            }
            //订单备注
            JSONObject remark = new JSONObject();
            //用户选择的备注id
            remark.put("id", null);
            remark.put("text", billObject.optString("text"));
            orderObject.put("ordermemo", remark);
            //升级   gradeamount 升级所需金额
            orderObject.put("is_locked", billObject.optInt("is_locked"));
            orderObject.put("total", billObject.optDouble("total"));
            orderObject.put("cost", billObject.optDouble("cost"));
            orderObject.put("discountable", billObject.optDouble("discountable"));
            orderObject.put("membercouponsprice", billObject.optDouble("membercouponsprice"));
            orderObject.put("people", billObject.optString("people"));
            orderObject.put("mealfee", billObject.optDouble("mealfee"));
            orderObject.put("oid", billObject.optString("oid"));
            orderObject.put("identify", billObject.optString("out_order_id"));
            orderObject.put("shop_name", billObject.optString("shop_name"));
        }
        orderObject.put("tableno", tableCode);
        orderObject.put("ordermode", SysDictionary.ORDER_MODE_AFTERPAY);
        //设置代金券是否可用
        orderObject.put("djqflag", true);
        orderObject.put("djqRules", "");
        //查询服务费
        List<JSONObject> serviceCharge = orderDao.serviceCharge(tenancyId, billObject.optString("oid"),tableCode, storeId);
        orderObject.put("serviceCharge", serviceCharge);

        //设置订单中的菜品信息
        setDish(tenancyId, orderObject);
        return orderObject;
    }

    /**
     * 设置订单的优惠信息
     * @param billObject
     * @param orderObject
     */
    private void setBillDiscount(JSONObject billObject, JSONObject orderObject){
        if (null == billObject){
            orderObject.put("discounts", null);
        }else {
            int discountMode = billObject.optInt("discount_mode_id");
            String title = "";
            switch (discountMode){
                case SysDictionary.DISCOUNT_MODE_1 :
                    title = "整单折扣" + billObject.optDouble("discount_rate")/10 + "折";
                    break;
                case SysDictionary.DISCOUNT_MODE_2 :
                    title = billObject.optString("discount_case_name");
                    break;
                case SysDictionary.DISCOUNT_MODE_3 :
                    title = "折让" + billObject.optDouble("discount_rate")/10 + "折";
                    break;
                case SysDictionary.DISCOUNT_MODE_4 :
                    title = "团体会员折扣" + billObject.optDouble("discount_rate")/10 + "折";
                    break;
                case SysDictionary.DISCOUNT_MODE_5 :
                    title = "会员折扣" + billObject.optDouble("discount_rate")/10 + "折";
                    break;
                case SysDictionary.DISCOUNT_MODE_6 :
                    title = "会员价";
                    break;
                case SysDictionary.DISCOUNT_MODE_7 :
                    title = "线上优惠" + billObject.optDouble("discount_rate")/10 + "折";
                    break;
                case SysDictionary.DISCOUNT_MODE_8 :
                    title = "折上折" + billObject.optDouble("discount_rate")/10 + "折";
                    break;
                case SysDictionary.DISCOUNT_MODE_9 :
                    title = "普通折扣" + billObject.optDouble("discount_rate")/10 + "折";
                    break;
                case SysDictionary.DISCOUNT_MODE_10 :
                    title = "单品折扣" + billObject.optDouble("discount_rate")/10 + "折";
                    break;
                case SysDictionary.DISCOUNT_MODE_11 :
                    title = "活动折扣" + billObject.optDouble("discount_rate")/10 + "折";
                    break;
                default:
                    break;
            }
            JSONArray array = new JSONArray();
            JSONObject discount = new JSONObject();
            discount.put("title", title);
            discount.put("money", billObject.optDouble("discount_amount",0));
            //6是折扣
            discount.put("type", 6);
            discount.put("rules", "");
            array.add(discount);
            orderObject.put("discounts", array);
        }
    }

    /**
     * 设置订单中的菜品信息
     * @param orderObject
     */
    private void setDish(String tenancyId,JSONObject orderObject) throws Exception{
        //桌台的未结账的账单号
        String billNum = orderObject.optString("oid");
        //查询套餐菜品信息(可能一笔订单，多个套餐)
        List<JSONObject> setmealList = orderDao.getSetmeal(tenancyId, billNum);

        if(null == setmealList)
        {
        	setmealList = new ArrayList<JSONObject>();
        }
        //查询套餐中的主菜和必选菜
        if (setmealList.size() > 0){
            for (int i=0; i < setmealList.size(); i++){
                //套餐id
                //String setmealId = setmealList.get(i).optString("did");
                //设置菜品可用券的情况
                setmealList.get(i).put("cpqflag", true);
                //菜品券互斥规则提示语
                setmealList.get(i).put("cpqRules", "");
                //菜品券抵扣数量
                setmealList.get(i).put("cpqhasbeen", 0);
                //抵扣菜品券面值
                setmealList.get(i).put("cpqDeno", "");

                setmealList.get(i).put("bbuySno", "");
                setmealList.get(i).put("bgiftSno", "");
                //可使用会员价的会员等级
                setmealList.get(i).put("membergid", new JSONArray());
                setmealList.get(i).put("isWeigh", setmealList.get(i).optInt("is_weigh"));
                //查询菜品做饭
                List<JSONObject> cookList = orderDao.getCooks(tenancyId, setmealList.get(i).optInt("id"));
                setmealList.get(i).put("cooks", cookList);
                //查询套餐里的主菜信息
                //主菜里的数量，要除以套餐的数量。
                int stemealCount = setmealList.get(i).optInt("number", 1);
                //套餐id
                String setmealId = setmealList.get(i).optString("did");
                List<JSONObject> mainList = orderDao.getSetmealDish(tenancyId, billNum, "main", stemealCount, setmealId);
                if(null == mainList)
                {
                	mainList = new ArrayList<JSONObject>();
                }
                //查询套餐中的必选菜品
                List<JSONObject> mandatoryList = orderDao.getSetmealDish(tenancyId, billNum, "mandatory", stemealCount, setmealId);
                if(null == mandatoryList)
                {
                	mandatoryList = new ArrayList<JSONObject>();
                }
                setmealList.get(i).put("maindish", mainList);
                setmealList.get(i).put("mandatory", mandatoryList);
                setmealList.get(i).put("optional", new JSONArray());
            }
        }
        //设置订单中的套餐信息
        orderObject.put("setmeal", setmealList);
        //非套餐菜品的查询
        List<JSONObject> normalList = orderDao.getNormalitems(tenancyId, billNum);
        if (null != normalList && normalList.size() > 0){
            for (int i=0; i < normalList.size(); i++){
                normalList.get(i).put("bbuySno", "");
                normalList.get(i).put("bgiftSno", "");
                //可使用会员价的会员等级
                normalList.get(i).put("membergid", new JSONArray());
                normalList.get(i).put("isWeigh", normalList.get(i).optInt("is_weigh"));
                //查询菜品做饭
                List<JSONObject> cookList = orderDao.getCooks(tenancyId, normalList.get(i).optInt("id"));
                normalList.get(i).put("cooks", cookList);
                //设置菜品可用券的情况
                normalList.get(i).put("cpqflag", true);
                //菜品券互斥规则提示语
                normalList.get(i).put("cpqRules", "");
                //菜品券抵扣数量
                normalList.get(i).put("cpqhasbeen", 0);
                //抵扣菜品券面值
                normalList.get(i).put("cpqDeno", "");
            }
        }
        if(null==normalList)
        {
        	normalList = new ArrayList<JSONObject>();
        }
        orderObject.put("normalitems", normalList);
    }
}
