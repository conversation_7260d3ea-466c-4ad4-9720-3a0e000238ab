package com.tzx.base.entity;

import java.sql.Timestamp;
import java.util.Date;

/** 账单折扣明细
 * <AUTHOR>
 * @version 2019-06-06
 * 
 */
public class PosBillDiscount
{
	private String		tenancy_id;
	private Integer		store_id;
	private Integer		id;
	private Date		report_date;
	private String		pos_num;
	private String		opt_num;
	private String		bill_num;
	private String		discount_type;
	private String		discount_mode;
	private String		discount_label;
	private Double		discount_amount	= 0d;
	private Double		discount_count	= 1d;
	private String		payment_uid;
	private Timestamp	last_updatetime;
	private Integer		upload_tag		= 0;
	
	public PosBillDiscount()
	{
		super();
	}
	
	public PosBillDiscount(String tenancy_id, Integer store_id, Date report_date, String pos_num, String opt_num, String bill_num, String discount_type, String discount_mode, String discount_label, Double discount_amount, Double discount_count, Timestamp last_updatetime)
	{
		super();
		this.tenancy_id = tenancy_id;
		this.store_id = store_id;
		this.report_date = report_date;
		this.pos_num = pos_num;
		this.opt_num = opt_num;
		this.bill_num = bill_num;
		this.discount_type = discount_type;
		this.discount_mode = discount_mode;
		this.discount_label = discount_label;
		this.discount_amount = discount_amount;
		this.discount_count = discount_count;
		this.last_updatetime = last_updatetime;
	}

	public String getTenancy_id()
	{
		return tenancy_id;
	}
	public void setTenancy_id(String tenancy_id)
	{
		this.tenancy_id = tenancy_id;
	}
	public Integer getStore_id()
	{
		return store_id;
	}
	public void setStore_id(Integer store_id)
	{
		this.store_id = store_id;
	}
	public Integer getId()
	{
		return id;
	}
	public void setId(Integer id)
	{
		this.id = id;
	}
	public Date getReport_date()
	{
		return report_date;
	}
	public void setReport_date(Date report_date)
	{
		this.report_date = report_date;
	}
	public String getPos_num()
	{
		return pos_num;
	}
	public void setPos_num(String pos_num)
	{
		this.pos_num = pos_num;
	}
	public String getOpt_num()
	{
		return opt_num;
	}
	public void setOpt_num(String opt_num)
	{
		this.opt_num = opt_num;
	}
	public String getBill_num()
	{
		return bill_num;
	}
	public void setBill_num(String bill_num)
	{
		this.bill_num = bill_num;
	}
	public String getDiscount_type()
	{
		return discount_type;
	}
	public void setDiscount_type(String discount_type)
	{
		this.discount_type = discount_type;
	}
	public String getDiscount_mode()
	{
		return discount_mode;
	}
	public void setDiscount_mode(String discount_mode)
	{
		this.discount_mode = discount_mode;
	}
	public String getDiscount_label()
	{
		return discount_label;
	}
	public void setDiscount_label(String discount_label)
	{
		this.discount_label = discount_label;
	}
	public Double getDiscount_amount()
	{
		return discount_amount;
	}
	public void setDiscount_amount(Double discount_amount)
	{
		this.discount_amount = discount_amount;
	}
	public Double getDiscount_count()
	{
		return discount_count;
	}
	public void setDiscount_count(Double discount_count)
	{
		this.discount_count = discount_count;
	}
	public String getPayment_uid()
	{
		return payment_uid;
	}
	public void setPayment_uid(String payment_uid)
	{
		this.payment_uid = payment_uid;
	}
	public Timestamp getLast_updatetime()
	{
		return last_updatetime;
	}
	public void setLast_updatetime(Timestamp last_updatetime)
	{
		this.last_updatetime = last_updatetime;
	}
	public Integer getUpload_tag()
	{
		return upload_tag;
	}
	public void setUpload_tag(Integer upload_tag)
	{
		this.upload_tag = upload_tag;
	}
}
