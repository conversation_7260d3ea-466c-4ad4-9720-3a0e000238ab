package com.tzx.clientorder.wechatprogram.bo;

import net.sf.json.JSONObject;

/**
 * 通用业务（查询门店营业状态、桌台的订单） Created by qingui on 2018-06-26.
 */
public interface PromptGeneralService
{

	String NAME = "com.tzx.clientorder.wechatprogram.bo.impl.PromptGeneralServiceImp";

	/**
	 * 查询门店的营业状态
	 * 
	 * @param tenancyId
	 * @param organId
	 * @return
	 * @throws Exception
	 */
	JSONObject getOrganStatus(String tenancyId, int organId,String channel) throws Exception;
}
