package com.tzx.clientorder.wechatprogram.runnable;

import org.apache.log4j.Logger;

import com.tzx.clientorder.wechatprogram.bo.adapter.PromptServiceAdapter;

public class CompleteOrderRunnable implements Runnable
{
	private static final Logger logger = Logger.getLogger(CompleteOrderRunnable.class);
	
	private String	tenancyId;
	private Integer	storeId;
	private String	billNum;
	private String 	channel;
	private Boolean isModifyPay;

	public CompleteOrderRunnable(String tenancyId, Integer storeId, String billNum, String channel, Boolean isModifyPay)
	{
		super();
		this.tenancyId = tenancyId;
		this.storeId = storeId;
		this.billNum = billNum;
		this.channel = channel;
		this.isModifyPay = isModifyPay;
	}

	@Override
	public void run()
	{
		try
		{
			// 休眠500毫秒,等待数据提交完成;
			Thread.sleep(500);

			PromptServiceAdapter adapter = new PromptServiceAdapter(channel);
			if(isModifyPay){
				adapter.modifyPayOrder(tenancyId, storeId, billNum);
			}
			else{
				adapter.completeOrder(tenancyId, storeId, billNum);
			}
		}
		catch (Exception e)
		{
			logger.info("关单通知失败:", e);
		}
	}

	public void setTenancyId(String tenancyId)
	{
		this.tenancyId = tenancyId;
	}

	public void setStoreId(Integer storeId)
	{
		this.storeId = storeId;
	}

	public void setBillNum(String billNum)
	{
		this.billNum = billNum;
	}

	public void setChannel(String channel)
	{
		this.channel = channel;
	}

	public void setModifyPay(Boolean modifyPay) {
		isModifyPay = modifyPay;
	}
}
