/** 
 * @(#)CodeFormat.java    1.0   2018-04-19 
 * 
 * Copyright 北京普照天星科技有限公司. All rights reserved. 
 * PROPRIETARY/CONFIDENTIAL. Use is subject to license terms. 
 */
package com.tzx.base.listener;

import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import org.apache.log4j.Logger;
import com.tzx.framework.common.constant.Constant;
import com.tzx.framework.common.constant.SysParameterCode;
import com.tzx.framework.common.util.CommonUtil;
import com.tzx.framework.common.util.SpringConext;
import com.tzx.framework.common.util.dao.datasource.DBContextHolder;
import com.tzx.pos.po.springjdbc.dao.PosPaymentDao;
import com.tzx.pos.po.springjdbc.dao.imp.PosPaymentDaoImp;

/**
 * 判断是否需要启用秒付数据自动上传。
 * 
 * <AUTHOR> email:<EMAIL>
 * @version 1.0  2018-04-19
 * @see     
 * @since   JDK7.0
 * @update  
 */
public class JudgePosQuickPassTask implements Runnable {
	
	private static final Logger	logger	= Logger.getLogger(JudgePosQuickPassTask.class);

	@Override
	public void run() {
		try{	
			// 等待Bean初始化完成
			while(SpringConext.getApplicationContext() == null){
				Thread.sleep(1000);
			}  
			logger.info("判断是否启动秒付数据自动上传......");
			Map<String, String> systemMap = Constant.getSystemMap();
            String tenent_id= systemMap.get("tenent_id");
            String store_id= systemMap.get("store_id");
            DBContextHolder.setTenancyid(tenent_id);
            PosPaymentDaoImp posPaymentServiceImp = (PosPaymentDaoImp) SpringConext.getApplicationContext().getBean(PosPaymentDao.NAME);
            String para_value= posPaymentServiceImp.getSysParameter(tenent_id,Integer.parseInt(store_id), SysParameterCode.IFUP_QUICK_PAY);
           //秒付开关
            if("0".equals(para_value)||"".equals(para_value)){
            	return ;
            }else{
            	logger.info("秒付数据自动上传服务......");
            	int hqdataPeriod = CommonUtil.ti(Constant.getSystemMap().get("hqdata_minite_period"));
    			Executors.newScheduledThreadPool(1).scheduleWithFixedDelay(new PosQuickPassTask(), 1, hqdataPeriod, TimeUnit.MINUTES);
            }
		}catch(Exception e){
			logger.error("判断是否需要启用秒付数据自动上传出错",e);
		}
	}

}
