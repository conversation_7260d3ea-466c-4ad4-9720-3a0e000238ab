package com.tzx.base.entity;

import java.sql.Timestamp;
import java.util.Date;

import com.tzx.framework.common.util.DoubleHelper;

/**
 * 账单会员操作表映射实体
 * 
 * <AUTHOR>
 * <AUTHOR>
 */
public class PosBillMember
{
	private String		tenancy_id;
	private Integer		store_id;
	private Integer		id;
	private String		bill_num;
	private Date		report_date;
	private String		type;
	private Double		amount							= 0d;
	private Double		credit							= 0d;
	private String		card_code;
	private String		mobil;
	private Timestamp	last_updatetime;
	private Integer		upload_tag						= 0;
	private String		remark;
	private String		bill_code;
	private String		request_state;
	private String		customer_code;
	private String		customer_name;
	private Double		consume_before_credit			= 0d;
	private Double		consume_after_credit			= 0d;
	private Double		consume_before_main_balance		= 0d;
	private Double		consume_before_reward_balance	= 0d;
	private Double		consume_after_main_balance		= 0d;
	private Double		consume_after_reward_balance	= 0d;
	private String		generic_field;							// 通用字段
	private String		customer_type;							// 会员类型
	private String		third_code;

	public PosBillMember()
	{
		super();
	}

	public PosBillMember(String tenancy_id, Integer store_id, String bill_num, Date report_date, String type, Double amount, Double credit, String card_code, String mobil, Timestamp last_updatetime, String remark, String customer_code, String customer_name, Double consume_before_credit,
			Double consume_after_credit, Double consume_before_main_balance, Double consume_before_reward_balance, Double consume_after_main_balance, Double consume_after_reward_balance)
	{
		super();
		this.tenancy_id = tenancy_id;
		this.store_id = store_id;
		this.bill_num = bill_num;
		this.report_date = report_date;
		this.type = type;
		this.amount = amount;
		this.credit = credit;
		this.card_code = card_code;
		this.mobil = mobil;
		this.last_updatetime = last_updatetime;
		this.remark = remark;
		this.customer_code = customer_code;
		this.customer_name = customer_name;
		this.consume_before_credit = consume_before_credit;
		this.consume_after_credit = consume_after_credit;
		this.consume_before_main_balance = consume_before_main_balance;
		this.consume_before_reward_balance = consume_before_reward_balance;
		this.consume_after_main_balance = consume_after_main_balance;
		this.consume_after_reward_balance = consume_after_reward_balance;
	}

	public String getTenancy_id()
	{
		return tenancy_id;
	}

	public void setTenancy_id(String tenancy_id)
	{
		this.tenancy_id = tenancy_id;
	}

	public Integer getStore_id()
	{
		return store_id;
	}

	public void setStore_id(Integer store_id)
	{
		this.store_id = store_id;
	}

	public Integer getId()
	{
		return id;
	}

	public void setId(Integer id)
	{
		this.id = id;
	}

	public String getBill_num()
	{
		return bill_num;
	}

	public void setBill_num(String bill_num)
	{
		this.bill_num = bill_num;
	}

	public Date getReport_date()
	{
		return report_date;
	}

	public void setReport_date(Date report_date)
	{
		this.report_date = report_date;
	}

	public String getType()
	{
		return type;
	}

	public void setType(String type)
	{
		this.type = type;
	}

	public Double getAmount()
	{
		return DoubleHelper.isNaN(amount) ? 0d : amount;
	}

	public void setAmount(Double amount)
	{
		this.amount = amount;
	}

	public Double getCredit()
	{
		return DoubleHelper.isNaN(credit) ? 0d : credit;
	}

	public void setCredit(Double credit)
	{
		this.credit = credit;
	}

	public String getCard_code()
	{
		return card_code;
	}

	public void setCard_code(String card_code)
	{
		this.card_code = card_code;
	}

	public String getMobil()
	{
		return mobil;
	}

	public void setMobil(String mobil)
	{
		this.mobil = mobil;
	}

	public Timestamp getLast_updatetime()
	{
		return last_updatetime;
	}

	public void setLast_updatetime(Timestamp last_updatetime)
	{
		this.last_updatetime = last_updatetime;
	}

	public Integer getUpload_tag()
	{
		return upload_tag;
	}

	public void setUpload_tag(Integer upload_tag)
	{
		this.upload_tag = upload_tag;
	}

	public String getRemark()
	{
		return remark;
	}

	public void setRemark(String remark)
	{
		this.remark = remark;
	}

	public String getBill_code()
	{
		return bill_code;
	}

	public void setBill_code(String bill_code)
	{
		this.bill_code = bill_code;
	}

	public String getRequest_state()
	{
		return request_state;
	}

	public void setRequest_state(String request_state)
	{
		this.request_state = request_state;
	}

	public String getCustomer_code()
	{
		return customer_code;
	}

	public void setCustomer_code(String customer_code)
	{
		this.customer_code = customer_code;
	}

	public String getCustomer_name()
	{
		return customer_name;
	}

	public void setCustomer_name(String customer_name)
	{
		this.customer_name = customer_name;
	}

	public Double getConsume_before_credit()
	{
		return DoubleHelper.isNaN(consume_before_credit) ? 0d : consume_before_credit;
	}

	public void setConsume_before_credit(Double consume_before_credit)
	{
		this.consume_before_credit = consume_before_credit;
	}

	public Double getConsume_after_credit()
	{
		return DoubleHelper.isNaN(consume_after_credit) ? 0d : consume_after_credit;
	}

	public void setConsume_after_credit(Double consume_after_credit)
	{
		this.consume_after_credit = consume_after_credit;
	}

	public Double getConsume_before_main_balance()
	{
		return DoubleHelper.isNaN(consume_before_main_balance) ? 0d : consume_before_main_balance;
	}

	public void setConsume_before_main_balance(Double consume_before_main_balance)
	{
		this.consume_before_main_balance = consume_before_main_balance;
	}

	public Double getConsume_before_reward_balance()
	{
		return DoubleHelper.isNaN(consume_before_reward_balance) ? 0d : consume_before_reward_balance;
	}

	public void setConsume_before_reward_balance(Double consume_before_reward_balance)
	{
		this.consume_before_reward_balance = consume_before_reward_balance;
	}

	public Double getConsume_after_main_balance()
	{
		return DoubleHelper.isNaN(consume_after_main_balance) ? 0d : consume_after_main_balance;
	}

	public void setConsume_after_main_balance(Double consume_after_main_balance)
	{
		this.consume_after_main_balance = consume_after_main_balance;
	}

	public Double getConsume_after_reward_balance()
	{
		return DoubleHelper.isNaN(consume_after_reward_balance) ? 0d : consume_after_reward_balance;
	}

	public void setConsume_after_reward_balance(Double consume_after_reward_balance)
	{
		this.consume_after_reward_balance = consume_after_reward_balance;
	}

	public String getGeneric_field()
	{
		return generic_field;
	}

	public void setGeneric_field(String generic_field)
	{
		this.generic_field = generic_field;
	}

	public String getCustomer_type()
	{
		return customer_type;
	}

	public void setCustomer_type(String customer_type)
	{
		this.customer_type = customer_type;
	}

	public String getThird_code()
	{
		return third_code;
	}

	public void setThird_code(String third_code)
	{
		this.third_code = third_code;
	}
}
