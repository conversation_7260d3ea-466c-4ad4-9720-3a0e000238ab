package com.tzx.clientorder.common.entity;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * <AUTHOR> 2016年4月7日
 */

public class AcewillPosBillMember implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 商户ID
	 */
	private String tenancy_id;
	/**
	 * 门店ID
	 */
	private Integer store_id;
	/**
	 * 自增ID
	 */
	private Integer id;
	/**
	 * 账单编号
	 */
	private String bill_num;
	/**
	 * 报表日期
	 */
	private Date report_date;
	/**
	 * 类型
	 */
	private String type;
	/**
	 * 金额
	 */
	private Double amount;
	/**
	 * 积分数
	 */
	private Double credit;
	/**
	 * 会员卡号
	 */
	private String card_code;
	/**
	 * 手机号
	 */
	private String mobil;
	/**
	 * 操作时间
	 */
	private Date last_updatetime;
	/**
	 * 上传标记
	 */
	private Integer upload_tag;
	/**
	 * 备注
	 */
	private String remark;
	/**
	 * 交易流水号
	 */
	private String bill_code;
	/**
	 * 请求状态
	 */
	private String request_state;
	/**
	 * 会员编号
	 */
	private String customer_code;
	/**
	 * 会员姓名
	 */
	private String customer_name;
	/**
	 * 交易前积分
	 */
	private Double consume_before_credit;
	/**
	 * 交易后积分
	 */
	private Double consume_after_credit;
	/**
	 * 交易前主账户余额
	 */
	private Double consume_before_main_balance;
	/**
	 * 交易前赠送账户余额
	 */
	private Double consume_before_reward_balance;
	/**
	 * 交易后主账户余额
	 */
	private Double consume_after_main_balance;
	/**
	 * 交易后赠送账户余额
	 */
	private Double consume_after_reward_balance;
	
	private String generic_field;

    private String total_credit;
    private String shop_consume_number;
    private String brand_consume_number;
	
	public String getTenancy_id() {
		return tenancy_id;
	}
	public void setTenancy_id(String tenancy_id) {
		this.tenancy_id = tenancy_id;
	}
	public Integer getStore_id() {
		return store_id;
	}
	public void setStore_id(Integer store_id) {
		this.store_id = store_id;
	}
	public Integer getId() {
		return id;
	}
	public void setId(Integer id) {
		this.id = id;
	}
	public String getBill_num() {
		return bill_num;
	}
	public void setBill_num(String bill_num) {
		this.bill_num = bill_num;
	}
	public Date getReport_date() {
		return report_date;
	}
	public void setReport_date(Date report_date) {
		this.report_date = report_date;
	}
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
	public Double getAmount() {
		return amount;
	}
	public void setAmount(Double amount) {
		this.amount = amount;
	}
	public Double getCredit() {
		return credit;
	}
	public void setCredit(Double credit) {
		this.credit = credit;
	}
	public String getCard_code() {
		return card_code;
	}
	public void setCard_code(String card_code) {
		this.card_code = card_code;
	}
	public String getMobil() {
		return mobil;
	}
	public void setMobil(String mobil) {
		this.mobil = mobil;
	}
	public Date getLast_updatetime() {
		return last_updatetime;
	}
	public void setLast_updatetime(Date last_updatetime) {
		this.last_updatetime = last_updatetime;
	}
	public Integer getUpload_tag() {
		return upload_tag;
	}
	public void setUpload_tag(Integer upload_tag) {
		this.upload_tag = upload_tag;
	}
	public String getRemark() {
		return remark;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}
	public String getBill_code() {
		return bill_code;
	}
	public void setBill_code(String bill_code) {
		this.bill_code = bill_code;
	}
	public String getRequest_state() {
		return request_state;
	}
	public void setRequest_state(String request_state) {
		this.request_state = request_state;
	}
	public String getCustomer_code() {
		return customer_code;
	}
	public void setCustomer_code(String customer_code) {
		this.customer_code = customer_code;
	}
	public String getCustomer_name() {
		return customer_name;
	}
	public void setCustomer_name(String customer_name) {
		this.customer_name = customer_name;
	}
	public Double getConsume_before_credit() {
		return consume_before_credit;
	}
	public void setConsume_before_credit(Double consume_before_credit) {
		this.consume_before_credit = consume_before_credit;
	}
	public Double getConsume_after_credit() {
		return consume_after_credit;
	}
	public void setConsume_after_credit(Double consume_after_credit) {
		this.consume_after_credit = consume_after_credit;
	}
	public Double getConsume_before_main_balance() {
		return consume_before_main_balance;
	}
	public void setConsume_before_main_balance(Double consume_before_main_balance) {
		this.consume_before_main_balance = consume_before_main_balance;
	}
	public Double getConsume_before_reward_balance() {
		return consume_before_reward_balance;
	}
	public void setConsume_before_reward_balance(Double consume_before_reward_balance) {
		this.consume_before_reward_balance = consume_before_reward_balance;
	}
	public Double getConsume_after_main_balance() {
		return consume_after_main_balance;
	}
	public void setConsume_after_main_balance(Double consume_after_main_balance) {
		this.consume_after_main_balance = consume_after_main_balance;
	}
	public Double getConsume_after_reward_balance() {
		return consume_after_reward_balance;
	}
	public void setConsume_after_reward_balance(Double consume_after_reward_balance) {
		this.consume_after_reward_balance = consume_after_reward_balance;
	}
	public String getGeneric_field()
	{
		return generic_field;
	}
	public void setGeneric_field(String generic_field)
	{
		this.generic_field = generic_field;
	}

    public String getTotal_credit() {
        return total_credit;
    }

    public void setTotal_credit(String total_credit) {
        this.total_credit = total_credit;
    }

    public String getShop_consume_number() {
        return shop_consume_number;
    }

    public void setShop_consume_number(String shop_consume_number) {
        this.shop_consume_number = shop_consume_number;
    }

    public String getBrand_consume_number() {
        return brand_consume_number;
    }

    public void setBrand_consume_number(String brand_consume_number) {
        this.brand_consume_number = brand_consume_number;
    }
}
