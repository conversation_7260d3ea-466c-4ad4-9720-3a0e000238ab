package com.tzx.clientorder.acewillwechat.bo.imp;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import net.sf.json.JSONObject;

import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import com.tzx.base.common.util.PosPropertyUtil;
import com.tzx.framework.common.constant.SysParameterCode;
import com.tzx.framework.common.util.HttpUtil;
import com.tzx.pos.po.springjdbc.dao.PosSoldOutDao;
import com.tzx.clientorder.acewillwechat.bo.SoldOutService;
import com.tzx.clientorder.acewillwechat.bo.WshPosEntranceService;
import com.tzx.clientorder.acewillwechat.common.constant.WLifeH5Constant;
import com.tzx.clientorder.acewillwechat.po.springjdbc.dao.OrderDao;


/**
 * Created by qin-gui on 2018-03-16.
 */
@Service(SoldOutService.NAME)
public class SoldOutServiceImp implements SoldOutService {

    private static final Logger logger = Logger.getLogger(SoldOutServiceImp.class);

    @Resource(name = WshPosEntranceService.NAME)
    private WshPosEntranceService wshService;
    
    @Resource(name = PosSoldOutDao.NAME)
    private PosSoldOutDao soldOutDao;
    @Resource(name = OrderDao.NAME)
    private OrderDao orderDao;
//    @Resource(name = PreorderDao.NAME)
//    private PreorderDao preorderDao;

//	private final String		IS_USER_WLIFE_KEY	= "IS_USER_WLIFE";
    
	/** 获取请求地址
	 * @param para
	 * @return
	 * @throws Exception
	 */
	public String getRequestUrl(String para) throws Exception
	{
		return PosPropertyUtil.getMsg("acewill.request.url") + para;
	}
	
    @Override
    public void wLifeSoldOut(String tenancyId, int storeId, Date reportDate) throws Exception {
//        Map<String, String> paramMap = new HashMap<String, String>();
//        //查询是否启用微生活点餐
//		if ("1".equals(soldOutDao.getSysParameter(tenancyId, storeId, IS_USER_WLIFE_KEY)))
//		{
//            //取消当前门店所有菜品沽清
//            cancelAllSoldOut(tenancyId);
//            //设置微生活的门店id和门店key
//            setWLifeShop(tenancyId, paramMap);
//            //查询菜品id和菜品规格id的list
//            List<JSONObject> list = soldOutDao.getItemUnits(tenancyId, storeId, reportDate);
//            if (null != list && list.size() > 0){
//                paramMap.put("dishs", list.toString());
//                String url = this.getRequestUrl(WLifeConstant.SOLD_OUT_URL); 
//                //logger.info("开始调用微生活的估清");
//                String msg = HttpUtil.sendPostRequest(url, paramMap);
//                logger.info("微生活估清接口返回信息：" + msg);
//            }else {
//                logger.info("沽清数量为0的菜品不存在");
//            }
//        }else {
//            //logger.info("未开启微生活会员");
//        }
    	if (!wshService.isWlifeOrderH5(tenancyId, storeId))
    	{
    		return;
    	}
		// 取消当前门店所有菜品沽清
		this.cancelAllSoldOut(tenancyId);

		// 设置估清
		this.soldOut(tenancyId, storeId, reportDate);
    }

    @Override
    public void soldOut(String tenancyId, int storeId, Date reportDate) throws Exception {
//        Map<String, String> paramMap = new HashMap<String, String>();
        //查询是否启用微生活点餐
		if (wshService.isWlifeOrderH5(tenancyId, storeId))
		{
			Map<String, String> paramMap = new HashMap<String, String>();
            //设置微生活的门店id和门店key
            setWLifeShop(tenancyId, paramMap);
            //查询菜品id和菜品规格id的list
            List<JSONObject> list = soldOutDao.getItemUnits(tenancyId, storeId, reportDate);
            if (null != list && list.size() > 0){
                paramMap.put("dishs", list.toString());
                String url = this.getRequestUrl(WLifeH5Constant.SOLD_OUT_URL);
                //logger.info("开始调用微生活的估清");
                String msg = HttpUtil.sendPostRequest(url, paramMap);
                logger.info("微生活估清接口返回信息：" + msg);
            }else {
             logger.info("不存在沽清为0的菜品");
            }
        }else {
            //logger.info("未开启微生活会员");
        }
    }

    @Override
    public void cancelSoldOut(String tenancyId, int storeId,String itemIds) throws Exception {
    	List<JSONObject> dishs = null;
		if (0 < itemIds.length())
		{
			dishs  = soldOutDao.getUnits(tenancyId, itemIds);
		}
		this.cancelSoldOut(tenancyId, storeId, dishs);
    }
    
    @Override
    public void cancelSoldOut(String tenancyId, int storeId,List<JSONObject> dishs) throws Exception {
//        Map<String, String> paramMap = new HashMap<String, String>();
        //查询是否启用微生活点餐
		if (wshService.isWlifeOrderH5(tenancyId, storeId))
		{
			Map<String, String> paramMap = new HashMap<String, String>();
            //设置微生活的门店id和门店key
            setWLifeShop(tenancyId, paramMap);
            paramMap.put("dishs", dishs.toString());

            String url = this.getRequestUrl( WLifeH5Constant.SOLD_OUT_CALCEL_URL);
            //logger.info("开始调用微生活的取消估清");
            String msg = HttpUtil.sendPostRequest(url, paramMap);
            logger.info("微生活取消估清接口返回信息：" + msg);
        }else {
            //logger.info("未开启微生活会员");
        }
    }

    /**
     * 取消当前门店所有菜品沽清
     * @param tenancyId
     * @throws Exception
     */
    private void cancelAllSoldOut(String tenancyId) throws Exception {
        Map<String, String> paramMap = new HashMap<String, String>();
        String url = this.getRequestUrl(WLifeH5Constant.SOLD_OUT_CANCEL_ALL_URL);
        //设置微生活的门店id和门店key
        setWLifeShop(tenancyId, paramMap);
        //logger.info("取消当前门店所有菜品沽清");
        String msg = HttpUtil.sendPostRequest(url, paramMap);
        logger.info("取消当前门店所有菜品沽清接口返回信息：" + msg);
    }

    /**
     * 设置微生活的门店id和门店key
     * @param tenancyId
     * @param paramMap
     * @throws Exception
     */
    private void setWLifeShop(String tenancyId, Map<String, String> paramMap) throws Exception{
        String shopId = "",shopKey = "";
        List<JSONObject> paramList = orderDao.getParam(tenancyId);
        if (null != paramList && paramList.size() > 0){
            for (int i=0; i < paramList.size(); i++){
                String code = paramList.get(i).optString("para_code");
                if (SysParameterCode.SHOP_ID.equals(code)){
                    shopId = paramList.get(i).optString("para_value");
                }else if (SysParameterCode.SHOP_KEY.equals(code)){
                    shopKey = paramList.get(i).optString("para_value");
                }
            }
        }
        paramMap.put("shop_id", shopId);
        paramMap.put("shopkey", shopKey);
    }
}
