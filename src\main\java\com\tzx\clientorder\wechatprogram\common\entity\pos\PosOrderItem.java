package com.tzx.clientorder.wechatprogram.common.entity.pos;

import java.util.List;

public class PosOrderItem implements Comparable<PosOrderItem>{
	
	private Integer							item_serial;
	private String							details_id;
	private String							item_id;
	private String							item_num;
	private String							item_name;
	private String							unit_id;
	private String							item_unit_name;
	private Double							item_count;
	private Double							item_price;
	private String							item_remark;
	private String							sale_mode;
	private String							seat_num;
	private String							setmeal_id;
	private Integer							setmeal_rwid;
	private String							assist_num;
	private String							assist_money;
	private String							assist_item_id;
	private String							item_property;
	private String							waitcall_tag;
	private String							item_taste;
	private String							item_class_id;
	private Double							item_amount;
	private List<PosOrderItemMethod>		method;
	private int								order_number	= 0;
	private String 							openid;

    private String				default_state;

    public String getDefault_state() {
        return default_state;
    }

    public void setDefault_state(String default_state) {
        this.default_state = default_state;
    }

	public Integer getItem_serial() {
		return item_serial;
	}
	public void setItem_serial(Integer item_serial) {
		this.item_serial = item_serial;
	}
	public String getDetails_id() {
		return details_id;
	}
	public void setDetails_id(String details_id) {
		this.details_id = details_id;
	}
	public String getItem_id() {
		return item_id;
	}
	public void setItem_id(String item_id) {
		this.item_id = item_id;
	}
	public String getItem_num() {
		return item_num;
	}
	public void setItem_num(String item_num) {
		this.item_num = item_num;
	}
	public String getItem_name() {
		return item_name;
	}
	public void setItem_name(String item_name) {
		this.item_name = item_name;
	}
	public String getUnit_id() {
		return unit_id;
	}
	public void setUnit_id(String unit_id) {
		this.unit_id = unit_id;
	}
	public String getItem_unit_name() {
		return item_unit_name;
	}
	public void setItem_unit_name(String item_unit_name) {
		this.item_unit_name = item_unit_name;
	}
	public Double getItem_count() {
		return item_count;
	}
	public void setItem_count(Double item_count) {
		this.item_count = item_count;
	}
	public Double getItem_price() {
		return item_price;
	}
	public void setItem_price(Double item_price) {
		this.item_price = item_price;
	}
	public String getItem_remark() {
		return item_remark;
	}
	public void setItem_remark(String item_remark) {
		this.item_remark = item_remark;
	}
	public String getSale_mode() {
		return sale_mode;
	}
	public void setSale_mode(String sale_mode) {
		this.sale_mode = sale_mode;
	}
	public String getSeat_num() {
		return seat_num;
	}
	public void setSeat_num(String seat_num) {
		this.seat_num = seat_num;
	}
	public String getSetmeal_id() {
		return setmeal_id;
	}
	public void setSetmeal_id(String setmeal_id) {
		this.setmeal_id = setmeal_id;
	}
	public Integer getSetmeal_rwid() {
		return setmeal_rwid;
	}
	public void setSetmeal_rwid(Integer setmeal_rwid) {
		this.setmeal_rwid = setmeal_rwid;
	}
	public String getAssist_num() {
		return assist_num;
	}
	public void setAssist_num(String assist_num) {
		this.assist_num = assist_num;
	}
	public String getAssist_money() {
		return assist_money;
	}
	public void setAssist_money(String assist_money) {
		this.assist_money = assist_money;
	}
	public String getAssist_item_id() {
		return assist_item_id;
	}
	public void setAssist_item_id(String assist_item_id) {
		this.assist_item_id = assist_item_id;
	}
	public String getItem_property() {
		return item_property;
	}
	public void setItem_property(String item_property) {
		this.item_property = item_property;
	}
	public String getWaitcall_tag() {
		return waitcall_tag;
	}
	public void setWaitcall_tag(String waitcall_tag) {
		this.waitcall_tag = waitcall_tag;
	}
	public String getItem_taste() {
		return item_taste;
	}
	public void setItem_taste(String item_taste) {
		this.item_taste = item_taste;
	}
	public List<PosOrderItemMethod> getMethod() {
		return method;
	}
	public void setMethod(List<PosOrderItemMethod> method) {
		this.method = method;
	}

	public String getItem_class_id() {
		return item_class_id;
	}

	public void setItem_class_id(String item_class_id) {
		this.item_class_id = item_class_id;
	}

	public Double getItem_amount() {
		return item_amount;
	}

	public void setItem_amount(Double item_amount) {
		this.item_amount = item_amount;
	}
	public int getOrder_number()
	{
		return order_number;
	}
	public void setOrder_number(int order_number)
	{
		this.order_number = order_number;
	}

	public String getOpenid() {
		return openid;
	}

	public void setOpenid(String openid) {
		this.openid = openid;
	}

	@Override
	public int compareTo(PosOrderItem o)
	{
		if (this.getOrder_number() > o.getOrder_number())
		{
			return 1;
		}
		else if (this.getOrder_number() == o.getOrder_number())
		{
			return 0;
		}
		return -1;
	}
}
