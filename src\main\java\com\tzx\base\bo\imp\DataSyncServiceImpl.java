/**
  * @Description: TODO
  * @author: z<PERSON><PERSON>n
  * @version: 1.0
  * @Date: 2018年4月3日
*/
package com.tzx.base.bo.imp;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;

import org.apache.log4j.Logger;
import org.postgresql.copy.CopyManager;
import org.postgresql.core.BaseConnection;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import com.tzx.base.bo.DataSyncService;
import com.tzx.base.bo.ProcessMessageService;
import com.tzx.base.listener.PosSystemInitListener;
import com.tzx.base.po.springjdbc.dao.DataSyncDao;
import com.tzx.base.service.servlet.PushProcessMessage;
import com.tzx.framework.common.constant.Constant;
import com.tzx.framework.common.util.SpringConext;
import com.tzx.framework.common.util.Tools;
import net.lingala.zip4j.core.ZipFile;

/**
  * @Description: TODO
  * @param:
  * @author: zhouquan
  * @version: 1.0
  * @Date: 2018年4月3日
*/
@Service(DataSyncService.NAME)
public class DataSyncServiceImpl implements DataSyncService{

	private Logger logger = Logger.getLogger(DataSyncServiceImpl.class);
	private Logger tipLogger = Logger.getLogger("tip");
	
	@Autowired
	private DataSyncDao	dataSyncDao;
    @Autowired
    private JdbcTemplate jdbcTemplate;
	
	@Override
	public boolean BaseDataSyncFromSaas(ZipFile zf,String baseFilePath,String downTableCode,String tables,HashMap<String,String> recordCountMap) {			
		boolean bo = false;		
		//DataSyncDao dataSyncDao = (DataSyncDao)SpringConext.getBean(DataSyncDao.NAME);
		ProcessMessageService processMessageService = (ProcessMessageService) SpringConext.getBean(ProcessMessageService.NAME);
		Map<String, String> systemMap = Constant.getSystemMap();
		String tenancy_id = systemMap.get("tenent_id");
		try {
			logger.info("开始加压缩加密文件...");
			if (zf.isEncrypted()) {
				String pwd = tenancy_id; 
				zf.setPassword(pwd);
			}
			// 解压文件
			zf.extractAll(baseFilePath);	
			logger.info("加压缩加密文件成功。");
			// 执行创建数据表语句
			logger.info("开始创建临时表...");
			List<String> sqlList = Tools.getListFromFile(baseFilePath+ File.separator + downTableCode +File.separator+ "CREATE_SQL.txt");			
			boolean result = dataSyncDao.execBatchSqls(sqlList);	
			if(result == false){
				logger.error("基础资料下发过程中，创建数据表过程中出现异常。");
				return false;
			}
			logger.info("创建临时表成功。");
			// 导入数据
			logger.info("开始往临时表中导入数据...");
			String[] tableList = tables.split(PushProcessMessage.SPLIT_CHARACTER);
			String filePath = baseFilePath+ File.separator + downTableCode;
			String fileName = "";
			boolean b = false;
			for(int i =0;i<tableList.length;i++){
				fileName = tableList[i].trim();
				if(!"".equals(fileName)){
					b = copyFormFile(filePath,fileName,recordCountMap);
					if(b==false){
						logger.error("从文件("+filePath+")往数据表("+fileName+")中导入数据出现异常！");
						return false;
					}
				}
			}
			logger.info("往临时表中导入数据成功。");
			/*
			 * 1、修改原表名,加前缀 del_
			 * 2、把前缀为tmp_的表去掉前缀
			 * 2、删除前缀为del_的表，为避免死锁产生，Drop表操作最后执行，且等待一定时间后执行。
			 */
			logger.info("开始改临时表为正式表...");
			int ran = new Random().nextInt(1000);
			sqlList = new ArrayList<String>();
			List<String> delSqlList = new ArrayList<String>();
			String tableName = "";
			String tmp = "0";
			for(int i =0;i<tableList.length;i++){
				tableName = tableList[i].trim();
				if(!"".equals(tableName)){
					// 判断是否存在表
					tmp = processMessageService.ifExistSequenceOrTable(tableName);
					if(!"0".equals(tmp)){
						sqlList.add("alter table "+tableName+" rename to del_"+ran+tableName);
					}					
					sqlList.add("alter table tmp_"+tableName+" rename to "+tableName);					
					delSqlList.add("DROP TABLE if EXISTS del_"+ran+tableName);					 
				}
			}
			//sqlList.add("DROP table organ1");   // 测试事务的回滚
			bo = dataSyncDao.execBatchSqls(sqlList);
			if(bo == false){
				logger.error("执行修改表名和删除表过程中出现异常，异常信息：");
				for(int i =0;i<sqlList.size();i++){
					logger.info(sqlList.get(i));
				}
				return bo;
			}
			logger.info("改临时表为正式表成功。");

			// 修改表（ID字段）对应的序列名称
			logger.info("开始处理序列相关操作...");
			String num = "0";
			tmp = "0";
			sqlList = new ArrayList<String>();
			for(int i =0;i<tableList.length;i++){
				tableName = tableList[i].trim();
				if(!"".equals(tableName)){
					num = processMessageService.ifExistSequenceOrTable("tmp_"+tableName+"_id_seq");
					if(!"0".equals(num)){
						tmp = processMessageService.ifExistSequenceOrTable(tableName+"_id_seq");
						if("0".equals(tmp)){
							// 修改序列名称
							sqlList.add("ALTER SEQUENCE "+"tmp_"+tableName+"_id_seq"+" RENAME TO "+tableName+"_id_seq");
							// 初始化序列值
							//sqlList.add("select setval('"+tableName+"_id_seq', (select max(id) from "+tableName+")+1)");
						}else{
							sqlList.add("ALTER SEQUENCE "+tableName+"_id_seq"+" RENAME TO "+tableName+"_id_seq_"+System.currentTimeMillis());
							// 修改序列名称
							sqlList.add("ALTER SEQUENCE "+"tmp_"+tableName+"_id_seq"+" RENAME TO "+tableName+"_id_seq");
							
						}
					}
				}
			}
			if(sqlList.size() > 0){
				bo = dataSyncDao.execBatchSqls(sqlList);
				if(bo == false){
					logger.error("修改字段ID对应的序列名称出现异常！");
					return bo;
				}
			}
			
			// 初始化序列值
			num = "0";
			tmp = "0";
			sqlList = new ArrayList<String>();
			for(int i =0;i<tableList.length;i++){
				tableName = tableList[i].trim();
				if(!"".equals(tableName)){	
					tmp = processMessageService.ifExistSequenceOrTable(tableName+"_id_seq");
					if(!"0".equals(tmp)){
						// 初始化序列值
						sqlList.add("select setval('"+tableName+"_id_seq', (select max(id) from "+tableName+")+1) as num ");
					}
				}
			}
			if(sqlList.size() > 0){
				for(int i=0;i<sqlList.size();i++){
					processMessageService.initSeqValue(sqlList.get(i));
				}
			}
			logger.info("处理序列相关操作成功。");
			
			/*
			 * 1、不用事物方式逐条执行Drop Table语句，事物方式容易引起死锁。
			 * 2、如果有未执行成功的语句， 在下次下发之前判断是否有del_打头的表，如果有则清除。
			 */
			Thread.sleep(2000);   // 休眠两秒，等待被删除表上执行的SQL语句结束，防止死锁发生。
			if(delSqlList.size() > 0){
				for(String sql : delSqlList){
					dataSyncDao.execute(tenancy_id, sql);
				}
			}
			
			return bo;
		} catch (Exception e) {
			//e.printStackTrace();
			bo = false;
		}
		return bo;
	}
	
	/**
	 * @Description 使用copy方式把数据文件导入到指定表中。
	 * @param filePath ： 文件路径
	 * @param fileName : 文件名称
	 * @return 
	 * @exception
	 * <AUTHOR> email:<EMAIL>
	 * @version 1.0  2018-06-14
	 * @see
	 */
	public boolean copyFormFile(String filePath,String fileName,HashMap<String,String> recordCountMap) throws SQLException, IOException {               
		boolean bo = false;
		FileInputStream fileInputStream = null; 
		Connection connection = null;
		String tableName = "";
	    try {
            connection =jdbcTemplate.getDataSource().getConnection();
	    	CopyManager copyManager = new CopyManager((BaseConnection)connection.getMetaData().getConnection()); 
	        fileInputStream = new FileInputStream(filePath+File.separator+fileName+".txt"); 
	        tableName = "tmp_"+ fileName;
	        long num = copyManager.copyIn("COPY " + tableName + " FROM STDIN", fileInputStream); 
	        bo = true;
	        /*
	         * 导入数据监测，判断导入的数据行数是否与下发的数据行数相等
	         */
	        String recordCount = recordCountMap.get(fileName.toLowerCase().trim());
	        if(recordCount == null || "".equals(recordCount)){
	        	bo = false;
		    	logger.error("获取下发表数据记录行数发生异常！");
	        }
	        if(Integer.valueOf(recordCount) != (int)num){
	        	bo = false;
	        	logger.error(tableName+"表导入数据行数与导出的数据行数不一致，导入数据行数："+num+",导出数据行数："+recordCount+"。");
	        }   
	        /*
	         * 通过SQL语句查询到的数据行数与导入数据行数比较
	         */
	        int sumCount = dataSyncDao.getInt("select count(*) from " +tableName);
	        if(sumCount != (int)num){
	        	bo = false;
	        	String errorInfo = tableName+"表导入数据行数与导入后查询到实际行数不一致，导入数据行数："+num+",导入后查询到实际行数："+sumCount+"。";
	        	logger.error(errorInfo);
	        	tipLogger.error(errorInfo);
	        }
	    }catch(Exception e){
	    	bo = false;
	    	logger.error("数据导入表"+tableName+"失败。");
	    	String txt = readFileText(filePath+File.separator+fileName+".txt");	    	
	    	logger.error("表"+tableName+"数据："+txt);
	    	logger.error(e.getMessage());
	    }finally {  
	        if (fileInputStream != null) {  
	            try {  
	                fileInputStream.close();  
	            } catch (IOException e) {  
	               //不要使用 //e.printStackTrace();，如果有必要，用logger.error替代;
	            }  
	        } 
	        if(connection != null){
	        	connection.close();
	        }
	    }  
	    return bo;
	} 
	
	/**
	 * @Description 读取txt文件内容
	 * @param fileName : 文件路径
	 * @return 
	 * @exception
	 * <AUTHOR> email:<EMAIL>
	 * @version 1.0  2018-09-14
	 * @see
	 */
	private String readFileText(String fileName){
		String returnStr = "";
		File f = new File(fileName);  
    	InputStream input;
		try {
			input = new FileInputStream(f);
			BufferedReader br = new BufferedReader(new InputStreamReader(input));
	    	StringBuilder sb = new StringBuilder();
	    	String s = null;
	        while((s = br.readLine())!=null){	//使用readLine方法，一次读一行
	        	sb.append(System.lineSeparator()+s);
	        }
	        returnStr = sb.toString();
	        br.close();
	    	input.close(); 
		} catch (Exception e) {
			//e.printStackTrace();
		} 
		return returnStr;
	}
	
	
//	@Override
//	public boolean BaseDataSyncFromSaas(ZipFile zf) {
//		
//		DataSyncDao dataSyncDao = (DataSyncDao) SpringConext.getBean(DataSyncDao.NAME);
//		
//		boolean temp = false;
//		Map<String, String> systemMap = Constant.getSystemMap();
//		String tenancy_id = systemMap.get("tenent_id");
//		try {
//			if (zf.isEncrypted()) {
//				String pwd = tenancy_id; 
//				zf.setPassword(pwd);
//			}
//			List<?> fileHeaderList = zf.getFileHeaders();
//			
//			ListSort ls = null;
//			Map<String,Object> map = new HashMap<String, Object>();
//			List<ListSort> listTableNames = new ArrayList<ListSort>();
//			for (int j = 0; j < fileHeaderList.size(); j++) {
//				FileHeader fileHeader = (FileHeader) fileHeaderList.get(j);
//				String[] tableNames = fileHeader.getFileName().split("/");
//				if(tableNames.length>1){
//					String tablename = tableNames[1];
//					map.put(tablename, fileHeader);
//					ls = new ListSort(tablename);
//					listTableNames.add(ls);
//				}
//			}
//			
//			// 排序
//	        Arrays.sort(listTableNames.toArray());
//	        
//			List<String> listString = new ArrayList<String>();
//			for(ListSort lst : listTableNames){
//				listString.add(lst.getTableName());
//			}
//		
//			
//			for (int i = 0; i < listString.size(); i++) {
//				logger.info("正在执行第"+(i+1)+"个文件");
//				String tableName = listString.get(i);
//				FileHeader fileHeader = (FileHeader) map.get(tableName);
//				if (fileHeader != null) {
//					String outFilePath = Thread.currentThread().getContextClassLoader().getResource("file").getPath()+"/" + fileHeader.getFileName();
//					File outFile = new File(outFilePath);
//					if (fileHeader.isDirectory()) {
//						outFile.mkdirs();
//						outFile.delete();
//						continue;
//					}
//					File parentDir = outFile.getParentFile();
//					if (!parentDir.exists()) {
//						parentDir.mkdirs();
//					}
//
//					ZipInputStream is = zf.getInputStream(fileHeader);
//					OutputStream os = new FileOutputStream(outFile);
//					int readLen = -1;
//					byte[] buff = new byte[2048];
//					while ((readLen = is.read(buff)) != -1) {
//						os.write(buff, 0, readLen);
//					}
//					os.close();
//					os = null;
//					is.close();
//					is = null;
//					UnzipUtil.applyFileAttributes(fileHeader, outFile);
//					StringBuilder sb = new StringBuilder();
//					List<String> list = new ArrayList<String>();
//					InputStreamReader isr = new InputStreamReader(new FileInputStream(outFile),"UTF-8");
//					BufferedReader bd = new BufferedReader(isr);					
//					String[] sqList= null;
//					String str;
//					while ((str = bd.readLine()) != null) {
//						sb.append(str);
//					}
//					JSONArray js = JSONArray.parseArray(sb.toString());					
//					for(Object object : js){
//						list.add(object.toString());
//						sqList = list.toArray(new String[list.size()]);
//					}					
//					
//					dataSyncDao.saveBaseData(sqList);	
//					
//					bd.close();
//					isr.close();					
//					outFile.delete();					
//					//temp = true;
//				}
//			}
//			temp = true;
////			if(temp){
////				String url = "http://127.0.0.1:8081/hq/boh/dataSyncResultRest/req";
////				int organId = Integer.parseInt(systemMap.get("store_id"));
////				Map<String,String> mapSuccess = new HashMap<String, String>();
////				mapSuccess.put("tenancyId",tenancy_id);
////				mapSuccess.put("storeId", String.valueOf(organId));
////				mapSuccess.put("success", "true");
////				mapSuccess.put("message", "数据处理成功完成");
////				HttpUtil.http(url, mapSuccess);	
////			}
//			return temp;
//		} catch (Exception e) {
//			// TODO Auto-generated catch block
//			//e.printStackTrace();
//			temp = false;
////			if(temp){
////				String url = "http://127.0.0.1:8081/hq/boh/dataSyncResultRest/req";
////				int organId = Integer.parseInt(systemMap.get("store_id"));
////				Map<String,String> mapFail = new HashMap<String, String>();
////				mapFail.put("tenancyId",tenancy_id);
////				mapFail.put("storeId", String.valueOf(organId));
////				mapFail.put("success", "false");
////				mapFail.put("message", "数据处理失败");
////				HttpUtil.http(url, mapFail);	
////			}
//			return temp;
//		}
//		//return temp;
//	}
	
//	class  ListSort implements Comparable<ListSort>{
//		
//		private String tableName;
//
//		public String getTableName() {
//			return tableName;
//		}
//
//		public void setTableName(String tableName) {
//			this.tableName = tableName;
//		}
//
//		public ListSort() {
//		}
//
//		public ListSort(String tableName) {
//			this.tableName = tableName;
//		}
//
//		@Override
//		public int compareTo(ListSort o) {
//			// TODO Auto-generated method stub
//			int compareto = this.tableName.compareTo(tableName);
//			if(compareto == 0){
//				return compareto ;
//			}else{
//				return compareto;
//			}
//		}
//	}

}

