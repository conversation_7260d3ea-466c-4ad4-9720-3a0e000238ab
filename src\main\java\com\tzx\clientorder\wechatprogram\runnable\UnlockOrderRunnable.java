package com.tzx.clientorder.wechatprogram.runnable;

import org.apache.log4j.Logger;

import com.tzx.clientorder.wechatprogram.bo.adapter.PromptServiceAdapter;

public class UnlockOrderRunnable implements Runnable
{
	private static final Logger logger = Logger.getLogger(UnlockOrderRunnable.class);
	
	private String	tenancyId;
	private Integer	storeId;
	private String	billNum;
	private String 	channel;

	public UnlockOrderRunnable(String tenancyId, Integer storeId, String billNum, String channel)
	{
		super();
		this.tenancyId = tenancyId;
		this.storeId = storeId;
		this.billNum = billNum;
		this.channel = channel;
	}

	@Override
	public void run()
	{
		try
		{
			// 休眠500毫秒,等待数据提交完成;
			Thread.sleep(500);

			PromptServiceAdapter adapter = new PromptServiceAdapter(channel);
			adapter.unlockOrder(tenancyId, storeId, billNum);
		}
		catch (Exception e)
		{
			logger.info("解锁订单失败:", e);
		}
	}

	public void setTenancyId(String tenancyId)
	{
		this.tenancyId = tenancyId;
	}

	public void setStoreId(Integer storeId)
	{
		this.storeId = storeId;
	}

	public void setBillNum(String billNum)
	{
		this.billNum = billNum;
	}

	public void setChannel(String channel)
	{
		this.channel = channel;
	}
}
