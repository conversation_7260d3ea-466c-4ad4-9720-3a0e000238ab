package com.tzx.clientorder.acewillwechat.bo.imp;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import net.sf.json.JSONObject;

import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import com.tzx.clientorder.acewillwechat.bo.HoldBillService;
import com.tzx.clientorder.acewillwechat.bo.OrderService;
import com.tzx.clientorder.acewillwechat.bo.PreorderService;
import com.tzx.clientorder.acewillwechat.po.springjdbc.dao.PreorderDao;
import com.tzx.clientorder.acewillwechat.po.springjdbc.dao.TableStateDao;
import com.tzx.clientorder.common.constant.WLifeConstant;
import com.tzx.framework.common.constant.Oper;
import com.tzx.framework.common.constant.Type;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.util.CommonUtil;
import com.tzx.member.acewill.bo.AcewillCustomerService;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.bo.PosDiscountService;

/**
 * 预结订单
 * Created by qin-gui on 2018-03-14.
 */
@Service(PreorderService.NAME)
public class PreorderServiceImp implements PreorderService{

    private static final Logger logger = Logger.getLogger(PreorderServiceImp.class);

    @Resource(name = PreorderDao.NAME)
    private PreorderDao preorderDao;
    @Resource(name = OrderService.NAME)
    private OrderService orderService;
    @Resource(name = PosDiscountService.NAME)
    private PosDiscountService posDiscountService;
    @Resource(name = HoldBillService.NAME)
    private HoldBillService holdBillService;
    @Resource(name = AcewillCustomerService.NAME)
    private AcewillCustomerService acewillCustomerService;
    @Resource(name = TableStateDao.NAME)
    private TableStateDao tableStateDao;

    @Override
    public JSONObject process(String tenancyId, Integer storeId, JSONObject jsonObject) throws Exception {

        //先查询是否门店锁单了，是门店已锁单的话就不处理，不再上传订单
        JSONObject lockObj =tableStateDao.getTableOpen(tenancyId, storeId, jsonObject.optString("table_sno"));
        if (null != lockObj){
            String lockState = lockObj.optString("lock_state");
            String lockType = lockObj.optString("lock_type");
           if ("1".equals(lockState) && SysDictionary.CHANEL_MD01.equals(lockType))
               return null;
        }
        //查询账单pos_bill，判断账单有没折扣
        JSONObject billObject = preorderDao.getBillDiscount(tenancyId, storeId, jsonObject.optString("table_sno"));
        if(null != billObject && !"0".equals(billObject.optString("discount_mode_id"))  ){
            //如果账单有折扣，直接上传dingd
            logger.info("账单有折扣，直接上传预结订单>>>>");
        }else {
            //查询是否启用微生活会员
            String memberType = preorderDao.getMemberType(tenancyId);
            if (null != memberType && SysDictionary.CUSTOMER_TYPE_ACEWILL.equals(memberType)){
                //调用查询会员接口,如果有会员折扣或者会员价，调用账单折扣，否则直接上传
                JSONObject member = jsonObject.optJSONObject("member");
                String cno = null;
				if (null != member)
				{
					// 会员卡编号
					cno = member.optString("cno");
				}

				if (CommonUtil.hv(cno))
				{
                    JSONObject obj = new JSONObject();
                    obj.put("card_code", cno);
                    List<JSONObject> dataList = new ArrayList<JSONObject>();
                    dataList.add(obj);
                    
                    Data requestData = new Data();
                    requestData.setStore_id(storeId);
                    requestData.setTenancy_id(tenancyId);
                    requestData.setData(dataList);
                    
                    Data customerUserInfo = acewillCustomerService.getAcewillCustomerUserInfo(requestData);
                    //System.err.println("查询会员返回信息：" + customerUserInfo.toString());
                    List<JSONObject> userList = (List<JSONObject>) customerUserInfo.getData();
                    if (null != userList && userList.size() > 0){
                        //折扣率，0-100
                        Integer rate = userList.get(0).optInt("rate");
                        //是否会员价，0为否，1为是
                        String is_vipprice = userList.get(0).optString("is_vipprice");
                        //账单折扣是走会员折扣
                        if (null != rate && rate > 0 && rate < 100){
                            billDiscount(tenancyId, storeId, jsonObject.optString("table_sno"), rate);
                        }else if (CommonUtil.hasText(is_vipprice) && "1".equals(is_vipprice)){
                            //账单折扣走会员价
                            billDiscount(tenancyId, storeId, jsonObject.optString("table_sno"), null);
                        }else {
                            logger.info("微生活会员既没折扣也不走会员价，直接上传预结订单>>>>");
                        }
                    }
                }
            }else {
                //logger.info("未开启微生活会员，直接上传预结订单>>>>");
            }
        }

        //logger.info("预结订单上传，更新锁单表的openid >>>>");
        holdBillService.saveOpenId(tenancyId, storeId, jsonObject.optString("table_sno"), jsonObject.optString("openid"));
        //logger.info("开始进行预结订单上传>>>>");
        orderService.getOrderInfo(tenancyId, storeId, jsonObject.optString("table_sno"), WLifeConstant.OPT_TYPE_PAY);
        return null;
    }

    /**
     * 进行账单折扣处理
     * @param tenancyId
     * @param storeId
     * @param tableCode
     * @param rate
     * @throws Exception
     */
    private void billDiscount(String tenancyId, int storeId, String tableCode, Integer rate) throws Exception{
        //调用账单折扣
        Data data= new Data();
        data.setType(Type.BILL_DISCOUNT);
        data.setOper(Oper.add);
        data.setTenancy_id(tenancyId);
        data.setStore_id(storeId);
        List<JSONObject> list = getDiscountParam(tenancyId, storeId, tableCode, rate);
        data.setData(list);
        //logger.info("开始进行折扣处理>>>>"+data.getData().toString());
        posDiscountService.newBillDiscount(data);
    }

    /**
     * 查询会员折扣需要的参数
     * @param tenancyId
     * @param storeId
     * @param tableCode
     * @return
     * @throws Exception
     */
    private List<JSONObject> getDiscountParam(String tenancyId, int storeId, String tableCode, Integer rate) throws Exception{
        List<JSONObject> list = new ArrayList<JSONObject>();
        JSONObject object = preorderDao.getBillDiscount(tenancyId, storeId, tableCode);
        if (null == object)
        {
            return null;
        }
        //0是取消折扣，1是账单折扣
        object.put("mode", 1);
        //操作员号
        object.put("opt_num", "");
        //批准人编号
        object.put("manager_num", "");
        //折让金额
        object.put("discountr_amount", 0);
        //折让方式
        if (null != rate){
            //会员折扣
            object.put("discount_mode", SysDictionary.DISCOUNT_MODE_5);
            object.put("discount_rate", rate);
        }else {
            //会员价
            object.put("discount_mode", SysDictionary.DISCOUNT_MODE_6);
        }
        
        List<JSONObject> optStateInfoList = posDiscountService.getOptStateInfo(tenancyId, storeId, object.optString("report_date"));
		if (optStateInfoList.size() > 0) {
			JSONObject optStateInfo = optStateInfoList.get(0);
			object.put("pos_num", optStateInfo.getString("pos_num"));
			object.put("opt_num", optStateInfo.getString("opt_num"));
			object.put("manager_num", optStateInfo.getString("opt_num"));
			object.put("shift_id", optStateInfo.optInt("shift_id"));
		}
		
        list.add(object);
        return list;
    }
}
