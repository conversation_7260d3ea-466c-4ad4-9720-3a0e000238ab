package com.tzx.member.yazuo.thread;

import java.util.Date;

import org.apache.log4j.Logger;

import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.util.SpringConext;
import com.tzx.member.yazuo.bo.YazuoCrmService;
import com.tzx.pos.base.util.ParamUtil;

import net.sf.json.JSONObject;

/**
 * 日结通知
 * 
 * <AUTHOR>
 *
 */
public class SettlementDayRunnable implements Runnable
{
	private static final Logger	logger	= Logger.getLogger(SettlementDayRunnable.class);

	private String				tenancyId;
	private Integer				storeId;
	private Date				reportDate;

	public SettlementDayRunnable(String tenancyId, Integer storeId, Date reportDate)
	{
		super();
		this.tenancyId = tenancyId;
		this.storeId = storeId;
		this.reportDate = reportDate;
	}
	
	public SettlementDayRunnable(Data param)
	{
		super();
		this.tenancyId = param.getTenancy_id();
		this.storeId = param.getStore_id();

		if (null == param.getData() || 0 == param.getData().size())
		{
			logger.info("请求参数为空");
		}
		JSONObject paramJson = JSONObject.fromObject(param.getData().get(0));
		this.reportDate = ParamUtil.getDateValueByObject(paramJson, "report_date");
	}

	@Override
	public void run()
	{
		try
		{
			YazuoCrmService yazuoCrmService = (YazuoCrmService) SpringConext.getBean(tenancyId, YazuoCrmService.NAME);
			yazuoCrmService.settlementDay(tenancyId, storeId, reportDate);
		}
		catch (Exception e)
		{
			logger.info("日结通知失败:", e);
		}
	}
}
