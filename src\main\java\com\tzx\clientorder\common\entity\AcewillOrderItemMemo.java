package com.tzx.clientorder.common.entity;

import java.util.List;

public class AcewillOrderItemMemo
{
	private Integer								omkid;
	private String								title;
	private Integer								is_radio;
	private Integer								is_must;
	private Integer								min;
	private Integer								max;
	private List<AcewillOrderItemMemoDetail>	items;

	public Integer getOmkid()
	{
		return omkid;
	}

	public void setOmkid(Integer omkid)
	{
		this.omkid = omkid;
	}

	public String getTitle()
	{
		return title;
	}

	public void setTitle(String title)
	{
		this.title = title;
	}

	public Integer getIs_radio()
	{
		return is_radio;
	}

	public void setIs_radio(Integer is_radio)
	{
		this.is_radio = is_radio;
	}

	public Integer getIs_must()
	{
		return is_must;
	}

	public void setIs_must(Integer is_must)
	{
		this.is_must = is_must;
	}

	public Integer getMin()
	{
		return min;
	}

	public void setMin(Integer min)
	{
		this.min = min;
	}

	public Integer getMax()
	{
		return max;
	}

	public void setMax(Integer max)
	{
		this.max = max;
	}

	public List<AcewillOrderItemMemoDetail> getItems()
	{
		return items;
	}

	public void setItems(List<AcewillOrderItemMemoDetail> items)
	{
		this.items = items;
	}
}
