package com.tzx.clientorder.acewillwechat.bo.imp;

import java.util.List;

import javax.annotation.Resource;

import net.sf.json.JSONObject;

import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import com.tzx.clientorder.acewillwechat.bo.HoldBillService;
import com.tzx.clientorder.acewillwechat.bo.OrderService;
import com.tzx.clientorder.acewillwechat.po.springjdbc.dao.HoldBillDao;
import com.tzx.clientorder.common.constant.WLifeConstant;
import com.tzx.framework.common.util.DateUtil;
import com.tzx.framework.common.util.CommonUtil;
import com.tzx.pos.base.constant.SysDictionary;

/**
 * 锁单业务层
 * Created by qin-gui on 2018-03-14.
 */
@Service(HoldBillService.NAME)
public class HoldBillServiceImp implements HoldBillService {

    private static final Logger logger = Logger.getLogger(HoldBillServiceImp.class);

    @Resource(name = HoldBillDao.NAME)
    private HoldBillDao holdBillDao;
    @Resource(name = OrderService.NAME)
    private OrderService orderService;
//    @Resource(name = "genericDaoImpl")
//    private GenericDao		dao;

    @Override
    public JSONObject process(String tenancyId, Integer storeId, JSONObject jsonObject) throws Exception {
        //平台订单号
        String orderNum = jsonObject.optString("out_order_id");
        if (!CommonUtil.hasText(orderNum))
            return null;
        JSONObject holdBill = holdBillDao.getBillLockStatus(tenancyId, orderNum);
        String lockState = holdBill.optString("lock_state");
        String lockType = holdBill.optString("lock_type");
        String tableCode = holdBill.optString("table_code");
        
		if ("0".equals(lockState) || SysDictionary.CHANEL_WX02.equals(lockType))
		{// 未锁单 或线上锁单
			// 更新桌位状态为锁单
			holdBillDao.updateTableState(tenancyId, storeId, tableCode);

			if (holdBill.optBoolean("is_null_lock"))
			{
				// 新增锁单记录
				insertBillLock(tenancyId, storeId, holdBill);
			}
			else
			{
				// 更新锁单
				holdBillDao.updateBillLock(tenancyId, holdBill.optString("bill_num"), storeId);
			}

			// 上传订单
			orderService.getOrderInfo(tenancyId, storeId, tableCode, WLifeConstant.OPT_TYPE_LOCK);
		}
		else
		{
			logger.error("锁定订单失败,门店已锁单");
		}
        
//        if ("0".equals(lockState)){
//            //未锁单
//            //更新桌位状态为锁单
//            holdBillDao.updateTableState(tenancyId, holdBill.optInt("store_id"), holdBill.optString("table_code"));
//            //新增锁单记录
//            insertBillLock(tenancyId, holdBill);
//            //上传订单
//            orderService.getOrderInfo(tenancyId, holdBill.optInt("store_id"), holdBill.optString("table_cpde"), WLifeConstant.OPT_TYPE_LOCK);
//        }else if ("1".equals(lockType)){
//            //已锁单，门店锁单
//        }else if ("2".equals(lockType)){
//            //已锁单，线上锁单
//            //更新锁单
//            holdBillDao.updateBillLock(tenancyId, holdBill.optString("bill_num"), holdBill.optInt("store_id"));
//            holdBillDao.updateTableState(tenancyId, holdBill.optInt("store_id"), holdBill.optString("table_code"));
//            //上传订单
//            orderService.getOrderInfo(tenancyId, holdBill.optInt("store_id"), holdBill.optString("table_cpde"), WLifeConstant.OPT_TYPE_LOCK);
//        }
        return null;
    }

    /**
     * 新增锁单记录
     * @param tenancyId
     * @param holdBill
     */
    private void insertBillLock(String tenancyId,Integer storeId, JSONObject holdBill) throws Exception{
        JSONObject lockObject = new JSONObject();
        //lockObject.put("id", UUIDUtil.generateGUID());
        lockObject.put("tenancy_id", tenancyId);
        lockObject.put("store_id", storeId);
        lockObject.put("bill_num", holdBill.optString("bill_num"));
        //锁单类型 MD01是门店，WX02是线上锁单
        lockObject.put("lock_type", SysDictionary.CHANEL_WX02);
        //锁单状态：0为未锁单，1为锁单
        lockObject.put("lock_state", 1);
        lockObject.put("open_id", null);
        lockObject.put("customer_id", "");
        lockObject.put("lock_time", DateUtil.getNowDateYYDDMMHHMMSS());
        //解锁类型
        lockObject.put("unlock_type", "");
        lockObject.put("unlock_time", "");
        //账单状态：0是未结账，1是已结账
        lockObject.put("bill_state", 0);
        this.holdBillDao.insertIgnorCase(tenancyId, "pos_bill_lock", lockObject);
    }

    @Override
    public void saveOpenId(String tenancyId, int storeId, String tableCode, String openId) throws Exception {
        List<JSONObject> list = holdBillDao.getBillLockOpenId(tenancyId, storeId, tableCode);
        if (null != list && list.size() > 0){
            //修改openid为最新的
            //logger.info("更新平台传过来的openid");
            JSONObject lockObject = new JSONObject();
            lockObject.put("id", list.get(0).optString("id"));
            lockObject.put("open_id", openId);
            holdBillDao.updateIgnorCase(tenancyId, "pos_bill_lock", lockObject);
        }else {
            //新增一条记录
            //logger.info("记录平台传过来的用户openid");
            JSONObject lockObject = new JSONObject();
            //lockObject.put("id", UUIDUtil.generateGUID());
            lockObject.put("tenancy_id", tenancyId);
            lockObject.put("store_id", storeId);
            lockObject.put("open_id", openId);
            //未锁单
            lockObject.put("lock_state", 0);
            //未结账
            lockObject.put("bill_state", 0);
            //账单号
            String billNum = holdBillDao.getBillNum(tenancyId, storeId, tableCode);
            lockObject.put("bill_num", billNum);
            holdBillDao.insertIgnorCase(tenancyId, "pos_bill_lock", lockObject);
        }
    }
}
