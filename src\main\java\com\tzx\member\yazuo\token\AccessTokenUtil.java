package com.tzx.member.yazuo.token;

import java.util.HashMap;
import java.util.Map;

import org.apache.log4j.Logger;

import com.tzx.framework.common.constant.Constant;
import com.tzx.framework.common.constant.SysParameterCode;
import com.tzx.framework.common.util.CommonUtil;
import com.tzx.framework.common.util.DateUtil;
import com.tzx.framework.common.util.HttpUtil;
import com.tzx.framework.common.util.SpringConext;
import com.tzx.member.yazuo.common.util.YazuoCrmRequestUtil;
import com.tzx.member.yazuo.common.util.YazuoCrmUtil;
import com.tzx.member.yazuo.po.springjdbc.dao.YazuoCustomerDao;
import com.tzx.pos.base.util.ParamUtil;

import net.sf.json.JSONObject;

public class AccessTokenUtil
{
	private static final Logger	logger	= Logger.getLogger(AccessTokenUtil.class);
	
	/** 接口访问凭证 */
	private static String	accessToken		= null;
	/** 有效期时间（秒） */
	private static Long		expiresIn		= null;
	/** 使用此令牌到期强制刷新token */
	private static String	refreshToken	= null;
	
	/** 对应雅座商家ID */
	private static Integer	brandId			= null;
	/** 对应雅座门店ID */
	private static Integer	merchantId		= null;
	/** 对应雅座门店编号 */
	private static String	merchantNo		= null;
	/** 对应操作终端号 */
	private static String	terminalNo		= null;
	
	private static final String	oauth_token_url				= "/oauth/token";

	private static final String	grant_type_password			= "password";
	private static final String	grant_type_refresh_token	= "refresh_token";
	private static final String	token_scope					= "common_scope";
	
	/** 接口访问凭证
	 * @return
	 */
	public static String getAccessToken()
	{
		if (CommonUtil.isNullOrEmpty(accessToken))
		{
			setAccessToken();
		}
		else if (DateUtil.currentTimestamp().getTime() >= expiresIn.longValue())
		{
			refreshAccessToken();
		}
		
		return accessToken;
	}

	/**对应雅座商家ID
	 * @return
	 */
	public static Integer getBrandId()
	{
		if(CommonUtil.isNullOrEmpty(brandId))
		{
			setAccessToken();
		}
		return brandId;
	}

	/** 对应雅座门店ID
	 * @return
	 */
	public static Integer getMerchantId()
	{
		if(CommonUtil.isNullOrEmpty(merchantId))
		{
			setAccessToken();
		}
		return merchantId;
	}

	/** 对应雅座门店编号
	 * @return
	 */
	public static String getMerchantNo()
	{
		if(CommonUtil.isNullOrEmpty(merchantNo))
		{
			setAccessToken();
		}
		return merchantNo;
	}

	/** 对应操作终端号
	 * @return
	 */
	public static String getTerminalNo()
	{
		if(CommonUtil.isNullOrEmpty(terminalNo))
		{
			setAccessToken();
		}
		return terminalNo;
	}
	
	private synchronized static void setAccessToken()
	{
		if (CommonUtil.isNullOrEmpty(accessToken))
		{
			try
			{
				String tenancyId = Constant.getSystemMap().get("tenent_id");
				int storeId = Integer.parseInt(Constant.getSystemMap().get("store_id"));

				YazuoCustomerDao posDao = (YazuoCustomerDao) SpringConext.getApplicationContext().getBean(YazuoCustomerDao.NAME);
				
				Map<String, String> paramMap = posDao.getSysParameter(tenancyId, storeId, new String[]
				{ SysParameterCode.YAZUO_CRM_CLIENT_ID, SysParameterCode.YAZUO_CRM_CLIENT_SECRET, SysParameterCode.YAZUO_CRM_USERNAME, SysParameterCode.YAZUO_CRM_PASSWORD });
				
				String clientId = YazuoCrmUtil.getStringValue(paramMap, SysParameterCode.YAZUO_CRM_CLIENT_ID);
				String clientSecret = YazuoCrmUtil.getStringValue(paramMap, SysParameterCode.YAZUO_CRM_CLIENT_SECRET);
				String username = YazuoCrmUtil.getStringValue(paramMap, SysParameterCode.YAZUO_CRM_USERNAME);
				String password = YazuoCrmUtil.getStringValue(paramMap, SysParameterCode.YAZUO_CRM_PASSWORD);
				
				Map<String, String> params = new HashMap<String, String>();
				params.put("client_id", clientId);
				params.put("client_secret", clientSecret);
				params.put("grant_type", grant_type_password);
				params.put("scope", token_scope);
				params.put("username", username);
				params.put("password", password);

				logger.info("调用雅座接口请求参数==> " + JSONObject.fromObject(params));
				logger.info("调用雅座接口请求url==> " + YazuoCrmRequestUtil.getRequestUrl(oauth_token_url));
				String result = HttpUtil.sendPostRequest(YazuoCrmRequestUtil.getRequestUrl(oauth_token_url), params);
				logger.info("调用雅座接口返回数据==> " + result);
				
				if(CommonUtil.hv(result))
				{
					JSONObject resultJson = JSONObject.fromObject(result);
					accessToken = ParamUtil.getStringValueByObject(resultJson, "access_token");
					refreshToken = ParamUtil.getStringValueByObject(resultJson, "refresh_token");
					setExpiresIn(ParamUtil.getIntegerValueByObject(resultJson, "expires_in"));
					
					brandId = ParamUtil.getIntegerValueByObject(resultJson, "brand_id");
					merchantId = ParamUtil.getIntegerValueByObject(resultJson, "merchant_id");
					merchantNo = ParamUtil.getStringValueByObject(resultJson, "merchant_no");
					terminalNo = ParamUtil.getStringValueByObject(resultJson, "terminal_no");
				}
			}
			catch (Exception e)
			{
				logger.info("获取token失败",e);
			}
		}
	}

	private synchronized static void refreshAccessToken()
	{
		if (DateUtil.currentTimestamp().getTime() >= expiresIn.longValue())
		{
			try
			{
				String tenancyId = Constant.getSystemMap().get("tenent_id");
				int storeId = Integer.parseInt(Constant.getSystemMap().get("store_id"));

				YazuoCustomerDao posDao = (YazuoCustomerDao) SpringConext.getApplicationContext().getBean(YazuoCustomerDao.NAME);
				
				Map<String, String> paramMap = posDao.getSysParameter(tenancyId, storeId, new String[]
				{ SysParameterCode.YAZUO_CRM_CLIENT_ID, SysParameterCode.YAZUO_CRM_CLIENT_SECRET });
				
				String clientId = YazuoCrmUtil.getStringValue(paramMap, SysParameterCode.YAZUO_CRM_CLIENT_ID);
				String clientSecret = YazuoCrmUtil.getStringValue(paramMap, SysParameterCode.YAZUO_CRM_CLIENT_SECRET);
				
				Map<String, String> params = new HashMap<String, String>();
				params.put("client_id", clientId);
				params.put("client_secret", clientSecret);
				params.put("grant_type", grant_type_refresh_token);
				params.put("refresh_token", refreshToken);

				logger.info("调用雅座接口请求参数==> " + JSONObject.fromObject(params));
				logger.info("调用雅座接口请求url==> " + YazuoCrmRequestUtil.getRequestUrl(oauth_token_url));
				String result = HttpUtil.sendPostRequest(YazuoCrmRequestUtil.getRequestUrl(oauth_token_url), params);
				logger.info("调用雅座接口返回数据==> " + result);
				
				if(CommonUtil.hv(result))
				{
					JSONObject resultJson = JSONObject.fromObject(result);
					accessToken = ParamUtil.getStringValueByObject(resultJson, "access_token");
					refreshToken = ParamUtil.getStringValueByObject(resultJson, "refresh_token");
					setExpiresIn(ParamUtil.getIntegerValueByObject(resultJson, "expires_in"));
				}
			}
			catch (Exception e)
			{
				logger.info("刷新token失败",e);
			}
		}
	}

	private static void setExpiresIn(Integer expiresIn)
	{
		if (null != expiresIn)
		{
			AccessTokenUtil.expiresIn = (DateUtil.currentTimestamp().getTime() + ((expiresIn - 600) * 1000));
		}
	}
}
