<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <module name="saaspos" />
      </profile>
    </annotationProcessing>
  </component>
  <component name="JavacSettings">
    <option name="ADDITIONAL_OPTIONS_OVERRIDE">
      <module name="saaspos" options="-bootclasspath &quot;$PROJECT_DIR$/../../../../../Program Files/Java/jdk1.8.0_301/jre/lib/rt.jar;$PROJECT_DIR$/../../../../../Program Files/Java/jdk1.8.0_301/jre/lib/jce.jar&quot;" />
    </option>
  </component>
</project>