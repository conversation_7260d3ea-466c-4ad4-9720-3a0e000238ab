package com.tzx.clientorder.wechatprogram.service.rest;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import com.tzx.clientorder.common.util.ExceptionPrintUtil;
import com.tzx.clientorder.wechatprogram.bo.PrePaymentService;
import com.tzx.clientorder.wechatprogram.common.constant.PromptMsgConstant;
import com.tzx.framework.common.constant.Constant;
import com.tzx.framework.common.exception.OtherSystemException;
import com.tzx.framework.common.exception.PosErrorCode;
import com.tzx.framework.common.exception.SystemErrorCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.exception.WxErrorCode;
import com.tzx.pos.base.util.ParamUtil;

import net.sf.json.JSONObject;

@Controller("PrePaymentRest")
@RequestMapping("/order/api")
public class PrePaymentRest
{
	private static final Logger	logger	= Logger.getLogger(PrePaymentRest.class);

	@Resource(name = PrePaymentService.NAME)
	private PrePaymentService	paymentService;

	/**
	 * 查询桌台是否有订单
	 * 
	 * @param request
	 * @param jsobj
	 */
	@RequestMapping(value = "/preorder", method = RequestMethod.POST)
	@ResponseBody
	public JSONObject preorder(HttpServletRequest request, @RequestBody JSONObject jsobj)
	{
		String requestId = ParamUtil.getStringValueByObject(jsobj, "request_id");
		logger.info(requestId + "<=先付下单收到请求参数===>" + jsobj.toString());

		JSONObject responseJson = null;
		try
		{
			String tenancyId = null;
			if (Constant.systemMap.containsKey("tenent_id"))
			{
				tenancyId = Constant.systemMap.get("tenent_id");
			}
			Integer storeId = null;
			if (Constant.systemMap.containsKey("store_id"))
			{
				storeId = Integer.valueOf(Constant.systemMap.get("store_id"));
			}

			if (StringUtils.isEmpty(tenancyId) || storeId == 0)
			{
				throw SystemException.getInstance(SystemErrorCode.TENENTID_OR_STOREID_NOT_NULL_ERROR);
			}
			String orderChannel = ParamUtil.getStringValueByObject(jsobj, "request_source", true, WxErrorCode.NOT_NULL_REQUEST_SOURCE_ERROR);

			responseJson = paymentService.preOrderToBill(tenancyId, storeId, jsobj, orderChannel);
		}
		catch (OtherSystemException se)
		{
			responseJson = new JSONObject();
			ExceptionPrintUtil.buildProgramOtherSysExceptionData(logger, se, responseJson, PromptMsgConstant.QUERY_ORDER_FAILURE);
		}
		catch (SystemException se)
		{
			responseJson = new JSONObject();
			ExceptionPrintUtil.buildProgramSysExceptionData(logger, se, responseJson, PromptMsgConstant.QUERY_ORDER_FAILURE);
		}
		catch (Exception e)
		{
			responseJson = new JSONObject();
			ExceptionPrintUtil.buildProgramExceptionData(logger, e, responseJson, PromptMsgConstant.QUERY_ORDER_FAILURE);
		}

		responseJson.put("request_id", requestId);
		logger.info(requestId + "<=先付下单返回参数===>" + responseJson.toString());
		return responseJson;
	}
}
