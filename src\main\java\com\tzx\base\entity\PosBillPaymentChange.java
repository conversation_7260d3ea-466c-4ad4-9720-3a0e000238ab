package com.tzx.base.entity;

import java.sql.Timestamp;
import java.util.Date;

/**
 * 付款方式修改记录
 * <AUTHOR>
 *
 */
public class PosBillPaymentChange
{
	private String		tenancy_id;
	private Integer		store_id;
	private Integer		id;
	private Date		report_date;
	private String		bill_num;
	private String		ago_bill_num;
	private String		table_code;
	private Integer		ago_payment_id;
	private Double		ago_payment_amount;
	private Integer		payment_id;
	private Double		payment_amount;
	private Integer		change_count;
	private String		opt_num;
	private String		manager_num;
	private String		pos_num;
	private Integer		reason_id;
	private Timestamp	update_time;
	private Integer		upload_tag	= 0;
	private String		remark;
	private Integer     upload_tag_kingdee;
	public PosBillPaymentChange()
	{
		super();
		// TODO 自动生成的构造函数存根
	}

	public PosBillPaymentChange(String tenancy_id, Integer store_id, Date report_date, String bill_num, Integer ago_payment_id, Double ago_payment_amount, Integer payment_id, Double payment_amount, Integer change_count, String opt_num, String pos_num, Timestamp update_time)
	{
		super();
		this.tenancy_id = tenancy_id;
		this.store_id = store_id;
		this.report_date = report_date;
		this.bill_num = bill_num;
		this.ago_payment_id = ago_payment_id;
		this.ago_payment_amount = ago_payment_amount;
		this.payment_id = payment_id;
		this.payment_amount = payment_amount;
		this.change_count = change_count;
		this.opt_num = opt_num;
		this.pos_num = pos_num;
		this.update_time = update_time;
	}

	public String getTenancy_id()
	{
		return tenancy_id;
	}

	public void setTenancy_id(String tenancy_id)
	{
		this.tenancy_id = tenancy_id;
	}

	public Integer getStore_id()
	{
		return store_id;
	}

	public void setStore_id(Integer store_id)
	{
		this.store_id = store_id;
	}

	public Integer getId()
	{
		return id;
	}

	public void setId(Integer id)
	{
		this.id = id;
	}

	public Date getReport_date()
	{
		return report_date;
	}

	public void setReport_date(Date report_date)
	{
		this.report_date = report_date;
	}

	public String getBill_num()
	{
		return bill_num;
	}

	public void setBill_num(String bill_num)
	{
		this.bill_num = bill_num;
	}

	public String getAgo_bill_num()
	{
		return ago_bill_num;
	}

	public void setAgo_bill_num(String ago_bill_num)
	{
		this.ago_bill_num = ago_bill_num;
	}

	public String getTable_code()
	{
		return table_code;
	}

	public void setTable_code(String table_code)
	{
		this.table_code = table_code;
	}

	public Integer getAgo_payment_id()
	{
		return ago_payment_id;
	}

	public void setAgo_payment_id(Integer ago_payment_id)
	{
		this.ago_payment_id = ago_payment_id;
	}

	public Double getAgo_payment_amount()
	{
		return ago_payment_amount;
	}

	public void setAgo_payment_amount(Double ago_payment_amount)
	{
		this.ago_payment_amount = ago_payment_amount;
	}

	public Integer getPayment_id()
	{
		return payment_id;
	}

	public void setPayment_id(Integer payment_id)
	{
		this.payment_id = payment_id;
	}

	public Double getPayment_amount()
	{
		return payment_amount;
	}

	public void setPayment_amount(Double payment_amount)
	{
		this.payment_amount = payment_amount;
	}

	public Integer getChange_count()
	{
		return change_count;
	}

	public void setChange_count(Integer change_count)
	{
		this.change_count = change_count;
	}

	public String getOpt_num()
	{
		return opt_num;
	}

	public void setOpt_num(String opt_num)
	{
		this.opt_num = opt_num;
	}

	public String getManager_num()
	{
		return manager_num;
	}

	public void setManager_num(String manager_num)
	{
		this.manager_num = manager_num;
	}

	public String getPos_num()
	{
		return pos_num;
	}

	public void setPos_num(String pos_num)
	{
		this.pos_num = pos_num;
	}

	public Integer getReason_id()
	{
		return reason_id;
	}

	public void setReason_id(Integer reason_id)
	{
		this.reason_id = reason_id;
	}

	public Timestamp getUpdate_time()
	{
		return update_time;
	}

	public void setUpdate_time(Timestamp update_time)
	{
		this.update_time = update_time;
	}

	public Integer getUpload_tag()
	{
		return upload_tag;
	}

	public void setUpload_tag(Integer upload_tag)
	{
		this.upload_tag = upload_tag;
	}

	public String getRemark()
	{
		return remark;
	}

	public void setRemark(String remark)
	{
		this.remark = remark;
	}

	public Integer getUpload_tag_kingdee() {
		return upload_tag_kingdee;
	}

	public void setUpload_tag_kingdee(Integer upload_tag_kingdee) {
		this.upload_tag_kingdee = upload_tag_kingdee;
	}
}
