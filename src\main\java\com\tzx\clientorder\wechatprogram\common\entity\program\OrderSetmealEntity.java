package com.tzx.clientorder.wechatprogram.common.entity.program;

import java.util.List;

public class OrderSetmealEntity extends OrderDishEntity 
{
	private List<OrderSetmealItemEntity>	maindish;
	private List<OrderSetmealItemEntity>	mandatory;
	private Double						aprice;

	public List<OrderSetmealItemEntity> getMaindish()
	{
		return maindish;
	}
	public void setMaindish(List<OrderSetmealItemEntity> maindish)
	{
		this.maindish = maindish;
	}
	public List<OrderSetmealItemEntity> getMandatory()
	{
		return mandatory;
	}
	public void setMandatory(List<OrderSetmealItemEntity> mandatory)
	{
		this.mandatory = mandatory;
	}
	public Double getAprice()
	{
		return aprice;
	}
	public void setAprice(Double aprice)
	{
		this.aprice = aprice;
	}
}
