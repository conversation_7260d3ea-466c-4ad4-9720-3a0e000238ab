package com.tzx.clientorder.acewillwechat.po.springjdbc.dao;

import com.tzx.framework.common.util.dao.GenericDao;
import net.sf.json.JSONObject;

import java.util.List;

/**
 * Created by qin-gui on 2018/3/10.
 */
public interface OrderDao extends GenericDao{
    String NAME = "com.tzx.clientorder.acewillwechat.po.springjdbc.dao.imp.OrderDaoImp";

    /**
     * 查询微生活会员信息
     * @param tenancyId
     * @param tableCode
     * @param storeId
     * @return
     */
    JSONObject getWLifeMember(String tenancyId, String tableCode, int storeId) throws Exception;

    /**
     * 查询订单的信息
     * @param tenancyId
     * @param tableCode
     * @param storeId
     * @return
     * @throws Exception
     */
    JSONObject getBillInfo(String tenancyId, String tableCode, int storeId) throws Exception;

    /**
     * 查询套餐菜品信息
     * @param tenancyId
     * @param billNum
     * @return
     * @throws Exception
     */
    List<JSONObject> getSetmeal(String tenancyId, String billNum) throws Exception;

    /**
     * 查询套餐的主菜或套餐必选菜的菜品
     * @param tenancyId
     * @param billNum
     * @param type 套餐主菜或是套餐必选菜查询
     * @return
     * @throws Exception
     */
    List<JSONObject> getSetmealDish(String tenancyId, String billNum, String type, int stemealCount, String setmealId) throws Exception;

    /**
     * 查询非套餐菜品
     * @param tenancyId
     * @param billNum
     * @return
     * @throws Exception
     */
    List<JSONObject> getNormalitems(String tenancyId, String billNum) throws Exception;

    /**
     * 根据pos_bill_item表的菜品id，查询做法
     * @param tenancyId
     * @param id
     * @return
     * @throws Exception
     */
    List<JSONObject> getCooks(String tenancyId, int id) throws Exception;

    /**
     * 查询微生活的门店、品牌等参数
     * @param tenancyId
     * @return
     * @throws Exception
     */
    List<JSONObject> getParam(String tenancyId) throws Exception;

    /**
     * 查询订单的信息
     * @param tenancyId
     * @param tableCode
     * @param storeId
     * @return
     * @throws Exception
     */
    JSONObject getBillOrder(String tenancyId, String tableCode, int storeId) throws Exception;

    /**
     * 查询服务费
     * @param tenancyId
     * @param billNum
     * @param storeId
     * @return
     * @throws Exception
     */
    List<JSONObject> serviceCharge(String tenancyId, String billNum,String tableCode, int storeId) throws Exception;
}
