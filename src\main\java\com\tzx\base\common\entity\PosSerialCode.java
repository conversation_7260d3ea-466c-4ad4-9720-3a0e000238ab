package com.tzx.base.common.entity;

import org.apache.commons.lang.StringUtils;

import java.util.concurrent.atomic.AtomicInteger;

public class PosSerialCode {

    private StringBuilder serialCode=new StringBuilder();

    private String prefix;
    private AtomicInteger serial;
    private int serialLength;
    private String suffix;

    public PosSerialCode(String prefix,int serialInitVale,int serialLength,String suffix){
       this.prefix=prefix;
       this.serial=new AtomicInteger(serialInitVale);
       this.serialLength=serialLength;
       this.suffix=suffix;
    }

    public String getNext(){
        serialCode.append(prefix);
        serialCode.append(StringUtils.leftPad(String.valueOf(serial.incrementAndGet()), serialLength, "0"));
        serialCode.append(suffix);
        return serialCode.toString();
    }
}
