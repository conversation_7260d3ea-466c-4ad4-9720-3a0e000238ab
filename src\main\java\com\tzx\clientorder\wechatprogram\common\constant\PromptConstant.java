package com.tzx.clientorder.wechatprogram.common.constant;

import java.util.Arrays;
import java.util.List;

import com.tzx.pos.base.constant.SysDictionary;

import static com.tzx.pos.base.constant.SysDictionary.*;

/**
 * <AUTHOR>
 *
 */
public class PromptConstant
{
	public static final String			SUCCESS						= "success";
	public static final String			MSG							= "msg";
	public static final String			DATA						= "data";

	/** 订单来源：支付宝 */
	public static final String			PAY_SOURCE_ALIPAY			= "alipay";
	/** 订单来源：微信 */
	public static final String			PAY_SOURCE_WEIXIN			= "weixin";
	/** 订单来源：企迈会员储值 */
	public static final String			PAY_SOURCE_BALANCE_QM		= "balance_qm";
	/** 订单来源：企迈会员积分 */
	public static final String			PAY_SOURCE_CREDIT_QM		= "credit_qm";
	/** 订单来源：企迈优惠券 */
	public static final String			PAY_SOURCE_COUPON_QM		= "coupon_qm";

    /*
     * 企迈自营外卖相关付款方式
     * qm_self_takeout_wechat 企迈自营外卖微信
     * qm_self_takeout_ali 企迈自营外卖支付宝
     * qm_self_takeout_credit 企迈自营外卖会员积分
     * qm_self_takeout_balance 企迈自营外卖会员储值
     * qm_self_wm_balance_phantom 企迈自营外卖会员储值虚收
     * qm_self_takeout_coupons 企迈自营外卖优惠券
     * qm_self_wm_coupons_phantom 企迈自营外卖优惠券虚收
     */
    /**
     * 企迈自营外卖微信
     */
    public static final String			QM_SELF_TAKEOUT_WECHAT		= "qm_self_takeout_wechat";
    /**
     * 企迈自营外卖支付宝
     */
    public static final String			QM_SELF_TAKEOUT_ALI		= "qm_self_takeout_ali";
    /**
     * 企迈自营外卖会员积分
     */
    public static final String			QM_SELF_TAKEOUT_CREDIT		= "qm_self_takeout_credit";
    /**
     * 企迈自营外卖会员储值
     */
    public static final String			QM_SELF_TAKEOUT_BALANCE		= "qm_self_takeout_balance";
    /**
     * 企迈自营外卖会员储值虚收
     */
    public static final String			QM_SELF_WM_BALANCE_PHANTOM		= "qm_self_wm_balance_phantom";
    /**
     * 企迈自营外卖优惠券
     */
    public static final String			QM_SELF_TAKEOUT_COUPONS		= "qm_self_takeout_coupons";
    /**
     * 企迈自营外卖优惠券虚收
     */
    public static final String			QM_SELF_WM_COUPONS_PHANTOM		= "qm_self_wm_coupons_phantom";




	/** 订单来源：雅座会员储值 */
	public static final String			PAY_SOURCE_BALANCE_YZ		= "balance_yz";
	/** 订单来源：雅座会员积分 */
	public static final String			PAY_SOURCE_CREDIT_YZ		= "credit_yz";
	/** 订单来源：雅座优惠券 */
	public static final String			PAY_SOURCE_COUPON_YZ		= "coupon_yz";
	/** 订单来源：雅座优惠券虚收 */
	public static final String			PAY_SOURCE_COUPON_PHANTOM_YZ		= "coupon_phantom_yz";

    public static final String			PAY_SOURCE_ALIPAY_ZS		= PAYMENT_CLASS_ZHONGSHANG_ALI_PAY;
    public static final String			PAY_SOURCE_WEIXIN_ZS		= PAYMENT_CLASS_ZHONGSHANG_WECHAT_PAY;
    public static final String			PAY_SOURCE_ACTIVITY_ZS		= PAYMENT_CLASS_ZHONGSHANG_ACTIVITY;
    /** 订单来源：众赏会员储值 */
    public static final String			PAY_SOURCE_BALANCE_ZS	= PAYMENT_CLASS_ZHONGSHANG_BALANCE;
    /** 订单来源：众赏会员积分 */
    public static final String			PAY_SOURCE_CREDIT_ZS		= PAYMENT_CLASS_ZHONGSHANG_CREDIT;
    /** 订单来源：众赏优惠券 */
    public static final String			PAY_SOURCE_COUPON_ZS		= PAYMENT_CLASS_ZHONGSHANG_COUPONS;





	public static final List<String>	PAY_SOURCE_BALANCE_LIST		= Arrays.asList(PAY_SOURCE_BALANCE_QM, PAY_SOURCE_BALANCE_YZ,QM_SELF_TAKEOUT_BALANCE);

	public static final List<String>	PAY_SOURCE_CREDIT_LIST		= Arrays.asList(PAY_SOURCE_CREDIT_QM, PAY_SOURCE_CREDIT_YZ,QM_SELF_TAKEOUT_CREDIT);

	public static final List<String>	PAY_SOURCE_COUPON_LIST		= Arrays.asList(PAY_SOURCE_COUPON_QM, PAY_SOURCE_COUPON_YZ,QM_SELF_TAKEOUT_COUPONS);

	public static final List<String>	PAY_SOURCE_MEMBER_LIST		= Arrays.asList(PAY_SOURCE_BALANCE_QM, PAY_SOURCE_CREDIT_QM, PAY_SOURCE_COUPON_QM,
            PAY_SOURCE_BALANCE_YZ, PAY_SOURCE_CREDIT_YZ, PAY_SOURCE_COUPON_YZ,
            QM_SELF_TAKEOUT_BALANCE,QM_SELF_TAKEOUT_CREDIT,QM_SELF_TAKEOUT_COUPONS
            );

	/** 同步菜品类型: 菜品 */
	public static final String			SYNC_DISH_DATA_TYPE_DISH	= "dish";
	/** 同步菜品类型: 菜类 */
	public static final String			SYNC_DISH_DATA_TYPE_KIND	= "dishkind";
	/** 同步菜品类型: 菜品菜类 */
	public static final String			SYNC_DISH_DATA_TYPE_ALL		= "all";

	/** 同步菜品方式: 重置数据（在.net后台所做修改将被覆盖） */
	public static final String			SYNC_DISH_DATA_FORCE_RESET	= "1";
	/** 同步菜品方式: 同步 */
	public static final String			SYNC_DISH_DATA_FORCE_SYNC	= "0";

	public static final List<String>	USABLE_CHANNEL				= Arrays.asList(SysDictionary.CHANEL_QIMAI,SysDictionary.CHANEL_ZS);
	
	/** 优惠劵类型:代金券 */
	public static final String			CUSTOMER_COUPON_TYPE_CASH	= "1";
	/** 优惠劵类型:礼品券/菜品劵 */
	public static final String			CUSTOMER_COUPON_TYPE_DISH	= "2";
	
	/** 优惠类型: 1,会员折扣 */
	public static final int				DISCOUNT_TYPE_RATE			= 1;
	/** 优惠类型: 2,会员价 */
	public static final int				DISCOUNT_TYPE_PRICE			= 2;
	/** 优惠类型: 3,优惠券 */
	public static final int				DISCOUNT_TYPE_COUPON		= 3;
	/** 优惠类型: 4,营销活动 */
	public static final int				DISCOUNT_TYPE_ACTITY		= 4;
	/** 优惠类型: 101,其他 */
	public static final int				DISCOUNT_TYPE_OTHER			= 101;
	
	/** 优惠类型: 1,参与活动菜品 */
	public static final int				ACTIVE_TYPE_1				= 1;
	/** 优惠类型: 2,满赠菜品 */
	public static final int				ACTIVE_TYPE_2				= 2;
	/** 优惠类型: 3,加购菜品 */
	public static final int				ACTIVE_TYPE_3				= 3;


	/** 上传订单操作类型: 1,加菜 */
	public static final int				UPLOADORDER_OPER_ADD			= 1;
	/** 上传订单操作类型: 2,退菜 */
	public static final int				UPLOADORDER_OPER_REFUND			= 2;
	/** 上传订单操作类型: 3,奉送 */
	public static final int				UPLOADORDER_OPER_FS			= 3;
	/** 上传订单操作类型: 4,转台 */
	public static final int				UPLOADORDER_OPER_ZT			= 4;
	/** 上传订单操作类型: 5,单品转台 */
	public static final int				UPLOADORDER_OPER_DPZT			= 5;
	/** 上传订单操作类型: 6,修改优惠 */
	public static final int				UPLOADORDER_OPER_MDIS			= 6;
	/** 上传订单操作类型: 7,修改服务费 */
	public static final int				UPLOADORDER_OPER_MS			= 7;
	/** 上传订单操作类型: 8,并台 */
	public static final int				UPLOADORDER_OPER_BT			= 8;
	/** 上传订单操作类型: 9,锁单 */
	public static final int				UPLOADORDER_OPER_LOCK			= 9;
	/** 上传订单操作类型: 10,解锁 */
	public static final int				UPLOADORDER_OPER_UNLOCK			= 10;
	/** 上传订单操作类型: 11,恢复账单 */
	public static final int				UPLOADORDER_OPER_REGAIN_BILL		= 11;

}

