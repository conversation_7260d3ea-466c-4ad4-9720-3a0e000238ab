package com.tzx.framework.common.util.io;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;

/**
 * 瀛楄妭杞崲鐨勫伐鍏�
 * <AUTHOR>
 * @desc:
 * @create date:2014-1-8涓嬪崍02:37:29
 */
public class ByteUtil {
	/**
	 * 瀛楄妭杞崲涓烘暟瀛�
	 * <AUTHOR>
	 * @Date:2014-1-8涓嬪崍02:37:58
	 * @param b
	 * @return
	 */
	 public static int byte2int(byte[] b) {
	        int value = 0;
	        for (int i = 0; i < 4; i++) {
	            int shift = (4 - 1 - i) * 8;
	            value += (b[i] & 0x000000FF) << shift;
	        }
	        return value;
	}
	
	/**
	 * 鏁板瓧杞崲涓哄瓧鑺�
	 * <AUTHOR>
	 * @Date:2014-1-8涓嬪崍02:40:01
	 * @param i
	 * @return
	 */
	public static byte[] int2byte(int i) {
	        return new byte[]{
	                (byte) ((i >> 24) & 0xFF),
	                (byte) ((i >> 16) & 0xFF),
	                (byte) ((i >> 8) & 0xFF),
	                (byte) (i & 0xFF)
	        };
	}
	
	/***
	 * 浠庢祦涓鍙栦竴涓暟瀛�
	 * <AUTHOR>
	 * @Date:2014-1-8涓嬪崍02:41:32
	 * @param is
	 * @return
	 * @throws IOException
	 */
	public static int readInteger(InputStream is) throws IOException {
        byte[] bytes = new byte[4];
        is.read(bytes);
        return byte2int(bytes);
    }
	
	public static final int BUFFER_SIZE = 1024;
    /**
     * 灏嗘祦杞垚瀛楄妭
     * <AUTHOR>
     * @Date:2013-12-16涓嬪崍05:10:33
     * @param is
     * @return
     * @throws IOException
     */
    public static byte[] getBytes(InputStream is) throws IOException {

       ByteArrayOutputStream baos = new ByteArrayOutputStream();
       byte[] b = new byte[BUFFER_SIZE];
       int len = 0;

       while ((len = is.read(b, 0, BUFFER_SIZE)) != -1) {
        baos.write(b, 0, len);
       }

       baos.flush();
       byte[] bytes = baos.toByteArray();
       return bytes;
    }
    
    /**
     * 灏哹yte[]杞负inputStream
     * @param b
     * @return
     */
    public static InputStream getIsFromBytes(byte [] b){
    	ByteArrayInputStream is=new ByteArrayInputStream(b);
    	return is;
    }
    
    /**
     * 将inputstream 流写入到byte中
     * @param in
     * @param bs
     * @throws IOException 
     */
    public static void writeInputStream2Bytes(InputStream in ,byte [] bs) throws IOException{
    	if(in != null){
    		int len = 0;
    		while ((len = in.read(bs, 0, BUFFER_SIZE)) != -1) {
    			
	        }
    		
    		in.close();
    	}
    }
    
    public static void main(String[] args) {
		try {
			byte[] b=ByteUtil.getBytes(new FileInputStream(new File("D:\\瀹夎杞欢\\宸ュ叿杞欢\\myeclipse8.5\\myeclipse-8.5.0-win32.exe")));
		} catch (FileNotFoundException e) {
			// TODO Auto-generated catch block
			//e.printStackTrace();
		} catch (IOException e) {
			// TODO Auto-generated catch block
			//e.printStackTrace();
		}
		//不要使用 System.out.println()，如有必要，用logger.debug()替代("鎴愬姛");
		
	}
}
