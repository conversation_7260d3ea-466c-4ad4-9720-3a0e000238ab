package com.tzx.clientorder.wechatprogram.dao.impl;

import java.util.ArrayList;
import java.util.List;

import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import com.tzx.base.entity.PaymentWay;
import com.tzx.base.entity.PosBillMember;
import com.tzx.base.entity.PosBillPayment;
import com.tzx.base.entity.PosBillPaymentCoupons;
import com.tzx.clientorder.wechatprogram.dao.AfterPaymentDao;
import com.tzx.member.common.entity.CrmCardTradingListEntity;
import com.tzx.pos.base.constant.SysDictionary;

import net.sf.json.JSONObject;

/**
 * 微生活后付持久层
 */
@Repository(AfterPaymentDao.NAME)
public class AfterPaymentDaoImpl extends PromptGeneralDaoImp implements AfterPaymentDao
{

	@Override
	public JSONObject getTableStatus(String tenantId, Integer storeId, String tableNo) throws Exception
	{
		if (!StringUtils.isEmpty(tableNo) && storeId != null)
		{
			StringBuilder stringBuilder = new StringBuilder("select id,state,lock_opt_num,lock_pos_num,opt_name from pos_tablestate where table_code = ? and store_id = ? order by last_updatetime desc limit 1");
			List<JSONObject> list = this.query4Json(tenantId, stringBuilder.toString(), new Object[]
			{ tableNo, storeId });

			if (list.size() > 0)
			{
				JSONObject jsonObject = list.get(0);
				return jsonObject;
			}
		}
		return null;
	}

	@Override
	public void lockTableStatus(Integer storeId, String tableNo, String lockPosNum, String lockOptNum, String optName) throws Exception
	{
		StringBuilder sql = new StringBuilder("UPDATE pos_tablestate SET lock_pos_num=?,lock_opt_num= ?,opt_name= ? WHERE table_code= ? and store_id= ?");
		this.jdbcTemplate.update(sql.toString(), new Object[]
		{ lockPosNum, lockOptNum, optName, tableNo, storeId });
	}

	@Override
	public JSONObject getTableStateForBill(String tenancyId, int storeId, String tableCode) throws Exception
	{
		StringBuilder sql = new StringBuilder("select s.state,bl.bill_num,bl.order_num from pos_tablestate s left join pos_bill bl on s.tenancy_id =bl.tenancy_id and s.store_id=bl.store_id and s.table_code=bl.fictitious_table and bl.bill_property=? ");
		sql.append("where s.tenancy_id = ? and s.store_id = ? and s.table_code = ? ");
		List<JSONObject> list = this.query4Json(tenancyId, sql.toString(), new Object[]
		{ SysDictionary.BILL_PROPERTY_OPEN, tenancyId, storeId, tableCode });
		
		if (null != list && 0 < list.size())
		{
			return list.get(0);
		}
		return null;
	}

	@Override
	public JSONObject getBillInfoByOrderNum(String tenancyId, int storeId, String orderNum) throws Exception
	{
		if (!StringUtils.isEmpty(orderNum))
		{
			String sql = "select b.* from pos_bill b where b.order_num = ? and b.tenancy_id = ? and b.store_id = ? ";
			List<JSONObject> query4Json = this.query4Json(tenancyId, sql, new Object[]
			{ orderNum, tenancyId, storeId });
			
			if (query4Json.size() > 0)
			{
				return query4Json.get(0);
			}
		}
		return null;
	}

	@Override
	public JSONObject getBillInfoByBillNum(String tenantId, int storeId, String billNum) throws Exception
	{
		if (!StringUtils.isEmpty(billNum))
		{
			StringBuilder sql = new StringBuilder("select b.* from pos_bill b where b.bill_num = ? and b.tenancy_id = ? and b.store_id = ? ");
			List<JSONObject> query4Json = this.query4Json(tenantId, sql.toString(), new Object[]
			{ billNum, tenantId, storeId });
			
			if (query4Json.size() > 0)
			{
				return query4Json.get(0);
			}
		}
		return null;
	}

	@Override
	public JSONObject getBillInfoByTableCode(String tenantId, Integer storeId, String tableNo) throws Exception
	{
		if (!StringUtils.isEmpty(tableNo) && storeId != null)
		{
			StringBuilder stringBuilder = new StringBuilder("select * from pos_bill where fictitious_table = ? and store_id = ? and bill_property<> ? order by id desc limit 1");
			List<JSONObject> list = this.query4Json(tenantId, stringBuilder.toString(), new Object[]
			{ tableNo, storeId, SysDictionary.BILL_PROPERTY_CLOSED });
			if (list.size() > 0)
			{
				JSONObject jsonObject = list.get(0);
				return jsonObject;
			}
		}
		return null;
	}
	
	@Override
	public JSONObject getBillInfo(String tenancyId, int storeId, String tableCode) throws Exception
	{
		StringBuffer sql = new StringBuffer();
		sql.append(" select pb.bill_num,pb.bill_taste,pb.bill_amount,pb.payment_amount,pb.guest,");
		sql.append(" pb.service_amount,pb.fictitious_table,pb.table_code, pb.order_num,pb.discount_mode_id,hdc.discount_case_name,pb.discount_rate,pb.discount_amount,pb.discountk_amount,");
		sql.append(" (case when t.lock_pos_num is null or t.lock_pos_num = '' then '1' else '3' end) as is_locked");
		sql.append(" from pos_bill pb left join pos_tablestate t on pb.store_id = t.store_id and pb.fictitious_table = t.table_code");
		sql.append(" left join hq_discount_case hdc on pb.discount_case_id = hdc.id ");
		sql.append(" where pb.store_id = ? and pb.fictitious_table = ? and pb.bill_property = ?");
		List<JSONObject> list = this.query4Json(tenancyId, sql.toString(), new Object[]
		{ storeId, tableCode, SysDictionary.BILL_PROPERTY_OPEN });
		
		if (null != list && list.size() > 0) return list.get(0);
		return null;
	}

	@Override
	public void updatePosBillSource(String tenancyId,String billNum, String channel) throws Exception
	{
		String sql = " UPDATE pos_bill SET payment_source='" + channel + "' WHERE bill_num='" + billNum + "'";
		this.execute(tenancyId, sql);
	}

	@Override
	public void updateOrderSource(String tenancyId, String billNum, String orderNum,String channel) throws Exception
	{
		StringBuffer sql = new StringBuffer();
		sql.append(" update pos_bill set order_num = '" + orderNum + "',order_source = '" + channel + "' where bill_num = '" + billNum + "'");
		this.execute(tenancyId, sql.toString());
	}
	
	@Override
	public JSONObject getBillMemberInfo(String tenancyId, int storeId, String billNum) throws Exception
	{
		StringBuffer sql = new StringBuffer();
		sql.append(" select pbm.customer_code as openid,pbm.customer_name as name,pbm.mobil as mobile,pbm.card_code as cno,pbm.remark as grade,pbm.consume_before_credit as credit,pbm.consume_before_main_balance as balance from pos_bill_member pbm");
		sql.append(" where pbm.tenancy_id=? and pbm.store_id=? and pbm.bill_num=? order by pbm.id desc LIMIT 1 ");
		List<JSONObject> list = this.query4Json(tenancyId, sql.toString(), new Object[]
		{ tenancyId, storeId, billNum });
		if (null != list && list.size() > 0) return list.get(0);
		return null;
	}

	@Override
	public List<JSONObject> getServiceCharge(String tenancyId, int storeId, String billNum) throws Exception
	{
		StringBuffer sql = new StringBuffer();
		sql.append("select hsft.name as title, pbs.service_total as money from pos_bill_service pbs left join hq_service_fee_type hsft on pbs.service_id = hsft.id ");
		sql.append("where pbs.tenancy_id =? and pbs.store_id = ? and pbs.bill_num = ? and pbs.service_id <> 0 ");
		return this.query4Json(tenancyId, sql.toString(), new Object[]
		{ tenancyId, storeId, billNum });
	}
	
	@Override
	public List<JSONObject> getItemList(String tenancyId, int storeId, String billNum) throws Exception
	{
		StringBuffer sql = new StringBuffer();
		sql.append(" select pbi.rwid,pbi.item_serial,pbi.setmeal_rwid,pbi.setmeal_id,pbi.item_id,pbi.item_num,pbi.item_name,pbi.item_unit_id,pbi.item_unit_name,item_count,pbi.item_price,pbi.method_money,pbi.assist_money,pbi.real_amount,pbi.discount_mode_id,pbi.third_price,pbi.item_taste,pbi.item_property,pbi.item_remark,");
		sql.append(" hii.item_class,hii.photo1,cd.is_itemgroup,cd.details_id as item_group_id,(case when pbi.item_remark=? then 1 else 0 end) as is_gift, pbi.openid,pbi.single_discount_rate ");
		sql.append(" from pos_bill_item pbi left join hq_item_info hii on pbi.item_id = hii.id");
		sql.append(" left join hq_item_combo_details cd on pbi.assist_item_id=cd.id and pbi.setmeal_id=cd.iitem_id");
		sql.append(" where pbi.bill_num = ? and (pbi.item_remark is null or (pbi.item_remark  <> ?)) ");
		List<JSONObject> list = this.query4Json(tenancyId, sql.toString(), new Object[]
		{ SysDictionary.ITEM_REMARK_FS02, billNum, SysDictionary.ITEM_REMARK_CJ05 });//pbi.item_remark  <> ? and  SysDictionary.ITEM_REMARK_TC01,
		return list;
	}

	@Override
	public List<JSONObject> getBillDiscountList(String tenancyId, Integer storeId,  String billNum) throws Exception
	{
		StringBuilder sql = new StringBuilder();
		sql.append("select (case when bd.discount_type <> ? then 4 else 5 end) as type,bd.discount_label as title,bd.discount_amount as money ");
		sql.append(",bd.discount_type ");
		sql.append("from pos_bill_discount bd ");
		sql.append("left join pos_bill bl on bd.tenancy_id=bl.tenancy_id and bd.store_id=bl.store_id and bd.bill_num=bl.bill_num ");
		sql.append("left join tables_info ti on bl.tenancy_id=ti.tenancy_id and bl.table_code=ti.table_code ");
		sql.append("left join organ og on bd.tenancy_id=og.tenancy_id and bd.store_id=og.id ");
		sql.append("left join hq_unusual_reason zkr on bl.tenancy_id=zkr.tenancy_id and bl.discount_reason_id=zkr.id and zkr.unusual_type='ZK04' ");
		sql.append("where bl.tenancy_id=? and bl.store_id =?  and bl.bill_num = ? ");

		return this.query4Json(tenancyId, sql.toString(), new Object[]
				{"FS04", tenancyId, storeId, billNum });
	}
	@Override
	public List<JSONObject> getMethodList(String tenancyId, int storeId,String billNum) throws Exception
	{
		StringBuffer sql = new StringBuffer("select zf.rwid,zf.item_id,zf.zfkw_id,zf.zfkw_name,zf.amount from pos_zfkw_item zf where zf.tenancy_id=? and zf.store_id=? and zf.bill_num=?");
		return this.query4Json(tenancyId, sql.toString(), new Object[]
		{ tenancyId, storeId, billNum });
	}
	
	@Override
	public Integer getLastItemSerial(String TenentId, String billNUm) throws Exception
	{
		if (!StringUtils.isEmpty(billNUm))
		{
			StringBuilder sql = new StringBuilder();
			sql.append("select coalesce(max(item_serial),'0') item_serial from pos_bill_item where bill_num = '");
			sql.append(billNUm);
			sql.append("'");
			List<JSONObject> list = this.query4Json(TenentId, sql.toString());
			if (list.size() > 0)
			{
				JSONObject jsonObject = list.get(0);
				return jsonObject.getInt("item_serial");
			}
		}

		return 0;
	}

	@Override
	public List<JSONObject> getUnitNameList(String tenancyId, int storeId, List<String> duids) throws Exception
	{
		String duidStr = StringUtils.collectionToDelimitedString(duids, ",");
		if (!StringUtils.isEmpty(duidStr))
		{
			StringBuilder sql = new StringBuilder();
			sql.append("select id,unit_name from hq_item_unit where id in (");
			sql.append(duidStr);
			sql.append(")");
			return this.query4Json(tenancyId, sql.toString());
		}
		return null;
	}

	@Override
	public List<JSONObject> findItemUnit(String tenentId, List<String> itemIdList) throws Exception
	{
		String itemIds = StringUtils.collectionToDelimitedString(itemIdList, ",");
		if (!StringUtils.isEmpty(itemIds))
		{
			StringBuilder sql = new StringBuilder();
			sql.append("select * from hq_item_unit where is_default='Y' and item_id in (");
			sql.append(itemIds);
			sql.append(")");
			return this.query4Json(tenentId, sql.toString());
		}
		return null;
	}

	@Override
	public List<JSONObject> getItemComboDetails(String tenantId, List<Integer> itemIdList) throws Exception
	{
		String itemIds = StringUtils.collectionToDelimitedString(itemIdList, ",");
		if (!StringUtils.isEmpty(itemIds))
		{
			StringBuilder sql = new StringBuilder();
			sql.append(" select * from hq_item_combo_details where iitem_id in (" + itemIds + ")");
			return this.query4Json(tenantId, sql.toString());
		}
		return null;
	}

	@Override
	public JSONObject getBillMemberInfo(String billNum, String type, String cardCode) throws Exception
	{
		StringBuilder sb = new StringBuilder("SELECT * FROM pos_bill_member WHERE bill_num=? and type =? and card_code=? ORDER BY id LIMIT 1");
		List<JSONObject> list = this.query4Json(null, sb.toString(), new Object[]
		{ billNum, type, cardCode });
		if (null != list && list.size() > 0)
		{
			return list.get(0);
		}
		return null;
	}

	@Override
	public void insertOpenId(String tenancyId, Integer storeId, String billnum, String openId) throws Exception
	{
		if (!StringUtils.isEmpty(billnum) && !StringUtils.isEmpty(openId))
		{
			StringBuilder sql = new StringBuilder();
			sql.append("INSERT INTO pos_bill_lock ( tenancy_id, store_id, bill_num, lock_state, open_id, bill_state )values(?,?,?,?,?,?)");
			this.update(sql.toString(), new Object[]
			{ tenancyId, storeId, billnum, 0, openId, 0 });
		}
	}

	@Override
	public void updateOpenId(String billnum, String openId) throws Exception
	{
		if (!StringUtils.isEmpty(billnum) && !StringUtils.isEmpty(openId))
		{
			StringBuilder sql = new StringBuilder();
			sql.append("UPDATE pos_bill_lock SET open_id='");
			sql.append(openId);
			sql.append("' where bill_num = '");
			sql.append(billnum);
			sql.append("'");
			this.update(sql.toString(), null);
		}
	}

	@Override
	public List<JSONObject> getBillLockOpenId(String tenancyId, int storeId, String tableCode) throws Exception
	{
		StringBuffer sql = new StringBuffer();
		sql.append(" select pbl.id,pbl.store_id,pbl.open_id from pos_bill_lock pbl").append(" left join pos_bill pb on pbl.bill_num = pb.bill_num").append(" where pb.store_id = " + storeId + " and pb.table_code = '" + tableCode + "' and pb.bill_property = 'OPEN'");
		return this.query4Json(tenancyId, sql.toString());
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<PaymentWay> findPaymentWay(String tenantId, Integer storeId, List<String> paymentClassList) throws Exception
	{
		String paymentClasses = StringUtils.collectionToDelimitedString(paymentClassList, ",", "'", "'");
		if (storeId != null && !StringUtils.isEmpty(paymentClasses))
		{
			StringBuilder sql = new StringBuilder();
			sql.append("select pw.* from payment_way pw LEFT JOIN payment_way_of_ogran pwo on pw.id = pwo.payment_id where pwo.organ_id=").append(storeId).append(" and pw.payment_class in (").append(paymentClasses).append(")");
			return (List<PaymentWay>) this.query(tenantId, sql.toString(), PaymentWay.class);
		}
		return null;
	}

	@Override
	public void savePosBillPayment(List<PosBillPayment> list) throws Exception
	{
		if (list == null || list.size() <= 0)
		{
			return;
		}
		StringBuilder sql = new StringBuilder();
		sql.append(
				"INSERT INTO pos_bill_payment ( tenancy_id, store_id, bill_num, table_code, type, jzid, name, name_english, amount, count, number, phone, report_date, shift_id, pos_num, cashier_num, last_updatetime, is_ysk, rate, currency_amount, upload_tag, customer_id, bill_code, remark, payment_state, param_cach, batch_num, more_coupon, fee, fee_rate, coupon_type, yjzid, coupon_buy_price, due, tenancy_assume, third_assume, third_fee,payment_uid ) VALUES");
		for (int i = 0; i < list.size(); i++)
		{
			if (i != 0)
			{
				sql.append(",");
			}
			sql.append("(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");
		}
		List<Object> paramList = new ArrayList<>();
		for (PosBillPayment posBillPayment : list)
		{
			paramList.add(posBillPayment.getTenancy_id());
			paramList.add(posBillPayment.getStore_id());
			paramList.add(posBillPayment.getBill_num());
			paramList.add(posBillPayment.getTable_code());
			paramList.add(posBillPayment.getType());
			paramList.add(posBillPayment.getJzid());
			paramList.add(posBillPayment.getName());
			paramList.add(posBillPayment.getName_english());
			paramList.add(posBillPayment.getAmount());
			paramList.add(posBillPayment.getCount());
			paramList.add(posBillPayment.getNumber());
			paramList.add(posBillPayment.getPhone());
			paramList.add(posBillPayment.getReport_date());
			paramList.add(posBillPayment.getShift_id());
			paramList.add(posBillPayment.getPos_num());
			paramList.add(posBillPayment.getCashier_num());
			paramList.add(posBillPayment.getLast_updatetime());
			paramList.add(posBillPayment.getIs_ysk());
			paramList.add(posBillPayment.getRate());
			paramList.add(posBillPayment.getCurrency_amount());
			paramList.add(posBillPayment.getUpload_tag());
			paramList.add(posBillPayment.getCustomer_id());
			paramList.add(posBillPayment.getBill_code());
			paramList.add(posBillPayment.getRemark());
			paramList.add(posBillPayment.getPayment_state());
			paramList.add(posBillPayment.getParam_cach());
			paramList.add(posBillPayment.getBatch_num());
			paramList.add(posBillPayment.getMore_coupon());
			paramList.add(posBillPayment.getFee());
			paramList.add(posBillPayment.getFee_rate());
			paramList.add(posBillPayment.getCoupon_type());
			paramList.add(posBillPayment.getYjzid());
			paramList.add(posBillPayment.getCoupon_buy_price());
			paramList.add(posBillPayment.getDue());
			paramList.add(posBillPayment.getTenancy_assume());
			paramList.add(posBillPayment.getThird_assume());
			paramList.add(posBillPayment.getThird_fee());
			paramList.add(posBillPayment.getPayment_uid());
		}
		this.update(sql.toString(), paramList.toArray());
	}

	@Override
	public void savePosBillMember(List<PosBillMember> list) throws Exception
	{
		if (list == null || list.size() <= 0)
		{
			return;
		}
		StringBuilder sql = new StringBuilder();
		sql.append(
				"INSERT INTO pos_bill_member ( tenancy_id, store_id, bill_num, report_date, type, amount, credit, card_code, mobil, last_updatetime, upload_tag, remark, bill_code, request_state, customer_code, customer_name, consume_before_credit, consume_after_credit, consume_before_main_balance, consume_before_reward_balance, consume_after_main_balance, consume_after_reward_balance ) VALUES");
		for (int i = 0; i < list.size(); i++)
		{
			if (i != 0)
			{
				sql.append(",");
			}
			sql.append("(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");
		}
		List<Object> paramList = new ArrayList<>();
		for (PosBillMember posBillMember : list)
		{
			paramList.add(posBillMember.getTenancy_id());
			paramList.add(posBillMember.getStore_id());
			paramList.add(posBillMember.getBill_num());
			paramList.add(posBillMember.getReport_date());
			paramList.add(posBillMember.getType());
			paramList.add(posBillMember.getAmount());
			paramList.add(posBillMember.getCredit());
			paramList.add(posBillMember.getCard_code());
			paramList.add(posBillMember.getMobil());
			paramList.add(posBillMember.getLast_updatetime());
			paramList.add(posBillMember.getUpload_tag());
			paramList.add(posBillMember.getRemark());
			paramList.add(posBillMember.getBill_code());
			paramList.add(posBillMember.getRequest_state());
			paramList.add(posBillMember.getCustomer_code());
			paramList.add(posBillMember.getCustomer_name());
			paramList.add(posBillMember.getConsume_before_credit());
			paramList.add(posBillMember.getConsume_after_credit());
			paramList.add(posBillMember.getConsume_before_main_balance());
			paramList.add(posBillMember.getConsume_before_reward_balance());
			paramList.add(posBillMember.getConsume_after_main_balance());
			paramList.add(posBillMember.getConsume_after_reward_balance());
		}
		this.update(sql.toString(), paramList.toArray());
	}

	@Override
	public void saveCrmCardTradingList(List<CrmCardTradingListEntity> list) throws Exception
	{
		if (list == null || list.size() <= 0)
		{
			return;
		}
		StringBuilder sql = new StringBuilder();
		sql.append(
				"INSERT INTO crm_card_trading_list ( tenancy_id,card_id, card_code, bill_code, chanel, store_id, business_date, main_trading, reward_trading, operat_type, main_original, reward_original, deposit, operator, operate_time, bill_money, third_bill_code, bill_code_original, activity_id, customer_id, revoked_trading, batch_num, last_updatetime, store_updatetime, card_class_id, name, mobil, operator_id, shift_id, total_balance, reward_balance, main_balance, pay_type, salesman, commission_saler_money, commission_store_money, invoice_balance, is_invoice, payment_state, recharge_state, request_status, request_code, request_msg )values");
		for (int i = 0; i < list.size(); i++)
		{
			if (i != 0)
			{
				sql.append(",");
			}
			sql.append("(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");
		}
		List<Object> paramList = new ArrayList<>();
		for (CrmCardTradingListEntity crmCardTradingList : list)
		{
			paramList.add(crmCardTradingList.getTenancy_id());
			paramList.add(crmCardTradingList.getCard_id());
			paramList.add(crmCardTradingList.getCard_code());
			paramList.add(crmCardTradingList.getBill_code());
			paramList.add(crmCardTradingList.getChanel());
			paramList.add(crmCardTradingList.getStore_id());
			paramList.add(crmCardTradingList.getBusiness_date());
			paramList.add(crmCardTradingList.getMain_trading());
			paramList.add(crmCardTradingList.getReward_trading());
			paramList.add(crmCardTradingList.getOperat_type());
			paramList.add(crmCardTradingList.getMain_original());
			paramList.add(crmCardTradingList.getReward_original());
			paramList.add(crmCardTradingList.getDeposit());
			paramList.add(crmCardTradingList.getOperator());
			paramList.add(crmCardTradingList.getOperate_time());
			paramList.add(crmCardTradingList.getBill_money());
			paramList.add(crmCardTradingList.getThird_bill_code());
			paramList.add(crmCardTradingList.getBill_code_original());
			paramList.add(crmCardTradingList.getActivity_id());
			paramList.add(crmCardTradingList.getCustomer_id());
			paramList.add(crmCardTradingList.getRevoked_trading());
			paramList.add(crmCardTradingList.getBatch_num());
			paramList.add(crmCardTradingList.getLast_updatetime());
			paramList.add(crmCardTradingList.getStore_updatetime());
			paramList.add(crmCardTradingList.getCard_class_id());
			paramList.add(crmCardTradingList.getName());
			paramList.add(crmCardTradingList.getMobil());
			paramList.add(crmCardTradingList.getOperator_id());
			paramList.add(crmCardTradingList.getShift_id());
			paramList.add(crmCardTradingList.getTotal_balance());
			paramList.add(crmCardTradingList.getReward_balance());
			paramList.add(crmCardTradingList.getMain_balance());
			paramList.add(crmCardTradingList.getPay_type());
			paramList.add(crmCardTradingList.getSalesman());
			paramList.add(crmCardTradingList.getCommission_saler_money());
			paramList.add(crmCardTradingList.getCommission_store_money());
			paramList.add(crmCardTradingList.getInvoice_balance());
			paramList.add(crmCardTradingList.getIs_invoice());
			paramList.add(crmCardTradingList.getPayment_state());
			paramList.add(crmCardTradingList.getRecharge_state());
			paramList.add(crmCardTradingList.getRequest_status());
			paramList.add(crmCardTradingList.getRequest_code());
			paramList.add(crmCardTradingList.getRequest_msg());
		}
		this.update(sql.toString(), paramList.toArray());
	}

	@Override
	public void savePosBillPaymentCouponsList(List<PosBillPaymentCoupons> list) throws Exception
	{
		if (list == null || list.size() <= 0)
		{
			return;
		}
		StringBuilder sql = new StringBuilder();
		sql.append(
				"INSERT INTO pos_bill_payment_coupons ( tenancy_id, store_id, bill_num, report_date, payment_id, coupons_code, deal_value, deal_name, last_updatetime, remark, upload_tag, is_cancel, class_id, type_id, discount_money, discount_num, chanel, price, item_id, item_num, coupons_pro, coupon_type, coupon_buy_price, due, tenancy_assume, third_assume, third_fee, request_state )values");
		for (int i = 0; i < list.size(); i++)
		{
			if (i != 0)
			{
				sql.append(",");
			}
			sql.append("(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");
		}
		List<Object> paramList = new ArrayList<>();
		for (PosBillPaymentCoupons posBillPaymentCoupons : list)
		{
			paramList.add(posBillPaymentCoupons.getTenancy_id());
			paramList.add(posBillPaymentCoupons.getStore_id());
			paramList.add(posBillPaymentCoupons.getBill_num());
			paramList.add(posBillPaymentCoupons.getReport_date());
			paramList.add(posBillPaymentCoupons.getPayment_id());
			paramList.add(posBillPaymentCoupons.getCoupons_code());
			paramList.add(posBillPaymentCoupons.getDeal_value());
			paramList.add(posBillPaymentCoupons.getDeal_name());
			paramList.add(posBillPaymentCoupons.getLast_updatetime());
			paramList.add(posBillPaymentCoupons.getRemark());
			paramList.add(posBillPaymentCoupons.getUpload_tag());
			paramList.add(posBillPaymentCoupons.getIs_cancel());
			paramList.add(posBillPaymentCoupons.getClass_id());
			paramList.add(posBillPaymentCoupons.getType_id());
			paramList.add(posBillPaymentCoupons.getDiscount_money());
			paramList.add(posBillPaymentCoupons.getDiscount_num());
			paramList.add(posBillPaymentCoupons.getChanel());
			paramList.add(posBillPaymentCoupons.getPrice());
			paramList.add(posBillPaymentCoupons.getItem_id());
			paramList.add(posBillPaymentCoupons.getItem_num());
			paramList.add(posBillPaymentCoupons.getCoupons_pro());
			paramList.add(posBillPaymentCoupons.getCoupon_type());
			paramList.add(posBillPaymentCoupons.getCoupon_buy_price());
			paramList.add(posBillPaymentCoupons.getDue());
			paramList.add(posBillPaymentCoupons.getTenancy_assume());
			paramList.add(posBillPaymentCoupons.getThird_assume());
			paramList.add(posBillPaymentCoupons.getThird_fee());
			paramList.add(posBillPaymentCoupons.getRequest_state());
		}
		this.update(sql.toString(), paramList.toArray());
	}

	@Override
	public List<JSONObject> getBilPayment(String tenancyId, int storeId, String billNum) throws Exception
	{
		StringBuffer sql = new StringBuffer();
		sql.append(" select pbp.jzid as id,pbp.name,pbp.amount from pos_bill_payment pbp");
		sql.append(" where pbp.tenancy_id=? and pbp.store_id=? and pbp.bill_num=? order by pbp.id desc ");
		List<JSONObject> list = this.query4Json(tenancyId, sql.toString(), new Object[]
				{ tenancyId, storeId, billNum });
		return list;
	}
}
