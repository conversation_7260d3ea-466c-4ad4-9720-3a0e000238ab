package com.tzx.framework.common.util;

import java.util.List;

import javax.jms.Connection;
import javax.jms.ConnectionFactory;
import javax.jms.Destination;
import javax.jms.JMSException;
import javax.jms.MessageProducer;
import javax.jms.QueueSession;
import javax.jms.TextMessage;

import net.sf.json.JSONObject;

import org.apache.activemq.ActiveMQConnectionFactory;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import com.tzx.base.common.util.PosPropertyUtil;
import com.tzx.dfs.client.FastDFSClient;
import com.tzx.dfs.factory.FastDFSClientFactory;
import com.tzx.framework.common.constant.Constant;
import com.tzx.framework.common.file.CompressAndDecompress;
import com.tzx.framework.common.util.dao.impl.GenericDaoImpl;


public class MessageUtils {

	private static Logger logger = Logger.getLogger(MessageUtils.class);
	public static void main(String[] args) {

		// new MessageUtils().sendMessage("sdfsdf");
		// ActiveMQ的URL: tcp://www.e7e6.net:6666
		// MQ的队列名称: qtoboh
		// 不要使用 MessageUtils 中的方法 请继续完成一个发送MQ的测试消息

		// 测试MQ发送消息
		testSendMQMessage();
	}

	/**
	 * 测试发送MQ消息的方法
	 * 不使用MessageUtils中的现有方法，直接使用ActiveMQ API
	 */
	public static void testSendMQMessage() {
		ConnectionFactory connectionFactory = null;
		Connection connection = null;
		QueueSession session = null;
		Destination destination = null;
		MessageProducer producer = null;

		try {
			logger.info("开始测试MQ发送消息...");

			// 创建连接工厂
			String mqUrl = "tcp://www.e7e6.net:6666";
			String queueName = "qtoboh";
			connectionFactory = new ActiveMQConnectionFactory(mqUrl);

			// 创建连接
			connection = connectionFactory.createConnection();
			connection.start();

			// 创建会话
			session = (QueueSession) connection.createSession(false, QueueSession.AUTO_ACKNOWLEDGE);

			// 创建目标队列
			destination = session.createQueue(queueName);

			// 创建生产者
			producer = session.createProducer(destination);
			producer.setDeliveryMode(2); // 持久化消息

			// 创建测试消息
			String testMessage = "这是一条测试消息 - " + new java.util.Date();
			TextMessage textMessage = session.createTextMessage(testMessage);

			// 发送消息
			producer.send(textMessage);

			logger.info("测试消息发送成功: " + testMessage);
			logger.info("发送到队列: " + queueName);
			logger.info("MQ服务器: " + mqUrl);

		} catch (JMSException e) {
			logger.error("发送MQ测试消息失败 - JMS异常: " + e.getMessage(), e);
		} catch (Exception e) {
			logger.error("发送MQ测试消息失败 - 其他异常: " + e.getMessage(), e);
		} finally {
			// 关闭资源
			try {
				if (producer != null) {
					producer.close();
				}
				if (session != null) {
					session.close();
				}
				if (connection != null) {
					connection.close();
				}
				logger.info("MQ连接资源已关闭");
			} catch (JMSException e) {
				logger.error("关闭MQ连接资源时发生异常: " + e.getMessage(), e);
			}
		}
	}

	protected GenericDaoImpl dao = (GenericDaoImpl) SpringConext.getBean("genericDaoImpl");
	

	//private ResourceBundle resource = ResourceBundle.getBundle("comenresource");
	/**
	 * 判断MQ是否可用
	 * @return
     */
	public static boolean isMQAvailable() {
		ConnectionFactory connectionFactory = null;
		Connection connection = null;
		boolean isAvailable = false;

		try {
			if (connectionFactory == null) {
				connectionFactory = new ActiveMQConnectionFactory(
						PosPropertyUtil.getMsg("tzxmq.user"),
						PosPropertyUtil.getMsg("tzxmq.password"),
						PosPropertyUtil.getMsg("tzxmq.url")
				);
			}
			connection = connectionFactory.createConnection();
			if(connection != null) {
				isAvailable = true;
			}
		} catch (Exception e) {
			logger.error("获取MQ连接失败", e);
		} finally {
			if(connection != null) {
				try {
					connection.close();
				} catch (JMSException e) {
					logger.error("MQ Connection close failed", e);
				}
			}
		}

		return isAvailable;
	}
	public int sendMessage(String sendMsg) {
		
		logger.info("执行MQ发送消息start");
		ConnectionFactory connectionFactory = null;
		Connection connection = null;
		QueueSession session = null;
		Destination producerDstination = null;
		MessageProducer producer = null;
		try {
			if (connectionFactory == null) {
				connectionFactory = new ActiveMQConnectionFactory(
						PosPropertyUtil.getMsg("tzxmq.user"),
						PosPropertyUtil.getMsg("tzxmq.password"),
						PosPropertyUtil.getMsg("tzxmq.url")
						);
			}
			try {
				connection = connectionFactory.createConnection();
			} catch (Exception e) {
				connectionFactory = new ActiveMQConnectionFactory(
						PosPropertyUtil.getMsg("tzxmq.user"),
						PosPropertyUtil.getMsg("tzxmq.password"),
						PosPropertyUtil.getMsg("tzxmq.url")
						);
				connection = connectionFactory.createConnection();
			}
			connection.start();
			session = (QueueSession) connection.createSession(false, 1);
			producerDstination = session.createQueue(Constant.getSystemMap().get("qtoboh"));
			producer = session.createProducer(producerDstination);
			producer.setDeliveryMode(2);
			TextMessage tmsg = session.createTextMessage();
			//logger.info("sendMsg:"+sendMsg);
			if (sendMsg != null) {
				tmsg.setText(sendMsg);
				producer.send(tmsg);
				return 1;
			}
		} catch (JMSException e) {
            logger.error("WRONG:  执行MQ发送消息失败",e);
			logger.error(e.getMessage(), e);
			return 0;
		} catch (Exception ex) {
			logger.error(ex.getMessage(), ex);
			return 0;
		} finally {
			try {
				producer.close();
				session.close();
				connection.close();
				logger.info("执行MQ发送消息end");
			} catch (JMSException e) {
				//e.printStackTrace();
			}
		} // /end

		return 0;
	}
	
	/**
	 * 
	 * @param sendMsg 消息体
	 * @param org_uuid 门店uuid
	 * @param synchro_flag 同步异步标识 0：同步    1：异步
	 * @return
	 */
	public int sendMessage(String sendMsg,String org_uuid,int synchro_flag,String tenancy_id,String store_id) {
		ConnectionFactory connectionFactory = null;
		Connection connection = null;
		QueueSession session = null;
		Destination producerDstination = null;
		MessageProducer producer = null;
		logger.info("执行MQ发送消息start");
		try {
			if (connectionFactory == null) {
				connectionFactory = new ActiveMQConnectionFactory(
						PosPropertyUtil.getMsg("tzxmq.user"),
						PosPropertyUtil.getMsg("tzxmq.password"),
						PosPropertyUtil.getMsg("tzxmq.url")
						);
			}
			try {
				connection = connectionFactory.createConnection();
			} catch (Exception e) {
				connectionFactory = new ActiveMQConnectionFactory(
						PosPropertyUtil.getMsg("tzxmq.user"),
						PosPropertyUtil.getMsg("tzxmq.password"),
						PosPropertyUtil.getMsg("tzxmq.url")
						);
				connection = connectionFactory.createConnection();
			}
			connection.start();
			session = (QueueSession) connection.createSession(false, 1);
			producerDstination = session.createQueue(Constant.getSystemMap().get("qtoboh")+"_"+org_uuid);
			producer = session.createProducer(producerDstination);
			producer.setDeliveryMode(2);
			TextMessage tmsg = session.createTextMessage();
			//logger.info("sendMsg:"+sendMsg+",org_uuid:"+org_uuid);
			if (sendMsg != null) {
				String newMsg = "";
				JSONObject jb = checkSendWayAndRegroupParams(sendMsg,synchro_flag,tenancy_id,store_id);
				int value = 0;
				if(jb.optBoolean("success")){
					value = 1;
					newMsg = jb.optString("msg");
				}else{
					value = 0;
					//newMsg = jb.optString("msg");
				}
				if(StringUtils.isNotBlank(newMsg)){
					//不要使用 System.out.println()，如有必要，用logger.debug()替代(newMsg);
					tmsg.setText(newMsg);
					producer.send(tmsg);
					return value;
				}
				
			}
		} catch (JMSException e) {
			//e.printStackTrace();
			return 0;
		} finally {
			try {
				producer.close();
				session.close();
				connection.close();
				logger.info("执行MQ发送消息end");
			} catch (JMSException e) {
				//e.printStackTrace();
			}
		} // /end

		return 0;
	}
	
	public int sendMessage(String sendMsg,String org_uuid) {
		ConnectionFactory connectionFactory = null;
		Connection connection = null;
		QueueSession session = null;
		Destination producerDstination = null;
		MessageProducer producer = null;
		logger.info("执行MQ发送消息start");
		try {
			if (connectionFactory == null) {
				connectionFactory = new ActiveMQConnectionFactory(
						PosPropertyUtil.getMsg("tzxmq.user"),
						PosPropertyUtil.getMsg("tzxmq.password"),
						PosPropertyUtil.getMsg("tzxmq.url")
						);
			}
			try {
				connection = connectionFactory.createConnection();
			} catch (Exception e) {
				connectionFactory = new ActiveMQConnectionFactory(
						PosPropertyUtil.getMsg("tzxmq.user"),
						PosPropertyUtil.getMsg("tzxmq.password"),
						PosPropertyUtil.getMsg("tzxmq.url")
						);
				connection = connectionFactory.createConnection();
			}
			connection.start();
			session = (QueueSession) connection.createSession(false, 1);
			producerDstination = session.createQueue(Constant.getSystemMap().get("qtoboh")+"_"+org_uuid);
			producer = session.createProducer(producerDstination);
			producer.setDeliveryMode(2);
			TextMessage tmsg = session.createTextMessage();
			logger.info("sendMsg:"+sendMsg+",org_uuid:"+org_uuid);
			if (sendMsg != null) {
				tmsg.setText(sendMsg);
				producer.send(tmsg);
				return 1;
			}
		} catch (JMSException e) {
			//e.printStackTrace();
			return 0;
		} finally {
			try {
				producer.close();
				session.close();
				connection.close();
				logger.info("执行MQ发送消息end");
			} catch (JMSException e) {
				//e.printStackTrace();
			}
		} // /end

		return 0;
	}
	
	
	
	public int sendMessage(String sendMsg,String tenantid,String storeid) {
		logger.info("执行MQ发送消息start");
		ConnectionFactory connectionFactory = null;
		Connection connection = null;
		QueueSession session = null;
		Destination producerDstination = null;
		MessageProducer producer = null;
		try {
			if (connectionFactory == null) {
				connectionFactory = new ActiveMQConnectionFactory(
						PosPropertyUtil.getMsg("tzxmq.user"),
						PosPropertyUtil.getMsg("tzxmq.password"),
						PosPropertyUtil.getMsg("tzxmq.url")
						);
			}
			try {
				connection = connectionFactory.createConnection();
			} catch (Exception e) {
				connectionFactory = new ActiveMQConnectionFactory(
						PosPropertyUtil.getMsg("tzxmq.user"),
						PosPropertyUtil.getMsg("tzxmq.password"),
						PosPropertyUtil.getMsg("tzxmq.url")
						);
				connection = connectionFactory.createConnection();
			}
			connection.start();
			session = (QueueSession) connection.createSession(false, 1);
			producerDstination = session.createQueue(Constant.getSystemMap().get("qtoboh")+"_"+tenantid+"_"+storeid);
			producer = session.createProducer(producerDstination);
			producer.setDeliveryMode(2);
			TextMessage tmsg = session.createTextMessage();
			logger.info("sendMsg:"+sendMsg+",tenantid:"+tenantid+",storeid:"+storeid);
			if (sendMsg != null) {
				tmsg.setText(sendMsg);
				producer.send(tmsg);
				return 1;
			}
		} catch (JMSException e) {
			//e.printStackTrace();
			return 0;
		} finally {
			try {
				producer.close();
				session.close();
				connection.close();
				logger.info("执行MQ发送消息end");
			} catch (JMSException e) {
				//e.printStackTrace();
			}
		} // /end

		return 0;
	}

	/**
	 * SAAS对供应商平台数据传输
	 *
	 * @param sendMsg
	 * @return
	 */
	public int sendMessage(JSONObject sendMsg) {
		logger.info("执行MQ发送消息start");
		ConnectionFactory connectionFactory = null;
		Connection connection = null;
		QueueSession session = null;
		Destination producerDstination = null;
		MessageProducer producer = null;
		try {
			if (connectionFactory == null) {
				connectionFactory = new ActiveMQConnectionFactory(
						PosPropertyUtil.getMsg("tzxmq.user"),
						PosPropertyUtil.getMsg("tzxmq.password"),
						PosPropertyUtil.getMsg("tzxmq.url")
				);
			}
			try {
				connection = connectionFactory.createConnection();
			} catch (Exception e) {
				connectionFactory = new ActiveMQConnectionFactory(
						PosPropertyUtil.getMsg("tzxmq.user"),
						PosPropertyUtil.getMsg("tzxmq.password"),
						PosPropertyUtil.getMsg("tzxmq.url")
				);
				connection = connectionFactory.createConnection();
			}
			connection.start();
			session = (QueueSession) connection.createSession(false, 1);
			producerDstination = session.createQueue(Constant.getSystemMap().get("saas_supplier"));
			producer = session.createProducer(producerDstination);
			producer.setDeliveryMode(2);
			TextMessage tmsg = session.createTextMessage();
			logger.info("sendMsg:"+sendMsg.toString());
			if (sendMsg != null) {
				tmsg.setText(sendMsg.toString());
				producer.send(tmsg);
				return 1;
			}
		} catch (JMSException e) {
			//e.printStackTrace();
			return 0;
		} finally {
			try {
				producer.close();
				session.close();
				connection.close();
				logger.info("执行MQ发送消息end");
			} catch (JMSException e) {
				//e.printStackTrace();
			}
		} // /end

		return 0;
	}
	
	
	
	/*
	 * msg synchro_flag  tenancy_id store_id
	 */
	private JSONObject  checkSendWayAndRegroupParams(String msg,int synchro_flag,String tenancy_id,String store_id) {
		JSONObject jb = new JSONObject();
		StringBuffer sb = new StringBuffer();
		
		try {
		
			sb.append(" select para_value from sys_parameter where para_code = 'MQ_SEND_WAY' and store_id = "+store_id);
			List<JSONObject> list = dao.query4Json(tenancy_id, sb.toString());
			int para_value = 0;
			if(null != list && list.size()>0){
				para_value = list.get(0).optInt("para_value");
			}
			if(0 == para_value){//验证结果为之前方式，直接返回参数继续下发
				jb.put("success", true);
				jb.put("msg", msg);
				jb.put("para_value", para_value);
				logger.info("MQ通过老版本方式下发...数据为："+jb.optString("msg"));
				return jb;
			}
			
			JSONObject mmsg  = new JSONObject();
			mmsg.put("sid", store_id);//store_id
			mmsg.put("t",tenancy_id);//商户号
			mmsg.put("v", para_value);//版本号
			mmsg.put("sy", synchro_flag);//同步异步标识   1异步  0是同步
			mmsg.put("p", 1);//采用新老方式的标识  1代表新的方式  0 代表老的方式
			byte[] srtbyte = msg.getBytes("UTF-8");
			/**
			 * 加密
			 */
			byte[] compressBytes = CompressAndDecompress.compressBytes(srtbyte);
			// 数据字节数组长度
			String returnStr = "";
			
	
			// 如果返回值为空或出现异常，则上传失败。
			jb.put("success", true);
			boolean mark = true;
			/**
			 * 失败重新上传  一共三次
			 */
			for(int i =0 ;i<3;i++){
				JSONObject jjj = uploadToFastDFS(compressBytes);
				if(1 == jjj.optInt("value")){
					returnStr = jjj.optString("url");
					mark = false;
					break;
				}
			}
			if(mark){
				jb.put("success", false);
				logger.info("mq下发时....上传文件三次均失败");
				JSONObject mqlog = new JSONObject();
				mqlog.put("store_id", store_id);
				mqlog.put("transfer_type", Constant.MQ_TRANSFER_4UPLOAD);
				mqlog.put("remark", "上传文件失败");
				dao.insertIgnorCase(tenancy_id, "mq_transfer_logs", mqlog);
				return jb;
			}
			
			mmsg.put("l", compressBytes.length);//长度
			mmsg.put("url", Constant.getSystemMap().get("MQFASTDFSURL")+returnStr);
			jb.put("msg", mmsg.toString());
			logger.info("MQ通过新版本方式下发.....数据为："+jb.optString("msg"));
			//不要使用 System.out.println()，如有必要，用logger.debug()替代(jb.toString());
		} catch (Exception e) {
			//e.printStackTrace();
			jb.put("success", false);
			logger.info("checkSendWayAndRegroupParams方法内部错误");
		}
		
		return jb;
	}
	
	/**
	 * 上传至FastDFS
	 * @param msg
	 * @return 1 表示成功 0表示失败
	 */
	private JSONObject uploadToFastDFS(byte[] msg){
		JSONObject json = new JSONObject();
		int value = 0;
	
		String returnStr = "";
		try {
			FastDFSClient fastDFSClient = FastDFSClientFactory.getFastDFSClient();
			returnStr = fastDFSClient.uploadByteData(msg,"mq", null);
			value = 1;
		} catch (Exception e) {
			value = 0;
			logger.info("mq下发时....上传文件失败");
			//e.printStackTrace();
		}		
		if(null  == returnStr || StringUtils.isBlank(returnStr)){
			value = 0;
		}
		json.put("value", value);
		if(1 == value){
			json.put("url", returnStr);
		}
		return json;
	}

	
}
