package com.tzx.clientorder.acewillwechat.po.springjdbc.dao.imp;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.dao.imp.BaseDaoImp;
import com.tzx.clientorder.common.entity.AcewillInsertOrder;
import com.tzx.clientorder.common.entity.AcewillOrderInfo;
import com.tzx.clientorder.common.entity.AcewillOrderNormalitem;
import com.tzx.clientorder.common.entity.AcewillOrderSetmeal;
import com.tzx.clientorder.common.entity.AcewillOrderSetmealItem;
import com.tzx.clientorder.common.entity.AcewillPosBillMember;
import com.tzx.clientorder.acewillwechat.po.springjdbc.dao.AcewillInsertOrderDao;

import net.sf.json.JSONObject;
@Repository(AcewillInsertOrderDao.NAME)
public class AcewillInserOrderDaoImpl  extends BaseDaoImp implements AcewillInsertOrderDao{
	
	@Override
	public JSONObject getTableStatus(String tenantId,Integer storeId,String tableNo) throws Exception {
		if(!StringUtils.isEmpty(tableNo)&&storeId!=null) {
			StringBuilder stringBuilder = new StringBuilder();
			stringBuilder.append("select id,state,lock_opt_num from pos_tablestate where table_code = '");
			stringBuilder.append(tableNo);
			stringBuilder.append("' and store_id = ");
			stringBuilder.append(storeId);
			stringBuilder.append(" order by last_updatetime desc limit 1");
			List<JSONObject> list = this.query4Json(tenantId, stringBuilder.toString());
			if(list.size()>0) {
				JSONObject jsonObject = list.get(0);
				return jsonObject;
			}
		}
		return null;
	}
	@Override
	public String getBillTaste(AcewillInsertOrder insertOrder) throws Exception {
		String tenantId = insertOrder.getTenent_id(); 
		AcewillOrderInfo order_info = insertOrder.getOrder_info();
		List<String> idList = order_info.getOrdermemo().getId();
		String ids = StringUtils.collectionToDelimitedString(idList, ",");
		StringBuilder billTasteStr = new StringBuilder();
		if(!StringUtils.isEmpty(ids)) {
			StringBuilder stringBuilder = new StringBuilder();
			stringBuilder.append("select name from item_taste where id in (").append(ids).append(")");
			List<JSONObject> list = this.query4Json(tenantId, stringBuilder.toString());
			for (JSONObject jsonObject : list) {
				billTasteStr.append(jsonObject.getString("name")).append(",");
			}
		}
		billTasteStr.append(order_info.getOrdermemo().getText());
		return billTasteStr.toString();
	}
	@Override
	public JSONObject getPosBill(String tenantId,Integer storeId,String tableNo) throws Exception {
		if(!StringUtils.isEmpty(tableNo)&&storeId!=null) {
			StringBuilder stringBuilder = new StringBuilder();
			stringBuilder.append("select * from pos_bill where table_code = '");
			stringBuilder.append(tableNo);
			stringBuilder.append("' and fictitious_table = '");
			stringBuilder.append(tableNo);
			stringBuilder.append("' and store_id = '");
			stringBuilder.append(storeId);
			stringBuilder.append("' and bill_property<> '");
			stringBuilder.append(SysDictionary.BILL_PROPERTY_CLOSED);
			stringBuilder.append("' order by id desc limit 1");
			List<JSONObject> list = this.query4Json(tenantId, stringBuilder.toString());
			if(list.size()>0) {
				JSONObject jsonObject = list.get(0);
				return jsonObject;
			}
		}
		return null;
	}
	@Override
	public void savePosBillMember(AcewillPosBillMember posBillMember) throws Exception {
		if(posBillMember!=null) {
			StringBuilder sql = new StringBuilder();
			sql.append("INSERT INTO pos_bill_member ( tenancy_id, store_id, bill_num, report_date, type, amount, credit, card_code, mobil, last_updatetime, upload_tag, remark, bill_code, request_state, customer_code, customer_name, consume_before_credit, consume_after_credit, consume_before_main_balance, consume_before_reward_balance, consume_after_main_balance, consume_after_reward_balance,generic_field,total_credit,shop_consume_number,brand_consume_number ) VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");
			List<Object> paramList = new ArrayList<>();
			paramList.add(posBillMember.getTenancy_id());
			paramList.add(posBillMember.getStore_id());
			paramList.add(posBillMember.getBill_num());
			paramList.add(posBillMember.getReport_date());
			paramList.add(posBillMember.getType());
			paramList.add(posBillMember.getAmount());
			paramList.add(posBillMember.getCredit());
			paramList.add(posBillMember.getCard_code());
			paramList.add(posBillMember.getMobil());
			paramList.add(posBillMember.getLast_updatetime());
			paramList.add(posBillMember.getUpload_tag());
			paramList.add(posBillMember.getRemark());
			paramList.add(posBillMember.getBill_code());
			paramList.add(posBillMember.getRequest_state());
			paramList.add(posBillMember.getCustomer_code());
			paramList.add(posBillMember.getCustomer_name());
			paramList.add(posBillMember.getConsume_before_credit());
			paramList.add(posBillMember.getConsume_after_credit());
			paramList.add(posBillMember.getConsume_before_main_balance());
			paramList.add(posBillMember.getConsume_before_reward_balance());
			paramList.add(posBillMember.getConsume_after_main_balance());
			paramList.add(posBillMember.getConsume_after_reward_balance());
			paramList.add(posBillMember.getGeneric_field());
            paramList.add(posBillMember.getTotal_credit());
            paramList.add(posBillMember.getBrand_consume_number());
            paramList.add(posBillMember.getShop_consume_number());
			this.update(sql.toString(), paramList.toArray());
		}
	}
	@Override
	public Map<String,String> getUnitNameMap(AcewillInsertOrder insertOrder) throws Exception{
		List<String> duids = new ArrayList<>();
		List<AcewillOrderSetmeal> setmealList = insertOrder.getOrder_info().getSetmeal();
		if(setmealList!=null) {
			for (AcewillOrderSetmeal setmeal : setmealList) {
				List<AcewillOrderSetmealItem> setmealItemList = new ArrayList<>();
				setmealItemList.addAll(setmeal.getMaindish());
				setmealItemList.addAll(setmeal.getMandatory());

				if (null != setmeal.getOptional()){
					setmealItemList.addAll(setmeal.getOptional());
				}else {
					List<AcewillOrderSetmealItem> itemList = new ArrayList<>();
					setmealItemList.addAll(itemList);
				}
				for (AcewillOrderSetmealItem setmealItem : setmealItemList) {
					duids.add(setmealItem.getDuid());
				}
				duids.add(setmeal.getDuid());
			}
		}
		List<AcewillOrderNormalitem> normalitems = insertOrder.getOrder_info().getNormalitems();
		if(normalitems!=null) {
			for (AcewillOrderNormalitem normalitem : normalitems) {
				duids.add(normalitem.getDuid());
			}
		}
		String duidStr = StringUtils.collectionToDelimitedString(duids, ",");
		Map<String,String> unitNameMap = new HashMap<>();
		if(!StringUtils.isEmpty(duidStr)) {
			StringBuilder sql = new StringBuilder();
			sql.append("select id,unit_name from hq_item_unit where id in (");
			sql.append(duidStr);
			sql.append(")");
			List<JSONObject> list = this.query4Json(insertOrder.getTenent_id(), sql.toString());
			for (JSONObject jsonObject : list) {
				unitNameMap.put(jsonObject.getString("id"), jsonObject.getString("unit_name"));
			}
		}
		return unitNameMap;
	}
	@Override
	public Integer getLastItemSerial(String TenentId,String billNUm) throws Exception {
		if(!StringUtils.isEmpty(billNUm)) {
			StringBuilder sql = new StringBuilder();
			sql.append("select coalesce(max(item_serial),'0') item_serial from pos_bill_item where bill_num = '");
			sql.append(billNUm);
			sql.append("'");
			List<JSONObject> list = this.query4Json(TenentId, sql.toString());
			if(list.size()>0) {
				JSONObject jsonObject = list.get(0);
				return jsonObject.getInt("item_serial");
			}
		}
		
		return 0;
	}
	
	@Override
	public List<JSONObject> getItemComboDetails(String TenentId,List<Integer> itemIdList) throws Exception {
		String itemIds = StringUtils.collectionToDelimitedString(itemIdList, ",");
		if(!StringUtils.isEmpty(itemIds)) {
			StringBuilder sql = new StringBuilder();
			/*sql.append(" select * from hq_item_combo_details where iitem_id in ("+ itemIds +")");*/
			sql.append(" select distinct d.id,COALESCE(i.id, d.details_id) as iitem_id,d.is_itemgroup,d.combo_num from hq_item_combo_details d " +
					" left join hq_item_group_details gd on d.details_id = gd.item_group_id and d.is_itemgroup = 'Y' " +
					" left join hq_item_info i on gd.item_id = i.id " +
					" where d.iitem_id in ("+ itemIds +")");
			/*sql.append("select d1.id,d.iitem_id,d.is_itemgroup,d.details_id,d.combo_num,d.standardprice,d.combo_order,d.valid_state,d.item_unit_id,d.fake_id,d.change_num_state from hq_item_combo_details d " +
					" left join" +
					" (select id,iitem_id from hq_item_combo_details " +
					" where is_itemgroup = 'Y' and iitem_id in ("+ itemIds +")" +
					" ) d1 on d.iitem_id = d1.iitem_id" +
					" where d.iitem_id in ("+ itemIds +") and d.is_itemgroup = 'N'");*/
			return this.query4Json(TenentId, sql.toString());
		}
		return null;
	}
	@Override
	public void insertOpenId(String tenancyId,Integer storeId,String billnum,String openId) throws Exception {
		if(!StringUtils.isEmpty(billnum)&&!StringUtils.isEmpty(openId)) {
			StringBuilder sql = new StringBuilder();
			sql.append("INSERT INTO pos_bill_lock ( tenancy_id, store_id, bill_num, lock_state, open_id, bill_state )values(?,?,?,?,?,?)");
			this.update(sql.toString(), new Object[] {
					tenancyId,
					storeId,
					billnum,
					0,
					openId,
					0
			});
		}
	}
	@Override
	public void updateOpenId(String billnum,String openId) throws Exception {
		if(!StringUtils.isEmpty(billnum)&&!StringUtils.isEmpty(openId)) {
			StringBuilder sql = new StringBuilder();
			sql.append("UPDATE pos_bill_lock SET open_id='");
			sql.append(openId);
			sql.append("' where bill_num = '");
			sql.append(billnum);
			sql.append("'");
			this.update(sql.toString(), null);
		}
	}
	@Override
	public List<JSONObject> findItemUnit(String tenentId, List<String> itemIdList) throws Exception {
		String itemIds = StringUtils.collectionToDelimitedString(itemIdList, ",");
		if(!StringUtils.isEmpty(itemIds)) {
			StringBuilder sql = new StringBuilder();
			sql.append("select * from hq_item_unit where is_default='Y' and item_id in (");
			sql.append(itemIds);
			sql.append(")");
			return this.query4Json(tenentId, sql.toString());
		}
		return null;
	}
	@Override
	public JSONObject getPosBillByOrderNum(String tenantId,String orderNum) throws Exception {
		if(!StringUtils.isEmpty(orderNum)) {
			StringBuilder sql = new StringBuilder();
			sql.append("select * from pos_bill where order_num = '").append(orderNum).append("'");
			List<JSONObject> query4Json = this.query4Json(tenantId, sql.toString());
			if(query4Json.size()>0) {
				return query4Json.get(0);
			}
		}
		return null;
	}
}
