package com.tzx.clientorder.wechatprogram.common.entity.program;

import java.util.List;

public class PayMemberEntity extends MemberEntity
{
	/**
	 * 消费获得积分
	 */
	private Double				receive_credit;
	/**
	 * 消费获得券
	 */
	private List<MemberCouponsEntity>	receive_coupons;

	public Double getReceive_credit()
	{
		return ((null != receive_credit && !receive_credit.isNaN()) ? receive_credit : 0d);
	}

	public void setReceive_credit(Double receive_credit)
	{
		this.receive_credit = receive_credit;
	}

	public List<MemberCouponsEntity> getReceive_coupons()
	{
		return receive_coupons;
	}

	public void setReceive_coupons(List<MemberCouponsEntity> receive_coupons)
	{
		this.receive_coupons = receive_coupons;
	}
}
