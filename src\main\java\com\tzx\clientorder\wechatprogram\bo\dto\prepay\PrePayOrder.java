package com.tzx.clientorder.wechatprogram.bo.dto.prepay;

import java.util.List;

import com.tzx.clientorder.wechatprogram.common.entity.program.ConsumeCouponsEntity;
import com.tzx.clientorder.wechatprogram.common.entity.program.OrderInfoEntity;

public class PrePayOrder extends OrderInfoEntity
{
	// 是否开发票
	private Integer						invoice;
	// 发票抬头
	private String						invoice_title;
	// 发票价格
	private Double						invoice_price;

	private List<ConsumeCouponsEntity>	coupons_info;

	public Integer getInvoice()
	{
		return invoice;
	}

	public void setInvoice(Integer invoice)
	{
		this.invoice = invoice;
	}

	public String getInvoice_title()
	{
		return invoice_title;
	}

	public void setInvoice_title(String invoice_title)
	{
		this.invoice_title = invoice_title;
	}

	public Double getInvoice_price()
	{
		return invoice_price;
	}

	public void setInvoice_price(Double invoice_price)
	{
		this.invoice_price = invoice_price;
	}

	public List<ConsumeCouponsEntity> getCoupons_info()
	{
		return coupons_info;
	}

	public void setCoupons_info(List<ConsumeCouponsEntity> coupons_info)
	{
		this.coupons_info = coupons_info;
	}
}
