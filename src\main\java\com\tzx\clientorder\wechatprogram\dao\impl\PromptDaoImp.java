package com.tzx.clientorder.wechatprogram.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.tzx.clientorder.wechatprogram.dao.PromptDao;

import net.sf.json.JSONObject;

/**
 * Created by qingui on 2018-06-26.
 */
@Repository(PromptDao.NAME)
public class PromptDaoImp extends PromptGeneralDaoImp implements PromptDao 
{
    @Override
    public JSONObject getBillInfo(String tenancyId, int storeId, String billNum) throws Exception
    {
    	StringBuffer sql = new StringBuffer();
		sql.append(" select pb.bill_num,trim(pb.bill_taste) as bill_taste,pb.bill_amount,pb.payment_amount,pb.guest,pb.order_source,");
		sql.append(" pb.service_amount,pb.fictitious_table,pb.table_code, pb.order_num,pb.discount_mode_id,hdc.discount_case_name,pb.discount_rate,pb.discount_amount,pb.discountk_amount,");
		sql.append(" (case when t.lock_pos_num is null or t.lock_pos_num = '' then '1' else '3' end) as is_locked, pb.opentable_time");
		sql.append(" from pos_bill pb left join pos_tablestate t on pb.store_id = t.store_id and pb.fictitious_table = t.table_code");
		sql.append(" left join hq_discount_case hdc on pb.discount_case_id = hdc.id ");
		sql.append(" where pb.tenancy_id=? and pb.store_id=? and pb.bill_num=? ");
		List<JSONObject> list = this.query4Json(tenancyId, sql.toString(), new Object[]
		{ tenancyId, storeId, billNum });
		
		if (null != list && list.size() > 0) return list.get(0);
		return null;
    }
}
