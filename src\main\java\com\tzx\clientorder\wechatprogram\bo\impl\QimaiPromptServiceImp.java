package com.tzx.clientorder.wechatprogram.bo.impl;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import com.tzx.base.cache.util.CacheTableUtil;
import com.tzx.clientorder.wechatprogram.common.constant.PromptConstant;
import com.tzx.clientorder.wechatprogram.dao.AfterPaymentDao;
import com.tzx.framework.common.entity.Data;
import com.tzx.framework.common.util.DateUtil;
import net.sf.ehcache.util.TimeUtil;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import com.tzx.base.common.util.PosPropertyUtil;
import com.tzx.base.entity.PosSoldOut;
import com.tzx.clientorder.wechatprogram.bo.AfterPaymentService;
import com.tzx.clientorder.wechatprogram.bo.PromptBasicDataService;
import com.tzx.clientorder.wechatprogram.bo.PromptService;
import com.tzx.clientorder.wechatprogram.dao.BasicDataDao;
import com.tzx.clientorder.wechatprogram.dao.PromptDao;
import com.tzx.framework.common.constant.SysParameterCode;
import com.tzx.framework.common.util.CommonUtil;
import com.tzx.framework.common.util.HttpUtil;
import com.tzx.framework.common.util.SpringConext;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.util.ParamUtil;

import net.sf.json.JSONObject;

/**
 * Created by qingui on 2018-07-24.
 */
@Service(PromptService.QIMAI_NAME)
public class QimaiPromptServiceImp implements PromptService
{

	private Logger				logger					= Logger.getLogger(QimaiPromptServiceImp.class);

	/** 上传订单url */
	private static final String		UPLOAD_ORDER_URL			= "/api/order/uploadOrder";
	/** 沽清url */
	private static final String		UPLOAD_SOLD_OUT_URL			= "/api/order/soldoutDish";
	/** 沽清url */
	private static final String		UPLOAD_CANCEL_SOLD_OUT_URL	= "/api/order/cancelSoldoutDish";
	/** 门店退单url */
	private static final String		ORDER_QUIT_URL				= "/api/order/refundOrder";
	/** 解锁订单 */
	private static final String		ORDER_UNLOCK_URL			= "/api/order/unlockOrder";
	/** 锁定订单 */
	private static final String		ORDER_LOCK_URL				= "/api/order/lockOrder";
	/** 清台 */
	private static final String		ORDER_COMPLETE_URL			= "/api/order/completeOrder";
	/** 收银同步菜品接口 */
	private static final String		SYNCHRO_DATA_DISH_URL		= "/api/order/syncShopBaseInfo";

	/** 订单反结账 */
	private static final String		ORDER_UPLOAD_PAYORDER_URL			= "/api/order/uploadPayOrder";
	/** 修改付款 */
	private static final String		ORDER_MODIFY_PAYORDER_URL			= "/api/order/updatePaymentInfo";
	private static final String		QIMAI_REQUEST_URL_KEY		= "qimai.request.url";

	@Resource(name = PromptBasicDataService.NAME)
	private PromptBasicDataService	basicService;
	
	@Resource(name = PromptDao.NAME)
	private PromptDao			posGenericDao;

	@Resource(name = BasicDataDao.NAME)
	private BasicDataDao		basicDao;

	@Resource(name = AfterPaymentDao.NAME)
	private AfterPaymentDao				afterPaymentDao;
	
	/**
	 * @param url
	 * @return
	 * @throws Exception
	 */
	private String getRequestUrl(String url) throws Exception
	{
		return PosPropertyUtil.getMsg(QIMAI_REQUEST_URL_KEY) + url;
	}

	/**
	 * @param orderSource
	 * @return
	 * @throws Exception
	 */
	private boolean iswechatprogramOrder(String orderSource) throws Exception
	{
		return SysDictionary.CHANEL_QIMAI.equals(orderSource);
	}

	/**
	 * 判断是否启用企迈小程序点餐
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param orderSource
	 * @return
	 * @throws Exception
	 */
	private boolean isUserwechatprogram(String tenancyId, int storeId) throws Exception
	{
		String orderType = posGenericDao.getSysParameter(tenancyId, storeId, SysParameterCode.USER_ORDER_TYPE_KEY);
		return SysDictionary.USER_ORDER_TYPE_QM_PROGRAM.equals(orderType);
	}

	@Override
	public void syncDishData(String tenancyId, int storeId) throws Exception {
		if (false == this.isUserwechatprogram(tenancyId, storeId)) {
			return;
		}
//		String customerType = CacheTableUtil.getSysParameter("CustomerType");
//		if (SysDictionary.CUSTOMER_TYPE_YAZUO.equals(customerType)){
//			logger.info("企迈小程序对接雅座会员模式不同步菜品信息 " );
//			return;
//		}
		
		JSONObject requestJson = basicService.getBasicData(tenancyId, storeId);
		requestJson.put("tenancy_id", tenancyId);
		requestJson.put("shopid", storeId);

		String url = this.getRequestUrl(SYNCHRO_DATA_DISH_URL);
		logger.info("企迈小程序收银同步菜品请求参数 =============> " + requestJson.toString());
		String result = HttpUtil.sendPostRequest(url, requestJson.toString(),10000,1200000);
		logger.info("企迈小程序收银同步菜品返回 参数=============> " + result);
	}

	@Override
	public JSONObject syncSoldoutData(String tenancyId, int storeId,List<PosSoldOut> soldList) throws Exception
	{
		if(!this.isUserwechatprogram(tenancyId, storeId))
		{
			return null;
		}
		
		JSONObject requestJson = basicService.getSoldOutData(tenancyId, storeId);
		requestJson.put("tenancy_id", tenancyId);
		requestJson.put("shopid", storeId);

		String url = this.getRequestUrl(UPLOAD_SOLD_OUT_URL);
		logger.info("企迈小程序同步估清请求参数=============> " + requestJson.toString());
		String msg = HttpUtil.sendPostRequest(url, requestJson.toString());
		logger.info("企迈小程序同步估清返回参数=============> " + msg);
		if (!CommonUtil.isNullOrEmpty(msg))
		{
			return JSONObject.fromObject(msg);
		}
		else
		{
			JSONObject result = new JSONObject();
			result.put("errcode", -1);
			result.put("errmsg", "同步估清失败");
			result.put("result", new JSONObject());
			return result;
		}
	}
	
	@Override
	public JSONObject cancelSoldoutData(String tenancyId, int storeId, List<PosSoldOut> soldList) throws Exception
	{
		if (null == soldList || 0 == soldList.size())
		{
			return null;
		}

		if (!this.isUserwechatprogram(tenancyId, storeId))
		{
			return null;
		}

		List<Integer> dishIdList = new ArrayList<Integer>();
		for (PosSoldOut soldOut : soldList)
		{
			dishIdList.add(soldOut.getItem_id());
		}

		List<JSONObject> dishList = basicDao.getItemInfoList(tenancyId, dishIdList);

		List<JSONObject> dishs = new ArrayList<JSONObject>();
		for (JSONObject itemJson : dishList)
		{
			JSONObject soldOutJson = new JSONObject();
			soldOutJson.put("dishid", ParamUtil.getIntegerValueByObject(itemJson, "item_id"));
			soldOutJson.put("dishno", ParamUtil.getStringValueByObject(itemJson, "item_code"));
			soldOutJson.put("unitid", ParamUtil.getIntegerValueByObject(itemJson, "item_unit_id"));
			soldOutJson.put("cookid", "0");
			soldOutJson.put("type", "1");
			dishs.add(soldOutJson);
		}

		JSONObject requestJson = new JSONObject();
		requestJson.put("tenancy_id", tenancyId);
		requestJson.put("shopid", storeId);
		requestJson.put("soldout", dishs);

		String url = this.getRequestUrl(UPLOAD_CANCEL_SOLD_OUT_URL);
		logger.info("企迈小程序同步估清请求参数=============> " + requestJson.toString());
		String msg = HttpUtil.sendPostRequest(url, requestJson.toString());
		logger.info("企迈小程序同步估清返回参数=============> " + msg);
		if (!CommonUtil.isNullOrEmpty(msg))
		{
			return JSONObject.fromObject(msg);
		}
		else
		{
			JSONObject result = new JSONObject();
			result.put("errcode", -1);
			result.put("errmsg", "同步估清失败");
			result.put("result", new JSONObject());
			return result;
		}
	}

	@Override
	public JSONObject orderRefund(String tenancyId, int storeId, String billNum, String orderNum, String tableCode, String orderSource) throws Exception
	{
		// 该门店是否开启了企迈小程序
		if (false == this.isUserwechatprogram(tenancyId, storeId))
		{
			logger.info("该门店是否开启了企迈小程序");
			return null;
		}
		// 账单不是企迈小程序来源的账单
		if (false == this.iswechatprogramOrder(orderSource))
		{
			logger.info("账单不是企迈小程序来源的账单");
			return null;
		}

		String url = this.getRequestUrl(ORDER_QUIT_URL);
		logger.info("POS通知企迈" + url + "退单的平台订单号是" + orderNum);

		JSONObject param = new JSONObject();
		param.put("tenancy_id", tenancyId);
		param.put("shopid", String.valueOf(storeId));
		param.put("tableno", tableCode);
		param.put("ordernum", orderNum);

		String msg = HttpUtil.sendPostRequest(url, param.toString());
		logger.info("企迈小程序平台返回的退单信息是：" + msg);
		if (!CommonUtil.isNullOrEmpty(msg))
		{
			return JSONObject.fromObject(msg);
		}
		else
		{
			JSONObject result = new JSONObject();
			result.put("errcode", -1);
			result.put("errmsg", "退单失败");
			result.put("result", new JSONObject());
			return result;
		}
	}

	@Override
	public JSONObject completeOrder(String tenancyId, int storeId, String billNum) throws Exception
	{
		// 该门店是否开启了企迈小程序以及是不是小程序订单
		if (!this.isUserwechatprogram(tenancyId, storeId))
		{
			return null;
		}
		
		JSONObject order = posGenericDao.getBillInfo(tenancyId,storeId, billNum);
		if (CommonUtil.isNullOrEmpty(order))
		{
			logger.info("通知企迈结账查询不到该账单号" + billNum + "对应的账单");
			return null;
		}
		String orderSource = order.optString("order_source");
		String out_order_id = order.optString("order_num");
		String tableCode = order.optString("fictitious_table");
		if (!CommonUtil.hasText(out_order_id) || false == this.iswechatprogramOrder(orderSource))
		{
			logger.info("POS结账，平台订单号为" + out_order_id + ",不通知企迈");
			return null;
		}

		String meal_number = StringUtils.substring(billNum, billNum.length() - 4);
		// String posMark = posGenericDao.getBillPayNames(tenancyId, billNum);
		AfterPaymentService orderService = (AfterPaymentService) SpringConext.getApplicationContext().getBean(AfterPaymentService.NAME);
		List<JSONObject> paymentInfo = orderService.getBilPayment(tenancyId, storeId, billNum);

		String url = this.getRequestUrl(ORDER_COMPLETE_URL);
		logger.info("POS结账通知企迈" + url + "的平台订单号是" + out_order_id + ",取餐号是" + meal_number);

		JSONObject param = new JSONObject();
		param.put("tenancy_id", tenancyId);
		param.put("shopid", String.valueOf(storeId));
		param.put("tableno", tableCode);
		param.put("ordernum", out_order_id);
		param.put("meal_number", meal_number);
		param.put("payment_info", paymentInfo);

		logger.info("企迈平台结账通知参数：" + param.toString());
		String msg = HttpUtil.sendPostRequest(url, param.toString());
		logger.info("企迈平台返回的结账信息是：" + msg);
		if (!CommonUtil.isNullOrEmpty(msg))
		{
			return JSONObject.fromObject(msg);
		}
		else
		{
			JSONObject result = new JSONObject();
			result.put("errcode", -1);
			result.put("errmsg", "结账失败");
			result.put("result", new JSONObject());
			return result;
		}
	}

	@Override
	public JSONObject uploadOrder(String tenancyId, int storeId, String billNum,int operateType,String sTable,String rTable, String sBillNum) throws Exception {
		// 该门店是否开启了企迈小程序
		if (!this.isUserwechatprogram(tenancyId, storeId)) {
			return null;
		}
		Boolean isUpload = true;
		logger.info("通知企迈上传订单该账单号=" + billNum + ",源账单号=" + sBillNum + ",operateType=" + operateType + "对应的账单");
		switch (operateType) {
			case PromptConstant.UPLOADORDER_OPER_ZT:
				break;
			case PromptConstant.UPLOADORDER_OPER_ADD:
				break;
			case PromptConstant.UPLOADORDER_OPER_REFUND:
				break;
			case PromptConstant.UPLOADORDER_OPER_FS:
				break;
			case PromptConstant.UPLOADORDER_OPER_MDIS:
				break;
			case PromptConstant.UPLOADORDER_OPER_MS:
				break;
			case PromptConstant.UPLOADORDER_OPER_DPZT:
				break;
			case PromptConstant.UPLOADORDER_OPER_LOCK:
				break;
			case PromptConstant.UPLOADORDER_OPER_UNLOCK:
				break;
			case PromptConstant.UPLOADORDER_OPER_REGAIN_BILL:
				break;
			default:
				isUpload = false;
				break;
		}
		if (!isUpload) {
			return null;
		}
		String url = this.getRequestUrl(UPLOAD_ORDER_URL);
		if (operateType == PromptConstant.UPLOADORDER_OPER_LOCK) {
			url = this.getRequestUrl(ORDER_LOCK_URL);

		} else if (operateType == PromptConstant.UPLOADORDER_OPER_UNLOCK) {
			url = this.getRequestUrl(ORDER_UNLOCK_URL);
		}
		else if(operateType == PromptConstant.UPLOADORDER_OPER_REGAIN_BILL){
			url = this.getRequestUrl(ORDER_UPLOAD_PAYORDER_URL);
		}

		//单品转台要判定两个桌台的信息，比较特殊
		if (operateType == PromptConstant.UPLOADORDER_OPER_DPZT) {
			JSONObject billObject = posGenericDao.getBillInfo(tenancyId, storeId, sBillNum);
			if (CommonUtil.isNullOrEmpty(billObject)) {
				logger.info("通知企迈上传订单查询不到该账单号" + sBillNum + "对应的账单");
				return null;
			}
			if (this.iswechatprogramOrder(billObject.optString("order_source"))) {
				String orderNum = billObject.optString("order_num");
				String tableCode = billObject.optString("fictitious_table"); // 查询桌台账单
				AfterPaymentService orderService = (AfterPaymentService) SpringConext.getApplicationContext().getBean(AfterPaymentService.NAME);
				JSONObject orderInfo = orderService.getOrderInfo(tenancyId, storeId, tableCode, billObject);
				JSONObject param = new JSONObject();
				param.put("tenancy_id", tenancyId);
				param.put("shopid", storeId);
				param.put("tableno", tableCode);
				param.put("ordernum", orderNum);
				param.put("operate_type", operateType);
				param.put("order_info", orderInfo);
				param.put("table_state", "BUSY");

				logger.info("POS通知企迈" + url + "上传订单的平台订单号是" + orderNum);
				logger.info("单品转台前企迈平台的上传订单参数是：" + param.toString());
				String msg = HttpUtil.sendPostRequest(url, param.toString());
				logger.info("单品转台前企迈平台返回的上传订单信息是：" + msg);
				JSONObject result = new JSONObject();
				if (!CommonUtil.isNullOrEmpty(msg)) {
					result = JSONObject.fromObject(msg);
					if (result.optInt("success") != 1) {
						result.put("errcode", -1);
						result.put("errmsg", "上传订单失败");
						result.put("result", new JSONObject());
						return result;
					}
				} else {
					result.put("errcode", -1);
					result.put("errmsg", "上传订单失败");
					result.put("result", new JSONObject());
					return result;
				}
			} else {
				logger.info("非企迈来源，不上传通知企迈上传订单该账单号" + sBillNum + "对应的账单");
			}

			billObject = posGenericDao.getBillInfo(tenancyId, storeId, billNum);
			if (CommonUtil.isNullOrEmpty(billObject)) {
				logger.info("通知企迈上传订单查询不到该账单号" + billNum + "对应的账单");
				return null;
			}
			if (this.iswechatprogramOrder(billObject.optString("order_source"))) {
				String orderNum = billObject.optString("order_num");
				String tableCode = billObject.optString("fictitious_table"); // 查询桌台账单
				AfterPaymentService orderService = (AfterPaymentService) SpringConext.getApplicationContext().getBean(AfterPaymentService.NAME);
				JSONObject orderInfo = orderService.getOrderInfo(tenancyId, storeId, tableCode, billObject);
				JSONObject param = new JSONObject();
				param.put("tenancy_id", tenancyId);
				param.put("shopid", storeId);
				param.put("tableno", tableCode);
				param.put("ordernum", orderNum);
				param.put("operate_type", operateType);
				param.put("order_info", orderInfo);
				param.put("table_state", "BUSY");

				logger.info("POS通知企迈" + url + "上传订单的平台订单号是" + orderNum);
				logger.info("单品转台后企迈平台的上传订单参数是：" + param.toString());
				String msg = HttpUtil.sendPostRequest(url, param.toString());
				logger.info("单品转台后企迈平台返回的上传订单信息是：" + msg);
				JSONObject result = new JSONObject();
				if (!CommonUtil.isNullOrEmpty(msg)) {
					result = JSONObject.fromObject(msg);
					if (result.optInt("success") != 1) {
						result.put("errcode", -1);
						result.put("errmsg", "上传订单失败");
						result.put("result", new JSONObject());
						return result;
					}
					return result;
				} else {
					result.put("errcode", -1);
					result.put("errmsg", "上传订单失败");
					result.put("result", new JSONObject());
					return result;
				}
			} else {
				logger.info("非企迈来源，不上传通知企迈上传订单该账单号" + billNum + "对应的账单");
				JSONObject result = new JSONObject();
				result.put("errcode", 0);
				result.put("errmsg", "上传订单成功，目标账单无需上传");
				result.put("result", new JSONObject());
				return result;
			}

		}
		else if (operateType == PromptConstant.UPLOADORDER_OPER_ZT) {
			JSONObject billObject = posGenericDao.getBillInfo(tenancyId, storeId, billNum);
			if (CommonUtil.isNullOrEmpty(billObject)) {
				logger.info("通知企迈上传订单查询不到转台后该账单号" + billNum + "对应的账单");
				return null;
			}
			if (sBillNum.equals(billNum)) {
				//一样只需要说明 是转到了空桌台
				if (!this.iswechatprogramOrder(billObject.optString("order_source"))) {
					logger.info("非企迈来源，不上传通知企迈上传订单该账单号" + billNum + "对应的账单");
					return null;
				}
				String orderNum = billObject.optString("order_num");
				String tableCode = billObject.optString("fictitious_table"); // 查询桌台账单
				AfterPaymentService orderService = (AfterPaymentService) SpringConext.getApplicationContext().getBean(AfterPaymentService.NAME);
				JSONObject orderInfo = orderService.getOrderInfo(tenancyId, storeId, tableCode, billObject);
				JSONObject param = new JSONObject();
				param.put("tenancy_id", tenancyId);
				param.put("shopid", storeId);
				param.put("tableno", tableCode);
				param.put("ordernum", orderNum);
				param.put("operate_type", operateType);
				param.put("order_info", orderInfo);
				param.put("table_state", "BUSY");
				param.put("old_tableno", sTable);
				param.put("old_ordernum", orderNum);

				logger.info("目标桌台是空台，转台企迈平台的上传订单参数是：" + param.toString());
				String msg = HttpUtil.sendPostRequest(url, param.toString());
				logger.info("目标桌台是空台，转台企迈平台返回的上传订单信息是：" + msg);
				JSONObject result = new JSONObject();
				if (!CommonUtil.isNullOrEmpty(msg)) {
					result = JSONObject.fromObject(msg);
					if (result.optInt("success") != 1) {
						result.put("errcode", -1);
						result.put("errmsg", "上传订单失败");
						result.put("result", new JSONObject());
						return result;
					}
				} else {
					result.put("errcode", -1);
					result.put("errmsg", "上传订单失败");
					result.put("result", new JSONObject());
					return result;
				}

			} else {
				JSONObject fromBillObject = posGenericDao.getBillInfo(tenancyId, storeId, sBillNum);
				if (CommonUtil.isNullOrEmpty(fromBillObject)) {
					logger.info("通知企迈上传订单查询不到转台前该账单号" + sBillNum + "对应的账单");
					return null;
				}
				Boolean fromIsWechat = this.iswechatprogramOrder(fromBillObject.optString("order_source"));
				if (!fromIsWechat) {
					logger.info("转台前非企迈来源，不上传通知企迈上传订单该账单号" + sBillNum + "对应的账单");
				}
				//不一样说明转到了 非空转台
				if (!this.iswechatprogramOrder(billObject.optString("order_source"))) {
					logger.info("转台后非企迈来源，不上传通知企迈上传订单该账单号" + billNum + "对应的账单");
					//目标台位非小程序，源桌台是小程序，发送源桌台的清台
					if (fromIsWechat) {
						String orderNum = fromBillObject.optString("order_num");
						String tableCode = fromBillObject.optString("fictitious_table"); // 查询桌台账单
						AfterPaymentService orderService = (AfterPaymentService) SpringConext.getApplicationContext().getBean(AfterPaymentService.NAME);
						JSONObject param = new JSONObject();
						param.put("tenancy_id", tenancyId);
						param.put("shopid", storeId);
						param.put("ordernum", orderNum);
						param.put("operate_type", operateType);
						param.put("old_tableno", null);
						param.put("old_ordernum", null);
						param.put("tableno", tableCode);
						param.put("order_info", null);
						param.put("table_state", "FREE");

						logger.info("目标桌台不是小程序订单，转台企迈平台的上传订单参数是：" + param.toString());
						String msg = HttpUtil.sendPostRequest(url, param.toString());
						logger.info("目标桌台不是小程序订单，转台企迈平台返回的上传订单信息是：" + msg);
						JSONObject result = new JSONObject();
						if (!CommonUtil.isNullOrEmpty(msg)) {
							result = JSONObject.fromObject(msg);
							if (result.optInt("success") != 1) {
								result.put("errcode", -1);
								result.put("errmsg", "上传订单失败");
								result.put("result", new JSONObject());
								return result;
							}
						} else {
							result.put("errcode", -1);
							result.put("errmsg", "上传订单失败");
							result.put("result", new JSONObject());
							return result;
						}
					}
					else{
						return null;
					}
				} else {
					//目标桌台是小程序，只需要发送目标桌台信息
					String orderNum = billObject.optString("order_num");
					String tableCode = billObject.optString("fictitious_table"); // 查询桌台账单
					AfterPaymentService orderService = (AfterPaymentService) SpringConext.getApplicationContext().getBean(AfterPaymentService.NAME);
					JSONObject orderInfo = orderService.getOrderInfo(tenancyId, storeId, tableCode, billObject);
					JSONObject param = new JSONObject();
					param.put("tenancy_id", tenancyId);
					param.put("shopid", storeId);
					param.put("tableno", tableCode);
					param.put("ordernum", orderNum);
					param.put("operate_type", operateType);
					param.put("order_info", orderInfo);
					param.put("table_state", "BUSY");
					//源桌台是小程序，上传源桌台信息，否则上传null
					if (fromIsWechat) {
						param.put("old_tableno", sTable);
						param.put("old_ordernum", fromBillObject.optString("order_num"));
					} else {
						param.put("old_tableno", null);
						param.put("old_ordernum", null);
					}
					logger.info("目标桌台是小程序订单，转台企迈平台的上传订单参数是：" + param.toString());
					String msg = HttpUtil.sendPostRequest(url, param.toString());
					logger.info("目标桌台是小程序订单，转台企迈平台返回的上传订单信息是：" + msg);
					JSONObject result = new JSONObject();
					if (!CommonUtil.isNullOrEmpty(msg)) {
						result = JSONObject.fromObject(msg);
						if (result.optInt("success") != 1) {
							result.put("errcode", -1);
							result.put("errmsg", "上传订单失败");
							result.put("result", new JSONObject());
							return result;
						}
					} else {
						result.put("errcode", -1);
						result.put("errmsg", "上传订单失败");
						result.put("result", new JSONObject());
						return result;
					}
				}
			}
		}
		else if(operateType == PromptConstant.UPLOADORDER_OPER_REGAIN_BILL){
			//恢复账单调用企迈 反结账接口
			JSONObject billObject = posGenericDao.getBillInfo(tenancyId, storeId, billNum);
			if (CommonUtil.isNullOrEmpty(billObject)) {
				logger.info("通知企迈上传订单查询不到该账单号" + billNum + "对应的账单");
				return null;
			}
			// 账单是不是企迈小程序来源的账单
			if (!this.iswechatprogramOrder(billObject.optString("order_source"))) {
				logger.info("非企迈来源，不上传通知企迈上传订单该账单号" + billNum + "对应的账单");
				return null;
			}
			String orderNum = billObject.optString("order_num");
			String tableCode = billObject.optString("fictitious_table"); // 查询桌台账单
			AfterPaymentService orderService = (AfterPaymentService) SpringConext.getApplicationContext().getBean(AfterPaymentService.NAME);
			JSONObject orderInfo = orderService.getOrderInfo(tenancyId, storeId, tableCode, billObject);
			JSONObject param = new JSONObject();
			param.put("table_mark", tableCode);//billObject.optString("guest")
			param.put("people_number", billObject.optString("guest"));
			param.put("order_no",billNum);
			param.put("old_app_no",orderNum);
			param.put("total_amount",billObject.optDouble("bill_amount"));
			param.put("minus_amount",billObject.optDouble("discount_amount"));
			param.put("amount",billObject.optDouble("payment_amount"));
			param.put("create_time", DateUtil.dateToStamp(billObject.optString("opentable_time")));
			param.put("store_id", tenancyId);
			param.put("multi_mark", storeId);

			// 查询最新的openid,查询的是锁单表的openid
			String openid = null;
			List<JSONObject> holdBillList = afterPaymentDao.getBillLockOpenId(tenancyId, storeId, tableCode);
			if (null != holdBillList && holdBillList.size() > 0) {
				openid = holdBillList.get(0).optString("open_id");
			}
			param.put("user_id", openid);

			param.put("mobile", orderInfo.optJSONObject("member").optString("mobile"));

			List<JSONObject> setmealList =  null;
			if(orderInfo.containsKey("setmeal")) {
				setmealList = orderInfo.getJSONArray("setmeal");
			}
			List<JSONObject> normalList = null;
			if(orderInfo.containsKey("normalitems")) {
				normalList = orderInfo.getJSONArray("normalitems");
			}
			List<JSONObject> goods = new ArrayList<JSONObject>();


			if(CommonUtil.hv(normalList)){
				JSONObject dishJson = null;
				for (JSONObject itemJson : normalList) {
					dishJson = new JSONObject();
					dishJson.put("product_no", itemJson.optString("unitid"));
					dishJson.put("trade_mark", itemJson.optString("dishno"));
					dishJson.put("name", itemJson.optString("dish_name"));
					dishJson.put("num", itemJson.optInt("number"));
					dishJson.put("price", itemJson.optDouble("price"));
					goods.add(dishJson);
				}
			}
			if(CommonUtil.hv(setmealList)){
				JSONObject dishJson = null;
				for (JSONObject itemJson : setmealList){
					dishJson = new JSONObject();
					dishJson.put("product_no", itemJson.optString("unitid"));
					dishJson.put("trade_mark", itemJson.optString("dishno"));
					dishJson.put("name", itemJson.optString("dish_name"));
					dishJson.put("num", itemJson.optInt("number"));
					dishJson.put("price", itemJson.optDouble("price"));
					dishJson.put("is_mashup", 0);
					List<JSONObject> order_give_goods = new ArrayList<JSONObject>();


					List<JSONObject> maindishList = null;
					if(itemJson.containsKey("maindish")){
						maindishList = itemJson.getJSONArray("maindish");
					}
					if(CommonUtil.hv(maindishList)){
						JSONObject dishDetailJson = null;
						for (JSONObject detailJson : maindishList){
							dishDetailJson = new JSONObject();
							dishDetailJson.put("product_no", detailJson.optString("unitid"));
							dishDetailJson.put("trade_mark", detailJson.optString("dishno"));
							dishDetailJson.put("name", detailJson.optString("dish_name"));
							dishDetailJson.put("num", detailJson.optInt("number"));
							if(detailJson.containsKey("aprice")) {
								dishDetailJson.put("price", detailJson.optDouble("aprice"));
								dishDetailJson.put("is_mashup", 1);
							}
							else{
								dishDetailJson.put("is_mashup", 0);
							}
							order_give_goods.add(dishDetailJson);
						}
					}
					dishJson.put("order_give_goods", order_give_goods);

					goods.add(dishJson);
				}
			}
			param.put("goods",goods);
			param.put("operate_type", operateType);

			logger.info("POS通知企迈" + url + "上传订单的平台订单号是" + orderNum);
			logger.info("企迈平台的上传订单参数是：" + param.toString());
			String msg = HttpUtil.sendPostRequest(url, param.toString());
			logger.info("企迈平台返回的上传订单信息是：" + msg);
			JSONObject result = new JSONObject();
			if (!CommonUtil.isNullOrEmpty(msg)) {
				result = JSONObject.fromObject(msg);
				if (result.optInt("success") != 1) {
					result.put("errcode", -1);
					result.put("errmsg", "上传订单失败");
					result.put("result", new JSONObject());
					return result;
				}
				else{
					//根据企迈恢复账单返回的值更新 预定单号  有值的时候更新，无值的时候将订单号和来源清空
					String app_no = result.optString("app_no");
					if(CommonUtil.hv(app_no)) {
						afterPaymentDao.updateOrderSource(tenancyId, billNum, app_no, SysDictionary.CHANEL_QIMAI);
					}
					else{
						afterPaymentDao.updateOrderSource(tenancyId, billNum, "", "");
					}
				}
				return result;
			} else {
				result.put("errcode", -1);
				result.put("errmsg", "上传订单失败");
				result.put("result", new JSONObject());
				return result;
			}
		}
		else {
			JSONObject billObject = posGenericDao.getBillInfo(tenancyId, storeId, billNum);
			if (CommonUtil.isNullOrEmpty(billObject)) {
				logger.info("通知企迈上传订单查询不到该账单号" + billNum + "对应的账单");
				return null;
			}
			// 账单是不是企迈小程序来源的账单
			if (!this.iswechatprogramOrder(billObject.optString("order_source"))) {
				logger.info("非企迈来源，不上传通知企迈上传订单该账单号" + billNum + "对应的账单");
				return null;
			}
			String orderNum = billObject.optString("order_num");
			String tableCode = billObject.optString("fictitious_table"); // 查询桌台账单
			AfterPaymentService orderService = (AfterPaymentService) SpringConext.getApplicationContext().getBean(AfterPaymentService.NAME);
			JSONObject orderInfo = orderService.getOrderInfo(tenancyId, storeId, tableCode, billObject);
			JSONObject param = new JSONObject();
			param.put("tenancy_id", tenancyId);
			param.put("shopid", storeId);
			param.put("tableno", tableCode);
			param.put("ordernum", orderNum);
			param.put("operate_type", operateType);
			param.put("order_info", orderInfo);
			param.put("table_state", "BUSY");

			logger.info("POS通知企迈" + url + "上传订单的平台订单号是" + orderNum);

			logger.info("企迈平台的上传订单参数是：" + param.toString());
			String msg = HttpUtil.sendPostRequest(url, param.toString());
			logger.info("企迈平台返回的上传订单信息是：" + msg);
			JSONObject result = new JSONObject();
			if (!CommonUtil.isNullOrEmpty(msg)) {
				result = JSONObject.fromObject(msg);
				if (result.optInt("success") != 1) {
					result.put("errcode", -1);
					result.put("errmsg", "上传订单失败");
					result.put("result", new JSONObject());
					return result;
				}
				return result;
			} else {
				result.put("errcode", -1);
				result.put("errmsg", "上传订单失败");
				result.put("result", new JSONObject());
				return result;
			}
		}
		return  null;

	}

	@Override
	public JSONObject lockOrder(String tenancyId, int storeId, String billNum) throws Exception
	{
		// 该门店是否开启了企迈小程序以及是不是小程序订单
		if (this.isUserwechatprogram(tenancyId, storeId))
		{
			return null;
		}
		JSONObject order = posGenericDao.getBillInfo(tenancyId,storeId, billNum);
		if (CommonUtil.isNullOrEmpty(order))
		{
			logger.info("通知企迈解锁查询不到该账单号" + billNum + "对应的账单");
			return null;
		}
		String orderSource = order.optString("order_source");

		// 未开启企迈小程序或者账单不是企迈小程序来源的账单
		if (false == this.iswechatprogramOrder(orderSource))
		{
			return null;
		}

		String out_order_id = order.optString("order_num");
		String tableCode = order.optString("fictitious_table");

		JSONObject param = new JSONObject();
		param.put("tenancy_id", tenancyId);
		param.put("shopid", String.valueOf(storeId));
		param.put("tableno", tableCode);
		param.put("ordernum", out_order_id);

		String url = this.getRequestUrl(ORDER_LOCK_URL);
		logger.info("POS通知企迈" + url + "解锁订单的平台订单号是" + out_order_id);
		String msg = HttpUtil.sendPostRequest(url, param.toString());
		logger.info("企迈平台返回的解锁订单信息是：" + msg);

		if (!CommonUtil.isNullOrEmpty(msg))
		{
			return JSONObject.fromObject(msg);
		}
		else
		{
			JSONObject result = new JSONObject();
			result.put("errcode", -1);
			result.put("errmsg", "解锁失败");
			result.put("result", new JSONObject());
			return result;
		}
	}

	@Override
	public JSONObject unlockOrder(String tenancyId, int storeId, String billNum) throws Exception
	{
		// 该门店是否开启了企迈小程序以及是不是小程序订单
		if (this.isUserwechatprogram(tenancyId, storeId))
		{
			return null;
		}
		JSONObject order = posGenericDao.getBillInfo(tenancyId, storeId, billNum);
		if (CommonUtil.isNullOrEmpty(order))
		{
			logger.info("通知企迈解锁查询不到该账单号" + billNum + "对应的账单");
			return null;
		}
		String orderSource = order.optString("order_source");

		// 未开启企迈小程序或者账单不是企迈小程序来源的账单
		if (false == this.iswechatprogramOrder(orderSource))
		{
			return null;
		}
		String out_order_id = order.optString("order_num");
		String tableCode = order.optString("fictitious_table");

		JSONObject param = new JSONObject();
		param.put("tenancy_id", tenancyId);
		param.put("shopid", String.valueOf(storeId));
		param.put("tableno", tableCode);
		param.put("ordernum", out_order_id);

		String url = this.getRequestUrl(ORDER_UNLOCK_URL);
		logger.info("POS通知企迈" + url + "解锁订单的平台订单号是" + out_order_id);
		String msg = HttpUtil.sendPostRequest(url, param.toString());
		logger.info("企迈平台返回的解锁订单信息是：" + msg);

		if (!CommonUtil.isNullOrEmpty(msg))
		{
			return JSONObject.fromObject(msg);
		}
		else
		{
			JSONObject result = new JSONObject();
			result.put("errcode", -1);
			result.put("errmsg", "解锁失败");
			result.put("result", new JSONObject());
			return result;
		}
	}

	@Override
	public JSONObject modifyPayOrder(String tenancyId, int storeId, String billNum) throws Exception {
		// 该门店是否开启了企迈小程序以及是不是小程序订单
		if (!this.isUserwechatprogram(tenancyId, storeId))
		{
			return null;
		}

		JSONObject order = posGenericDao.getBillInfo(tenancyId,storeId, billNum);
		if (CommonUtil.isNullOrEmpty(order))
		{
			logger.info("通知企迈修改付款查询不到该账单号" + billNum + "对应的账单");
			return null;
		}
		String orderSource = order.optString("order_source");
		String out_order_id = order.optString("order_num");
		String tableCode = order.optString("fictitious_table");
		if (!CommonUtil.hasText(out_order_id) || false == this.iswechatprogramOrder(orderSource))
		{
			logger.info("非企迈小程序订单，平台订单号为" + out_order_id + ",不通知企迈");
			return null;
		}

		String meal_number = StringUtils.substring(billNum, billNum.length() - 4);
		// String posMark = posGenericDao.getBillPayNames(tenancyId, billNum);
		AfterPaymentService orderService = (AfterPaymentService) SpringConext.getApplicationContext().getBean(AfterPaymentService.NAME);
		List<JSONObject> paymentInfo = orderService.getBilPayment(tenancyId, storeId, billNum);

		String url = this.getRequestUrl(ORDER_MODIFY_PAYORDER_URL);
		logger.info("POS修改付款通知企迈" + url + "的平台订单号是" + out_order_id + ",取餐号是" + meal_number);

		JSONObject param = new JSONObject();
		param.put("tenancy_id", tenancyId);
		param.put("shopid", String.valueOf(storeId));
		param.put("tableno", tableCode);
		param.put("order_num", out_order_id);
		param.put("third_order_no", billNum);
		param.put("meal_number", meal_number);
		param.put("payment_info", paymentInfo);

		logger.info("企迈平台的修改付款通知信息参数是：" + param.toString());
		String msg = HttpUtil.sendPostRequest(url, param.toString());
		logger.info("企迈平台返回的修改付款通知信息是：" + msg);
		if (!CommonUtil.isNullOrEmpty(msg))
		{
			return JSONObject.fromObject(msg);
		}
		else
		{
			JSONObject result = new JSONObject();
			result.put("errcode", -1);
			result.put("errmsg", "修改付款通知失败");
			result.put("result", new JSONObject());
			return result;
		}
	}
}
