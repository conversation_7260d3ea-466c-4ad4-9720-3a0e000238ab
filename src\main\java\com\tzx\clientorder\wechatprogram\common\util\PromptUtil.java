package com.tzx.clientorder.wechatprogram.common.util;

import java.beans.PropertyDescriptor;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;

import com.tzx.clientorder.wechatprogram.common.constant.PromptConstant;
import com.tzx.clientorder.wechatprogram.common.constant.QimaiPaymentWayEnum;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.exception.WxErrorCode;
import com.tzx.framework.common.util.CommonUtil;
import com.tzx.pos.base.constant.SysDictionary;

import net.sf.json.JSONObject;
import net.sf.json.JsonConfig;
import net.sf.json.util.PropertyFilter;

public class PromptUtil
{
	public static JsonConfig getJsonConfig(Class<?> rootClass, Map<String, Class<?>> classMap)
	{
		JsonConfig config = new JsonConfig();
		config.setClassMap(classMap);
		config.setRootClass(rootClass);
		config.setJavaPropertyFilter(new PropertyFilter()
		{
			@Override
			public boolean apply(Object source, String name, Object value)
			{
				PropertyDescriptor propertyDescriptor = BeanUtils.getPropertyDescriptor(source.getClass(), name);
				return propertyDescriptor == null;
			}
		});
		return config;
	}

	/**
	 * @param ordermemo
	 * @return
	 * @throws Exception
	 */
	public static String[] formatOrderMemo(String ordermemo) throws Exception
	{
		if (CommonUtil.hv(ordermemo))
		{
			return ordermemo.trim().split(" ");
		}
		return new String[0];
	}

	/**
	 * @param ordermemo
	 * @return
	 */
	public static String formatOrderMemo(String[] ordermemo)
	{
		if (CommonUtil.hv(ordermemo))
		{
			return StringUtils.join(ordermemo, " ");
		}
		return "";
	}

	/**
	 * @param couponType
	 * @return
	 */
	public static String getCouponPro(String couponType)
	{
		String couponsPro = null;
		switch (couponType)
		{
			case PromptConstant.CUSTOMER_COUPON_TYPE_CASH:
				couponsPro = SysDictionary.COUPONS_PRO_DEDUCT;
				break;
			case PromptConstant.CUSTOMER_COUPON_TYPE_DISH:
				couponsPro = SysDictionary.COUPONS_PRO_DISH;
				break;
			default:
				break;
		}
		return couponsPro;
	}
	
	/**
	 * @param orderType
	 * @return
	 */
	public static String getChannel(String orderType)
	{
		String channel = null;
		if (CommonUtil.hv(orderType))
		{
			switch (orderType)
			{
				case SysDictionary.USER_ORDER_TYPE_QM_PROGRAM:
					channel = SysDictionary.CHANEL_QIMAI;
					break;
			}
		}
		return channel;
	}
	
	/**
	 * @param paySource
	 * @param paymentSource
	 * @return
	 */
	public static String getPaymentClassBySource(String paySource, String paymentSource)
	{
		String paymentClass = null;
		if (SysDictionary.CHANEL_QIMAI.equals(paymentSource)||SysDictionary.CHANEL_ZS.equals(paymentSource))
		{
			paymentClass = QimaiPaymentWayEnum.getPaymentClassBySource(paySource);
		}
		return paymentClass;
	}
	
	@SuppressWarnings("unchecked")
	public static <T> Map<T, List<JSONObject>> buildMap(List<JSONObject> list, String attr)
	{
		Map<T, List<JSONObject>> map = new HashMap<T, List<JSONObject>>();
		for (JSONObject json : list)
		{
			T key = (T) json.opt(attr);
			if (!map.containsKey(key))
			{
				map.put(key, new ArrayList<JSONObject>());
			}
			map.get(key).add(json);
		}
		return map;
	}
	
	public static int[] transferIntegerArray(Collection<Integer> collection)
	{
		if (null == collection || 0 == collection.size())
		{
			return new int[0];
		}

		Integer[] array = collection.toArray(new Integer[0]);
		int[] ids = new int[array.length];
		for (int i = 0; i < ids.length; i++)
		{
			ids[i] = array[i];
		}
		return ids;
	}
	
	public static String getDiscountType(int type)
	{
		String discountType = SysDictionary.DISCOUNT_TYPE_ORDER;
		switch (type)
		{
			case PromptConstant.DISCOUNT_TYPE_RATE:
				discountType = SysDictionary.DISCOUNT_TYPE_DK;
				break;
			case PromptConstant.DISCOUNT_TYPE_PRICE:
				discountType = SysDictionary.DISCOUNT_TYPE_MEMBER;
				break;
			case PromptConstant.DISCOUNT_TYPE_COUPON:
				discountType = SysDictionary.DISCOUNT_TYPE_COUPON;
				break;
			case PromptConstant.DISCOUNT_TYPE_ACTITY:
				discountType = SysDictionary.DISCOUNT_TYPE_AT;
				break;
		}
		return discountType;
	}
	
	public static String getItemRemarkByActiveType(int activeType)
	{
		String itemRemark = null;
		switch (activeType)
		{
			case PromptConstant.ACTIVE_TYPE_1:
				itemRemark = SysDictionary.ITEM_REMARK_HD07;
				break;
			case PromptConstant.ACTIVE_TYPE_2:
				itemRemark = SysDictionary.ITEM_REMARK_HDZC08;
				break;
			case PromptConstant.ACTIVE_TYPE_3:
				itemRemark = SysDictionary.ITEM_REMARK_JJG11;
				break;
		}
		return itemRemark;
	}
}
