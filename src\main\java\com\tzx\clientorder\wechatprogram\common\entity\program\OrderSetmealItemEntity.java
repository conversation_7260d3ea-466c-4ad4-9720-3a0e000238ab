package com.tzx.clientorder.wechatprogram.common.entity.program;

import java.util.List;

public class OrderSetmealItemEntity 
{
	private Integer					dishid;
	private String					dishno;
	private String					dish_name;
	private Integer					unitid;
	private String					unit_name;
	private Double					number;
	private List<OrderCookEntity>	cooks;
	private Double					cook_price;
	private String[]				memo;
	private Double					aprice;
	private Integer					rpdid;
	
	public Integer getDishid()
	{
		return dishid;
	}
	public void setDishid(Integer dishid)
	{
		this.dishid = dishid;
	}
	public String getDishno()
	{
		return dishno;
	}
	public void setDishno(String dishno)
	{
		this.dishno = dishno;
	}
	public String getDish_name()
	{
		return dish_name;
	}
	public void setDish_name(String dish_name)
	{
		this.dish_name = dish_name;
	}
	public Integer getUnitid()
	{
		return unitid;
	}
	public void setUnitid(Integer unitid)
	{
		this.unitid = unitid;
	}
	public String getUnit_name()
	{
		return unit_name;
	}
	public void setUnit_name(String unit_name)
	{
		this.unit_name = unit_name;
	}
	public Double getNumber()
	{
		return number;
	}
	public void setNumber(Double number)
	{
		this.number = number;
	}
	public List<OrderCookEntity> getCooks()
	{
		return cooks;
	}
	public void setCooks(List<OrderCookEntity> cooks)
	{
		this.cooks = cooks;
	}
	public Double getCook_price()
	{
		return cook_price;
	}
	public void setCook_price(Double cook_price)
	{
		this.cook_price = cook_price;
	}
	public String[] getMemo()
	{
		return memo;
	}
	public void setMemo(String[] memo)
	{
		this.memo = memo;
	}
	public Double getAprice()
	{
		return aprice;
	}
	public void setAprice(Double aprice)
	{
		this.aprice = aprice;
	}
	public Integer getRpdid()
	{
		return rpdid;
	}
	public void setRpdid(Integer rpdid)
	{
		this.rpdid = rpdid;
	}
	
	public void setMallListName()
	{
		StringBuilder sb = new StringBuilder("　");
		sb.append(this.dish_name.trim());
		this.dish_name = sb.toString();
	}
}
