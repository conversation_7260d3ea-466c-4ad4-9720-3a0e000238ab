package com.tzx.clientorder.wechatprogram.dao.impl;

import java.util.ArrayList;
import java.util.List;

import org.springframework.stereotype.Repository;

import com.tzx.clientorder.wechatprogram.dao.BasicDataDao;
import com.tzx.pos.base.constant.SysDictionary;
import com.tzx.pos.base.util.SqlUtil;

import net.sf.json.JSONObject;

/**
 * Created by qingui on 2018-06-27.
 */
@Repository(BasicDataDao.NAME)
public class BasicDataDaoImp extends PromptGeneralDaoImp implements BasicDataDao
{

	@Override
	public List<JSONObject> getSoldOutInfo(String tenancyId, int storeId) throws Exception
	{
		StringBuilder sql = new StringBuilder();
		sql.append(" select i.id as dishid,i.item_code as dishno,u.id as unitid, s.num as count, 0 as cookid, 1 as type, (case when s.num = 0 then 2 else 1 end) as status");
		sql.append(" from pos_soldout s ");
		sql.append(" left join hq_item_info i on s.item_id = i.id and i.valid_state = '1' ");
		sql.append(" left join hq_item_unit u on u.item_id = i.id and u.valid_state = '1' ");
		sql.append(" where s.store_id = " + storeId);
		return this.query4Json(tenancyId, sql.toString());
	}

	@Override
	public JSONObject selectOrganInfo(String tenancyId, int storeId) throws Exception
	{
		StringBuilder sql = new StringBuilder(" select * from organ where organ.id=? and organ.valid_state='1' and organ.tenancy_id=? ");
		List<JSONObject> query4Json = this.query4Json(tenancyId, sql.toString(), new Object[]
		{ storeId, tenancyId });
		return query4Json.size() > 0 ? query4Json.get(0) : null;
	}
	
	@Override
	public List<JSONObject> getTables(String tenancy_id, int store_id) throws Exception
	{
		StringBuilder sql = new StringBuilder();
		sql.append(" select ti.id as tableid, ti.table_code as tableno, 0 as mealfee, ti.table_name as table_name,ti.business_area_id as areaid,table_property_id ");
		sql.append(" from tables_info ti where ti.tenancy_id='" + tenancy_id + "' and ti.valid_state='1' and ti.organ_id='" + store_id + "' ");
		return this.query4Json(tenancy_id, sql.toString());
	}

	@Override
	public List<JSONObject> selectMemo(String tenancy_id, int store_id) throws Exception
	{
		StringBuilder sql = new StringBuilder();
		sql.append(" select it.id as memoid, COALESCE(it.father_id, 0) as pmemoid, it.name as memo_name ");
		sql.append(" from item_taste it ");
		sql.append(" left join item_taste_org ito on it.tenancy_id=ito.tenancy_id and it.id=ito.teste_id");
		sql.append(" where ito.tenancy_id='" + tenancy_id + "' and ito.store_id='" + store_id + "'");
		sql.append(" and it.valid_state='1' and (it.father_id is not null and it.father_id<>'0') ");
		sql.append(" order by it.father_id ");
		return this.query4Json(tenancy_id, sql.toString());
	}
	
	@Override
	public List<JSONObject> getDishKinds(String tenancyId, Integer storeId, String chanel) throws Exception
	{
		StringBuilder sql = new StringBuilder();
		sql.append("with recursive item_class as (");
		sql.append(" select hic.id,hic.itemclass_name,himco.classorder,hic.itemclass_code,hic.father_id,him.item_menu_id,count(*) item_count from (");
		sql.append(" select distinct himo.tenancy_id,himo.store_id,him.id as item_menu_id,himd.item_id,himc.class as item_class_id,himc.chanel");
		sql.append(" from hq_item_menu_class himc");
		sql.append(" left join hq_item_menu_details himd on himc.tenancy_id=himd.tenancy_id and himc.details_id=himd.id");
		sql.append(" left join hq_item_menu him on himd.tenancy_id=him.tenancy_id  and himd.item_menu_id=him.id");
		sql.append(" left join hq_item_menu_organ himo on him.tenancy_id=himo.tenancy_id and him.id=himo.item_menu_id");
		sql.append(" where himo.tenancy_id=? and himo.store_id=? and him.valid_state='1' and himd.valid_state='1' and himc.chanel=?) him");
		sql.append(" left join hq_item_class hic on him.tenancy_id=hic.tenancy_id and hic.id=him.item_class_id  ");
		sql.append(" left join hq_item_menu_classorder himco on hic.id=himco.class_id and him.item_menu_id=himco.menu_id");
		sql.append(" group by hic.id,hic.itemclass_name,himco.classorder,hic.itemclass_code,hic.father_id,him.item_menu_id");
		sql.append(" union select hic.id,hic.itemclass_name,himo.classorder,hic.itemclass_code,hic.father_id,item_class.item_menu_id,'0' item_count from hq_item_class hic");
		sql.append(" inner join item_class on hic.id=item_class.father_id ");
		sql.append(" left join hq_item_menu_classorder himo on hic.id=himo.class_id and item_class.item_menu_id=himo.menu_id");
		sql.append(") select * from item_class order by item_class.classorder ");
		List<JSONObject> query4Json = this.query4Json(tenancyId, sql.toString(), new Object[]
		{ tenancyId, storeId, chanel });
		return query4Json;
	}

	@Override
	public List<JSONObject> getItemInfos(String tenancy_id, int store_id, String chanel) throws Exception
	{
		StringBuilder sql = new StringBuilder();
		sql.append("  select distinct hii.id as dishid, hii.item_name as dish_name, hii.item_code as dishno, '' as describ ");
		sql.append("  , COALESCE(hii.item_description, '') as info, (case when hii.is_combo='Y' then 2 else 1 end) as type ");
		sql.append("  , (case when hii.is_assemble_combo='1' then 2 else ((case when himd.is_show='1' then 1 else 2 end)) end) as is_display, himc.class as kindid ");
		sql.append("  , hic.father_id as pkindid, COALESCE(hii.photo1, '') as icon, COALESCE(hii.photo1, '') as image ");
		sql.append("  , COALESCE(hii.photo1, '') as dishimg, 1 as min_unit, 1 as min_count, 1 as min_reduce ");
		sql.append("  , (case when hii.is_runningprice='Y' then hiu.unit_name else '' end) as price_name ");
		sql.append("  , (case when hii.is_runningprice='Y' then 1 else 0 end) as is_weigh ");
		sql.append("  , himcc.classorder,CAST (himd.menu_item_rank AS int4) as menu_item_rank");
		sql.append("  from hq_item_menu_organ himo  ");
		sql.append("  join hq_item_menu him on (him.id=himo.item_menu_id and him.valid_state='1')   ");
		sql.append("  join hq_item_menu_details himd on (himd.item_menu_id=him.id and himd.valid_state='1')   ");
		sql.append("  join hq_item_menu_class himc on himc.details_id=himd.id and himc.chanel='" + chanel + "'  ");
		sql.append("  join hq_item_menu_classorder himcc on him.id=himcc.menu_id and himc.class=himcc.class_id  ");
		sql.append("  join hq_item_class hic on hic.id=himc.class and hic.chanel=himc.chanel and hic.valid_state='1'   ");
		sql.append("  join hq_item_info hii on (hii.id=himd.item_id and hii.valid_state='1')  ");
		sql.append("  join hq_item_unit hiu on (hiu.item_id=hii.id and hiu.valid_state='1' and hiu.is_default='Y') ");
		sql.append("  where himo.store_id=" + store_id + " and himo.tenancy_id='" + tenancy_id + "'");
		sql.append("  order by himcc.classorder,cast(himd.menu_item_rank as int4) ");
		return this.query4Json(tenancy_id, sql.toString());
	}

	@Override
	public List<JSONObject> getItemInfosForCombo(String tenancyId, int storeId, int[] comboIds, int[] itemIds, String chanel) throws Exception
	{
		if (null == comboIds || 0 == comboIds.length)
		{
			return null;
		}

		StringBuilder sql = new StringBuilder();
		
		sql.append("  select distinct hii.id as dishid, hii.item_name as dish_name, hii.item_code as dishno, '' as describ ");
		sql.append("  , coalesce(hii.item_description, '') as info, (case when hii.is_combo='Y' then 2 else 1 end) as type ");
		sql.append("  , 2 as is_display, cl.class as kindid ");
		sql.append("  , cl.father_id as pkindid, coalesce(hii.photo1, '') as icon, coalesce(hii.photo1, '') as image");
		sql.append("  , coalesce(hii.photo1, '') as dishimg, 1 as min_unit,1 as min_count,1 as min_reduce ");
		sql.append("  , (case when hii.is_runningprice='Y' then hiu.unit_name else '' end) as price_name");
		sql.append("  , (case when hii.is_runningprice='Y' then 1 else 0 end) as is_weigh");
		sql.append("  , cl.classorder,cl.menu_item_rank");
		sql.append(" from (select cd.iitem_id,(case when cd.is_itemgroup='Y' then gd.item_id else cd.details_id end) item_id from hq_item_combo_details cd ");
		sql.append(" left join hq_item_group_details gd on cd.details_id = gd.item_group_id and cd.is_itemgroup='Y'");
		sql.append(" where cd.iitem_id in (").append(SqlUtil.getInnerStr(comboIds)).append(")) cd ");
		sql.append(" join hq_item_info hii on (hii.id=cd.item_id and hii.valid_state='1') ");
		sql.append(" left join hq_item_unit hiu on (hiu.item_id=hii.id and hiu.valid_state='1' and hiu.is_default='Y')");
		sql.append(" left join (select himd.item_id,himc.class,hic.father_id,himcc.classorder,cast(himd.menu_item_rank AS int4) as menu_item_rank");
		sql.append(" from hq_item_menu_organ himo");
		sql.append(" join hq_item_menu him on (him.id=himo.item_menu_id and him.valid_state='1') ");
		sql.append(" join hq_item_menu_details himd on (himd.item_menu_id=him.id and himd.valid_state='1')  ");
		sql.append(" join hq_item_menu_class himc on himc.details_id=himd.id and himc.chanel='").append(chanel).append("'");
		sql.append(" join hq_item_menu_classorder himcc on him.id=himcc.menu_id and himc.class=himcc.class_id ");
		sql.append(" join hq_item_class hic on hic.id=himc.class and hic.chanel=himc.chanel and hic.valid_state='1'");
		sql.append(" where himo.store_id=").append(storeId).append(" and himo.tenancy_id='").append(tenancyId).append("' ) cl on cl.item_id=cd.iitem_id ");
		sql.append(" where  hii.tenancy_id='").append(tenancyId).append("' ");

		if (null != itemIds && 0 < itemIds.length)
		{
			sql.append(" and hii.id not in (").append(SqlUtil.getInnerStr(itemIds)).append(")");
		}
		sql.append(" order by cl.classorder,cl.menu_item_rank");

		return this.query4Json(tenancyId, sql.toString());
	}
	
	@Override
	public List<JSONObject> getItemInfosForCombo(String tenancy_id, int store_id, String chanel) throws Exception
	{
		StringBuilder sql = new StringBuilder();
		sql.append("select distinct item_id as dishid,item_name as dish_name,item_code as dishno,'' as describ,item_description as info,photo1 as icon,photo1 as image,photo1 as dishimg, ");
		sql.append("is_combo as type,is_display,item_class as kindid,father_class_id as pkindid,1 as min_unit,1 as min_count,1 as min_reduce,price_name,is_weigh,classorder, menu_item_rank ");
		sql.append("from ( ");
		sql.append("with menu as( ");
		sql.append("	select himo.tenancy_id,himo.store_id,him.id item_menu_id,himd.item_id,himd.menu_item_rank,himd.is_show,himc.class as class_id,himc.chanel,himcc.classorder ");
		sql.append("	from hq_item_menu_organ himo ");
		sql.append("	join hq_item_menu him on him.tenancy_id=himo.tenancy_id and him.id=himo.item_menu_id and him.valid_state='1'  ");
		sql.append("	join hq_item_menu_details himd on him.tenancy_id=himo.tenancy_id and himd.item_menu_id=him.id and himd.valid_state='1'   ");
		sql.append("	join hq_item_menu_class himc on him.tenancy_id=himo.tenancy_id and himc.details_id=himd.id ");
		sql.append("	join hq_item_menu_classorder himcc on him.tenancy_id=himo.tenancy_id and him.id=himcc.menu_id and himc.class=himcc.class_id  ");
		sql.append("	where himo.tenancy_id=? and himo.store_id=? and himc.chanel=? ");
		sql.append("), ");
		sql.append("combo as( ");
		sql.append("	select hcd.tenancy_id,hcd.iitem_id,hcd.is_itemgroup,(case when hcd.is_itemgroup='Y' then hgd.item_id else hcd.details_id end) as item_id ");
		sql.append("	from menu ");
		sql.append("	inner join hq_item_info hi on menu.tenancy_id=hi.tenancy_id and menu.item_id=hi.id and hi.is_combo='Y' ");
		sql.append("	inner join hq_item_combo_details hcd on hi.tenancy_id=hcd.tenancy_id and hi.id=hcd.iitem_id and hcd.valid_state='1' ");
		sql.append("	left join hq_item_group hig on hcd.tenancy_id=hig.tenancy_id and hcd.is_itemgroup='Y' and hcd.details_id=hig.id and hig.valid_state='1' ");
		sql.append("	left join hq_item_group_details hgd on hgd.tenancy_id=hcd.tenancy_id and hig.id=hgd.item_group_id ");
		sql.append(") ");
		sql.append("select hii.id as item_id, hii.item_name, hii.item_code, COALESCE(hii.item_description, '') as item_description, COALESCE(hii.photo1, '') as photo1, ");
		sql.append("(case when hii.is_runningprice='Y' then hiu.unit_name else '' end) as price_name, (case when hii.is_runningprice='Y' then 1 else 0 end) as is_weigh, ");
		sql.append("(case when hii.is_combo='Y' then 2 else 1 end) as is_combo, (case when (hii.is_assemble_combo='1' or menu.is_show<>'1' ) then 2 else 1 end) as is_display, ");
		sql.append("menu.class_id as item_class, hic.father_id as father_class_id, menu.classorder,CAST(menu.menu_item_rank AS int4) as menu_item_rank ");
		sql.append("from hq_item_info hii ");
		sql.append("left join menu on hii.tenancy_id=menu.tenancy_id and hii.id=menu.item_id ");
		sql.append("left join hq_item_class hic on hic.id=menu.class_id and hic.chanel=menu.chanel and hic.valid_state='1' ");
		sql.append("left join hq_item_unit hiu on hiu.item_id=hii.id and hiu.valid_state='1' and hiu.is_default='Y' ");
		sql.append("where hii.valid_state='1' and menu.item_id is not null ");
		sql.append("union all ");
		sql.append("select hii.id as item_id, hii.item_name, hii.item_code,COALESCE(hii.item_description, '') as item_description,COALESCE(hii.photo1, '') as photo1, ");
		sql.append("(case when hii.is_runningprice='Y' then hiu.unit_name else '' end) as price_name,(case when hii.is_runningprice='Y' then 1 else 0 end) as is_weigh, ");
		sql.append("(case when hii.is_combo='Y' then 2 else 1 end) as is_combo,2 as is_display, hii.item_class as kindid,hic.father_id as father_class_id,999 classorder,999 as menu_item_rank ");
		sql.append("from hq_item_info hii ");
		sql.append("left join menu on hii.tenancy_id=menu.tenancy_id and hii.id=menu.item_id ");
		sql.append("left join combo on hii.tenancy_id=combo.tenancy_id and hii.id=combo.item_id ");
		sql.append("left join hq_item_class hic on hic.id=hii.item_class and hic.chanel='").append(SysDictionary.CHANEL_MD01).append("' and hic.valid_state='1' ");
		sql.append("left join hq_item_unit hiu on hiu.item_id=hii.id and hiu.valid_state='1' and hiu.is_default='Y' ");
		sql.append("where hii.valid_state='1' and menu.item_id is null and combo.item_id is not null ");
		sql.append(") it order by classorder,menu_item_rank ");

		return this.query4Json(tenancy_id, sql.toString(), new Object[]
		{ tenancy_id, store_id, chanel });
	}

	@Override
	public List<JSONObject> getUnitInfos(String tenancyId, int[] itemIds, String chanel, String priceSystem) throws Exception
	{
		if (itemIds == null || itemIds.length == 0)
		{
			return new ArrayList<JSONObject>();
		}
		StringBuilder sql = new StringBuilder();
		sql.append(" select hiu.item_id, hiu.id as unitid, hiu.unit_name as unit_name");
		sql.append(" , (case when hiu.is_default='Y' then 1 else 0 end) as is_default");
		sql.append(" , COALESCE(hip.price, hiu.standard_price) as price, COALESCE(hip.price, hiu.standard_price) as bargain_price");
		sql.append(" , COALESCE(COALESCE(civs.vip_price, hip.price), hiu.standard_price) as member_price");
		sql.append(" , 1 as min_unit, 0 as limit_count");
		sql.append(" from hq_item_unit hiu  ");
		sql.append(" left join hq_item_pricesystem hip on (hip.item_unit_id=hiu.id and hip.chanel='" + chanel + "' and hip.price_system='" + priceSystem + "') ");
		sql.append(" left join crm_item_vip_sysprice civs on (civs.unit_id=hiu.id and civs.chanel='" + chanel + "' and civs.price_system='" + priceSystem + "') ");
		sql.append(" where hiu.item_id in (" + SqlUtil.getInnerStr(itemIds) + ") and hiu.valid_state='1' ");
		sql.append(" and hiu.tenancy_id='" + tenancyId + "' ");
		return this.query4Json(tenancyId, sql.toString());
	}

	@Override
	public List<JSONObject> getMethodInfos(String tenancy_id, int storeId, int[] item_ids, String chanel) throws Exception
	{
		if (item_ids == null || item_ids.length == 0)
		{
			return new ArrayList<JSONObject>();
		}
		StringBuilder sql = new StringBuilder();
		sql.append(" select DISTINCT him.item_id, him.id as cookid, sd.class_item as cook_name,hiu.id unit_id,'' as icon,");
		sql.append(" (case when him.makeup_way = 'ADD' then him.proportion_money when him.makeup_way = 'MULTI' and 0 < COALESCE(him.proportion_money,0) then him.proportion_money * itp.price ELSE 0 end) as aprice ");
		sql.append(" from hq_item_method him ");
		sql.append(" left join sys_dictionary sd on (sd.id=him.method_name_id and sd.class_identifier_code='method') ");
		sql.append(" left join hq_item_unit hiu on hiu.item_id = him.item_id and hiu.is_default='Y'");
		sql.append(" left join hq_item_pricesystem itp on itp.item_unit_id = hiu.id and itp.chanel = '" + chanel + "'");
		sql.append(" left join organ o on itp.price_system::varchar = o.price_system ");
		sql.append(" where him.item_id in (" + SqlUtil.getInnerStr(item_ids) + ") ");
		sql.append(" and him.valid_state='1' ");
		sql.append(" and him.tenancy_id='" + tenancy_id + "' and o.id = " + storeId);
		return this.query4Json(tenancy_id, sql.toString());
	}

	@Override
	public List<JSONObject> getDishMemoList(String tenancyId, int storeId, int[] itemIds) throws Exception
	{
		StringBuilder sql = new StringBuilder();
		sql.append(" select hit.item_id,it.id as memoid, COALESCE(it.father_id, 0) as pmemoid, it.name as memo_name ");
		sql.append(" from hq_item_taste_details hit ");
		sql.append(" left join item_taste it on hit.taste_id=it.id ");
		sql.append(" left join item_taste_org ito on it.tenancy_id=ito.tenancy_id and it.id=ito.teste_id");
		sql.append(" where ito.tenancy_id='" + tenancyId + "' and ito.store_id='" + storeId + "'");
		sql.append(" and hit.item_id in (" + SqlUtil.getInnerStr(itemIds) + ") ");
		sql.append(" and it.valid_state='1' and (it.father_id is not null and it.father_id<>'0') ");
		sql.append(" order by it.code ");
		return this.query4Json(tenancyId, sql.toString());
	}
	
	@Override
	public List<JSONObject> getComboBaseInfo(String tenancyId, int[] combo_item_ids, String chanel, String priceSystem) throws Exception
	{
		if (combo_item_ids == null || combo_item_ids.length == 0)
		{
			return new ArrayList<JSONObject>();
		}
		
		StringBuilder sql = new StringBuilder();
		sql.append(" select hiu.item_id, i.item_name as setmeal_name");
		sql.append(" , hiu.id as unitid, hiu.unit_name as unit_name, COALESCE(hip.price, hiu.standard_price) as price, COALESCE(hip.price, hiu.standard_price) as bargain_price");
		sql.append(" , COALESCE(COALESCE(civs.vip_price, hip.price), hiu.standard_price) as member_price");
		sql.append(" , 1 as min_unit, 0 as limit_count");
		sql.append(" from hq_item_unit hiu  ");
		sql.append(" left join hq_item_info i on hiu.item_id = i.id ");
		sql.append(" left join hq_item_pricesystem hip on (hip.item_unit_id=hiu.id and hip.chanel='" + chanel + "' and hip.price_system='" + priceSystem + "') ");
		sql.append(" left join crm_item_vip_sysprice civs on (civs.unit_id=hiu.id and civs.chanel='" + chanel + "' and civs.price_system='" + priceSystem + "') ");
		sql.append(" where hiu.item_id in (" + SqlUtil.getInnerStr(combo_item_ids) + ") and hiu.valid_state='1' and hiu.is_default='Y'");
		sql.append(" and hiu.tenancy_id='" + tenancyId + "' ");
		return this.query4Json(tenancyId, sql.toString());
	}

	@Override
	public List<JSONObject> getMandatoryBaseInfo(String tenancy_id, int[] mandatoryCombIds) throws Exception
	{
		if (mandatoryCombIds == null || mandatoryCombIds.length == 0)
		{
			return new ArrayList<JSONObject>();
		}
		StringBuilder sql = new StringBuilder();
		sql.append(" select hicd.id as combo_id,hig.id as rpdid, hig.item_group_name as title ");
		sql.append(" , hicd.combo_num as selnum");
		sql.append(" from hq_item_combo_details hicd ");
		sql.append(" join hq_item_group hig on hig.id=hicd.details_id and hig.valid_state='1' ");
		sql.append(" where hicd.id in (" + SqlUtil.getInnerStr(mandatoryCombIds) + ") ");
		sql.append(" and hicd.valid_state='1' ");
		sql.append(" and hicd.tenancy_id='" + tenancy_id + "' ");
		return this.query4Json(tenancy_id, sql.toString());
	}

	@Override
	public List<JSONObject> getGroupDetails(String tenancy_id, int[] groupIds) throws Exception
	{
		if (groupIds == null || groupIds.length == 0)
		{
			return new ArrayList<JSONObject>();
		}
		StringBuilder sql = new StringBuilder();
		sql.append(" select DISTINCT higd.item_id as dishid, hii.item_code as dishno, hii.item_name as dish_name ");
		sql.append(" , higd.item_unit_id as unitid, higd.quantity_limit as maxnum, higd.makeup_money as aprice ");
		sql.append(" , higd.item_group_id as rpdid,(case when higd.isdefault='Y' then '1' else '0' end) as is_default ");
		sql.append(" from hq_item_group_details higd ");
		sql.append(" join hq_item_group hig on hig.id=higd.item_group_id and hig.valid_state='1' ");
		sql.append(" join hq_item_combo_details hicd on hicd.details_id=hig.id and hicd.is_itemgroup='Y' ");
		sql.append(" join hq_item_info hii on hii.id=higd.item_id and hii.valid_state='1'  ");
		sql.append(" where higd.item_group_id in (" + SqlUtil.getInnerStr(groupIds) + ") ");
		sql.append(" and higd.tenancy_id='" + tenancy_id + "' ");
		return this.query4Json(tenancy_id, sql.toString());
	}

	@Override
	public List<JSONObject> selectDetailsInfo(String tenancy_id, int[] combo_item_ids) throws Exception
	{
		if (combo_item_ids == null || combo_item_ids.length == 0)
		{
			return new ArrayList<JSONObject>();
		}
		StringBuilder sql = new StringBuilder();
		sql.append(" select hii.item_name as dish_name, hicd.combo_num as number, hicd.is_itemgroup ");
		sql.append(" , hicd.details_id as dishid, hicd.item_unit_id as unitid, hii.item_code as dishno ");
		sql.append(" , hicd.iitem_id as item_id, hicd.id as combo_id ");
		sql.append(" from hq_item_combo_details hicd ");
		sql.append(" left join hq_item_info hii on hii.id=hicd.details_id and hii.valid_state='1' ");
		sql.append(" where hicd.iitem_id in (" + SqlUtil.getInnerStr(combo_item_ids) + ") ");
		sql.append(" and hicd.valid_state='1' ");
		sql.append(" and hicd.tenancy_id='" + tenancy_id + "' order by hicd.iitem_id,hicd.combo_order,hicd.id");
		return this.query4Json(tenancy_id, sql.toString());
	}
	
	@Override
	public List<JSONObject> getItemInfoList(String tenancy_id, List<Integer> itemList) throws Exception
	{
		if (null == itemList || 0 == itemList.size())
		{
			return null;
		}

		StringBuilder sql = new StringBuilder();
		sql.append(" select i.id as item_id,i.item_code,u.id as item_unit_id from hq_item_info i");
		sql.append(" left join hq_item_unit u on u.item_id = i.id and u.valid_state = '1' ");
		sql.append(" where i.tenancy_id = ? and i.id in(").append(SqlUtil.getInnerStr(itemList)).append(")");

		return this.query4Json(tenancy_id, sql.toString(), new Object[]
		{ tenancy_id });
	}
}
