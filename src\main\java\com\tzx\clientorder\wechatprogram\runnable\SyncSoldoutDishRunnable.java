package com.tzx.clientorder.wechatprogram.runnable;

import java.util.List;

import org.apache.log4j.Logger;

import com.tzx.base.entity.PosSoldOut;
import com.tzx.clientorder.wechatprogram.bo.adapter.PromptServiceAdapter;
import com.tzx.clientorder.wechatprogram.common.util.PromptUtil;
import com.tzx.clientorder.wechatprogram.dao.PromptDao;
import com.tzx.framework.common.constant.SysParameterCode;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.exception.WxErrorCode;
import com.tzx.framework.common.util.CommonUtil;
import com.tzx.framework.common.util.SpringConext;
import com.tzx.pos.base.constant.SysDictionary;

public class SyncSoldoutDishRunnable implements Runnable
{
	private static final Logger	logger	= Logger.getLogger(SyncSoldoutDishRunnable.class);

	private String				tenancyId;
	private Integer				storeId;
	private List<PosSoldOut>	oldSoldList;
	private List<PosSoldOut>	soldList;
	private String				operMode;

	public SyncSoldoutDishRunnable(String tenancyId, Integer storeId, List<PosSoldOut> oldSoldList, List<PosSoldOut> soldList, String operMode)
	{
		super();
		this.tenancyId = tenancyId;
		this.storeId = storeId;
		this.oldSoldList = oldSoldList;
		this.soldList = soldList;
		this.operMode = operMode;// 0：设置沽清 1:取消沽清 2:修改估清
	}

	@Override
	public void run()
	{
		try
		{
			// 休眠500毫秒,等待数据提交完成;
			Thread.sleep(500);

			PromptDao dao = (PromptDao) SpringConext.getBean(tenancyId, PromptDao.NAME);
			String orderType = dao.getSysParameter(tenancyId, storeId, SysParameterCode.USER_ORDER_TYPE_KEY);
			if (CommonUtil.isNullOrEmpty(orderType) || "0".equals(orderType) || SysDictionary.USER_ORDER_TYPE_WLIFE_PROGRAM.equals(orderType) || SysDictionary.USER_ORDER_TYPE_WLIFE_H5.equals(orderType))
			{
				SystemException se = SystemException.getInstance(WxErrorCode.NOT_ENABLED_PROMPT_ORDER_ERROR);
				logger.info("小程序估清同步失败:" + se.getErrorMsg());
				return;
			}
			String channel = PromptUtil.getChannel(orderType);
			if (CommonUtil.isNullOrEmpty(channel))
			{
				SystemException se = SystemException.getInstance(WxErrorCode.ORDER_NOT_SPECIFIED_CHANNEL_ERROR);
				logger.info("小程序估清同步失败:" + se.getErrorMsg());
				return;
			}
			PromptServiceAdapter adapter = new PromptServiceAdapter(channel);
			if ("0".equals(operMode) || "2".equals(operMode))
			{
				adapter.syncSoldoutData(tenancyId, storeId, soldList);
			}
			else if ("1".equals(operMode))
			{
				adapter.cancelSoldoutData(tenancyId, storeId, oldSoldList);
			}
		}
		catch (Exception e)
		{
			logger.info("估清同步失败:", e);
		}
	}

	public void setTenancyId(String tenancyId)
	{
		this.tenancyId = tenancyId;
	}

	public void setStoreId(Integer storeId)
	{
		this.storeId = storeId;
	}

	public void setOldSoldList(List<PosSoldOut> oldSoldList)
	{
		this.oldSoldList = oldSoldList;
	}

	public void setSoldList(List<PosSoldOut> soldList)
	{
		this.soldList = soldList;
	}

	public void setOperMode(String operMode)
	{
		this.operMode = operMode;
	}
}
