package com.tzx.clientorder.wechatprogram.bo;

import java.util.List;

import com.tzx.base.entity.PosSoldOut;

import com.tzx.framework.common.entity.Data;
import net.sf.json.JSONObject;

/**
 * 调用微生活的接口 Created by qingui on 2018-07-24.
 */
public interface PromptService
{
	String QIMAI_NAME = "com.tzx.clientorder.wechatprogram.bo.impl.QimaiPromptServiceImp";

	/**
	 * 同步菜品基础数据接口
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @throws Exception
	 */
	public void syncDishData(String tenancyId, int storeId) throws Exception;

	/**
	 * 同步估清数据
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @throws Exception
	 */
	public JSONObject syncSoldoutData(String tenancyId, int storeId, List<PosSoldOut> soldList) throws Exception;

	/**取消估清同步
	 * @param tenancyId
	 * @param storeId
	 * @param soldList
	 * @return
	 * @throws Exception
	 */
	public JSONObject cancelSoldoutData(String tenancyId, int storeId, List<PosSoldOut> soldList) throws Exception;

	/**
	 * 门店退单
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param out_order_id
	 * @param orderSource
	 * @return
	 * @throws Exception
	 */
	public JSONObject orderRefund(String tenancyId, int storeId, String billNum, String orderNum, String tableCode, String orderSource) throws Exception;

	/**
	 * 结账通知
	 * 
	 * @param storeId
	 * @param billNum
	 * @return
	 * @throws Exception
	 */
	public JSONObject completeOrder(String tenancyId, int storeId, String billNum) throws Exception;

	/**
	 * 上传订单
	 * 
	 * @param tenancyId
	 * @param storeId
	 * @param billNum
	 * @return
	 * @throws Exception
	 */

	public JSONObject uploadOrder(String tenancyId, int storeId, String billNum,int operateType,String sTable,String rTable, String sBillNum) throws Exception;

	/**
	 * 锁定订单
	 * 
	 * @param billNum
	 * @return
	 * @throws Exception
	 */
	public JSONObject lockOrder(String tenancyId, int storeId, String billNum) throws Exception;

	/**
	 * 解锁订单
	 * 
	 * @param billNum
	 * @return
	 * @throws Exception
	 */
	public JSONObject unlockOrder(String tenancyId, int storeId, String billNum) throws Exception;

	/**
	 * 修改付款通知
	 *
	 * @param storeId
	 * @param billNum
	 * @return
	 * @throws Exception
	 */
	public JSONObject modifyPayOrder(String tenancyId, int storeId, String billNum) throws Exception;

}
