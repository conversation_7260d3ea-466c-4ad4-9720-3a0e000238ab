package com.tzx.base.service.servlet;

import java.util.Map;

import net.sf.json.JSONObject;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import com.tzx.base.common.util.UnicodeUtil;
import com.tzx.base.scoket.acewill.ErrorMesage;
import com.tzx.base.scoket.acewill.WebSocketClientUtil;
import com.tzx.clientorder.acewillwechat.service.servlet.SocketMessageProcessor;
import com.tzx.framework.common.constant.Constant;
import com.tzx.framework.common.exception.SystemException;
import com.tzx.framework.common.util.SpringConext;

public class AcewillWebScoketMessage {

	private static final Logger LOGGER = LoggerFactory.getLogger(AcewillWebScoketMessage.class);
	
	public void doExecute(String webScoketMessage) {
		LOGGER.info("============================================>收到消息：{}",webScoketMessage);
		
		webScoketMessage = UnicodeUtil.unicodeToString(webScoketMessage);
		JSONObject getJson = JSONObject.fromObject(webScoketMessage);
		Map<String, String> systemMap = Constant.getSystemMap();
		if (!systemMap.containsKey("tenent_id") || !systemMap.containsKey("store_id")
				|| "".equals(systemMap.get("tenent_id")) || "".equals(systemMap.get("store_id"))) {
			LOGGER.info("参数为空");
			return;
		}

		String tenancyId = systemMap.get("tenent_id");
		Integer storeId =Integer.parseInt(systemMap.get("store_id"));

		String action = getJson.optString("action"); // 处理方式
		SocketMessageProcessor processor = (SocketMessageProcessor) SpringConext.getBean(action);
		if(processor!=null) {
			JSONObject returnMessage = null;
			try {
				returnMessage = processor.process(tenancyId, storeId, getJson);
			} catch (Exception e) {
				//e.printStackTrace();
				String errorInfo = "未知错误";
				if(e instanceof SystemException) {
					errorInfo = ((SystemException) e).getErrorInfo();
					//判断是否为JSON
					JSONObject errorJson = isJsonString(errorInfo);
					if(errorJson!=null) {
						margeMessageHeader(getJson, errorJson);
						WebSocketClientUtil.send(errorJson.toString());
						return;
					}
				}
				JSONObject errorJosn = JSONObject.fromObject(new ErrorMesage(errorInfo));
				WebSocketClientUtil.send(errorJosn.toString());
				LOGGER.error(errorInfo);
				return;
			}
			if(returnMessage!=null&&!StringUtils.isEmpty(returnMessage.toString())) {
				//获取头信息并进行合并
				margeMessageHeader(getJson, returnMessage);
				
				WebSocketClientUtil.send(returnMessage.toString());
				LOGGER.info("返回消息：{}发送成功",returnMessage);
			}
		}else{
			LOGGER.error("action为[{}]接口未上线",action);
		}
	}
	private JSONObject isJsonString(String string) {
		try {
			return JSONObject.fromObject(string);
		}catch(Exception e) {
			return null;
		}
	}
	
	
	private void margeMessageHeader(JSONObject getJson, JSONObject returnMessage) {
		if(returnMessage.get("id")==null) {
			returnMessage.put("id", getJson.get("id"));
		}
		if(returnMessage.get("msgType")==null) {
			returnMessage.put("msgType", getJson.get("msgType"));
		}
		if(returnMessage.get("action")==null) {
			returnMessage.put("action", getJson.get("action"));
		}
		returnMessage.put("from", getJson.get("to"));
		returnMessage.put("to", getJson.get("from"));
		if(returnMessage.get("type")==null) {
			returnMessage.put("type", getJson.get("type"));
		}
	}
}
